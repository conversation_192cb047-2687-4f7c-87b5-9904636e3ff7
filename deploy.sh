output1=$(aws s3 sync ./dist s3://repd.us --region us-east-1 --acl public-read --exclude .DS_Store)

if [ $? -eq 0 ]; then
    echo "Successfully synced dist with s3://repd.us"
else
    echo "$output1"
    echo "Failed to sync dist with s3://repd.us"
    exit 1
fi

output2=$(aws cloudfront create-invalidation --distribution-id E1OY3SL50TBY4V --paths "/*" --region us-east-1)

if [ $? -eq 0 ]; then
    echo "Successfully invalidated distribution"
else
    echo "$output2"
    echo "Failed to invalidate distribution"
    exit 1
fi
