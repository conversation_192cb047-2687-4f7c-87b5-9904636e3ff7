<!DOCTYPE html>
<html lang="en">

<head>
  <meta property="og:url" content="https://api.repd.us/og" />

  <link rel="icon" href="favicon.ico" />
  <link rel="apple-touch-icon-precomposed" sizes="144x144"
    href="https://www.repd.us/favicon/apple-touch-icon-144x144.png" />
  <link rel="apple-touch-icon-precomposed" sizes="152x152"
    href="https://www.repd.us/favicon/apple-touch-icon-152x152.png" />
  <link rel="icon" type="image/png" href="https://www.repd.us/favicon/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="https://www.repd.us/favicon/favicon-16x16.png" sizes="16x16" />

  <meta name="msapplication-TileImage" content="https://www.repd.us/favicon/mstile-144x144.png" />
  <meta name="application-name" content="Rep'd" />
  <meta name="msapplication-TileColor" content="#FFFFFF" />
  <meta name="msapplication-TileImage" content="https://www.repd.us/favicon/mstile-144x144.png" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />

  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,wght@0,200;0,300;0,400;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet" />
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
  <meta name="theme-color" content="#000000" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>Rep'd, Inc.</title>
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->

  <!-- Analytics START -->
  <!-- <script>
    window.fbAsyncInit = function () {
      FB.init({
        appId: '****************',
        cookie: true,
        xfbml: true,
        version: 'v7.0'
      });

      // FB.AppEvents.logPageView();
    };
    (function (d, s, id) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) {
        return;
      }
      js = d.createElement(s);
      js.id = id;
      js.src = 'https://connect.facebook.net/en_US/sdk.js';
      fjs.parentNode.insertBefore(js, fjs);
    })(document, 'script', 'facebook-jssdk');
  </script> -->

  <!-- <div id="fb-root"></div> -->
  <!-- <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v7.0&appId=****************&autoLogAppEvents=1" nonce="NL2roubC"></script> -->

  <!-- Facebook Pixel Code -->
  <!-- <script>
    !(function (f, b, e, v, n, t, s) {
      if (f.fbq) return;
      n = f.fbq = function () {
        n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s);
    })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '****************');
    fbq('track', 'PageView');
  </script> -->
  <!-- <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=****************&ev=PageView&noscript=1" /></noscript> -->
  <!-- End Facebook Pixel Code -->
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script type="text/javascript">
    var ssaUrl = 'https://' + 'pixel.sitescout.com/iap/eb8dd8683be5f782';
    new Image().src = ssaUrl;
    (function (d) {
      var syncUrl = 'https://' + 'pixel.sitescout.com/dmp/asyncPixelSync';
      var iframe = d.createElement('iframe');
      (iframe.frameElement || iframe).style.cssText = 'width: 0; height: 0; border: 0;';
      iframe.src = 'javascript:false';
      d.body.appendChild(iframe);
      var doc = iframe.contentWindow.document;
      doc.open().write('<body onload="window.location.href=\'' + syncUrl + '\'">');
      doc.close();
    })(document);
  </script>

  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=UA-169016212-1"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', 'UA-169016212-1', {
      app_name: 'WebApp',
      custom_map: {
        dimension1: 'userId',
        dimension2: 'questionId',
        dimension3: 'answerId',
        dimension4: 'questionText',

        dimension5: 'firstName',
        dimension6: 'lastName',
        dimension7: 'email',
        dimension8: 'imageUrl',
        dimension9: 'phone',
        dimension10: 'zip',
        dimension11: 'accessLevel',

        dimension12: 'videoFeedbackAmount',
        dimension13: 'category',
        dimension14: 'searchTerm',
        dimension15: 'clientId',
        dimension16: 'savedListId'
      }
    });
  </script>
  <!-- Analytics END -->
</body>

</html>