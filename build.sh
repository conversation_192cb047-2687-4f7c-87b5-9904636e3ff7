#!/bin/bash

watch="$1"

compile_assets () {
  cp -r src/assets dist/
}

compile_js () {
  cp node_modules/jquery/dist/jquery.min.js dist/jquery.js
    cp node_modules/lodash/lodash.min.js dist/lodash.js

  files=( $(cat src/*.js | grep 'include' | perl -pe 's/ +|include|'"'"'|\(|\)|;//g' | cat ) )
  for (( i=1; i < (( ${#files[@]} + 1 )); i++ )); do
    file_name=${files[$i-1]}
    echo "$( cat src/$file_name.js )\n" >> dist/index.js

    echo "Copied: src/$file_name.js to dist/index.js"
  done
  cat src/*.js | perl -pe 's/^ *\/\/.+//g' | perl -pe 's/^ +//g' | perl -pe 's/\/\*[^\*]+\*\///g' | perl -pe 's/^include.+//g' >> dist/index.js # | perl -pe 's/\s+/ /g' | perl -pe 's/\s+/ /g'
}

compile_css () {
  cp node_modules/animate.css/animate.min.css dist/animate.css
  cat node_modules/@fortawesome/fontawesome-free/css/all.min.css | perl -pe 's/\.\.\///g' > dist/fontawesome.css
  cp -r node_modules/@fortawesome/fontawesome-free/webfonts dist/
  cat node_modules/@openfonts/nunito-sans_all/index.css | perl -pe 's/\.\///g' | perl -pe 's/\/\*.+//g' | perl -pe 's/\s+/ /g' | perl -pe "s/Nunito Sans'/Nunito'/g" > dist/nunito.css
  cp -r node_modules/@openfonts/nunito-sans_all/files dist/

  if [ "$watch" == '' ]; then
    stylus --compress --include-css --disable-cache --out dist/ src/*.styl
  else
    stylus --compress --include-css --disable-cache --out dist/ src/*.styl --watch src/*.styl
  fi

  npx postcss dist/*.css --use autoprefixer --replace
}

compile_html () {
  cp -r src/favicon dist/
  cp src/favicon/favicon.ico dist/

  if [ "$watch" == '' ]; then
    gulp # pug --output-style compressed --out dist/ src/*.pug
  else
    sleep 1
    gulp # pug --output-style compressed --out dist/ src/*.pug --watch src/*.pug
  fi
}

compile () {
  rm -r dist/*

  compile_assets

  if [ "$watch" == '' ]; then
    compile_js
    compile_css
    compile_html
  else
    compile_js & compile_css & compile_html
  fi
}

compile
