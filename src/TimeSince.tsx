import { useEffect, useState } from "react";

type Props = {
  isoDateTime: string;
};
export default function TimeSince({ isoDateTime }: Props) {
  const [seconds, setSeconds] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      const startTime = new Date(isoDateTime).getTime();
      const currentTime = Date.now();
      const timeElapsed = Math.floor((currentTime - startTime) / 1000);
      setSeconds(timeElapsed);
    }, 1000);

    return () => clearInterval(timer);
  }, [isoDateTime]);

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return (
    <div className="text-lg font-medium text-gray-700">
      {minutes > 0 ? `${minutes} min ${remainingSeconds} s` : `${seconds} s`}
    </div>
  );
}
