import { QuestionShareInterface } from './questionShares.interface';
import { UserInterface } from './user.interface';

export interface QuestionInterface {
    id: string;
    enabled: boolean;
    createdAt: string | Date;
    updatedAt: string | Date;
    clientId: string;
    text: string;
    suggestedNotes: string;
    category: string;
    // TODO: add type check for category icons it is limited list of strings
    categoryIcon: string;
    votes: number;
    user: UserInterface;
    isApproved: boolean;
    isDenied: boolean;
    isAnswered: boolean;
    isShared: boolean;
    // TODO: check if we receive here users?
    userVotes: UserInterface[] | any;
    asked: boolean;
    blockedForClient: boolean;
    shares: QuestionShareInterface[];
    overridingName?: string;
}
