import { QuestionInterface } from "./question.interface";

interface questionShares {
  id: string;
  enabled: boolean;
  updatedAt: string | Date;
  createdAt: string | Date;

  userId: string;
  clientId: number;
  answerId: string;
  questionId: string;

  email: string;
  isAnswered: boolean;
}

export interface AnswerInterface {
  id: string;
  enabled: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  endDate: string | Date;

  clientId: number;
  imageUrl: string;
  mp4VideoStatus: string;
  ogvVideoStatus: string;
  webmVideoStatus: string;
  videoUrl: string;
  videoUrls: {
    mp4: string;
    ogv: string;
    webm: string;
  };
  videoDuration?: number | null;
  subtitles?: string | null;
  subtitlesSpeed?: number | null;
  transcription?: any | null;
  showTranscribedSubtitles: boolean;

  votes: number;
  question: QuestionInterface;
  createdAtDateString: string;
  comments: any[];
  commented: boolean;
  userVotes: any[][];
  userLikes: any[][];
  likes: number;
  liked: boolean;
  sends: any;

  isDraft: boolean;
  isApproved: boolean;
  isDenied: boolean;
  isPinned: boolean;
  sentBy: string;
  questionShares: questionShares[];
}

export interface AnswerUpdateInterface {
  id: string;
  clientId: number;
  subtitles?: string | null;
  imageUrl?: string;
  videoUrl?: string;
  endDate?: string | Date | null;
  isPinned?: boolean;
}
