import { ClientInterface } from './client.interfaces';

export interface UserUpdateInterface {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    imageUrl?: string | null;
    zip?: string | null;
    password?: string | null;
    passwordConfirmation?: string | null;
    accessLevel?: string;
    telepromptSpeed?: "0.05" | "0.15" | "0.25" | "0.50" | "0.75" | "1.00" | "1.50" | "2.00";
}
export interface UserInterface {
    id: string;
    clientId: string;
    enabled: boolean;
    createdAt: string | Date;
    updatedAt: string | Date;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    imageUrl: string | null;
    zip: string;
    ipa: string;
    location: string;
    accessLevel: string;
    client?: ClientInterface;
    isMfaRequired: boolean;
    receiveQuestionNotifications: boolean;
    receiveAnswerNotifications: boolean;
    password?: string | null;
    passwordConfirmation?: string | null;
    telepromptSpeed: any;
}
