export * from './session.interface';
export * from './user.interface';
export * from './client.interfaces';
export * from './question.interface';
export * from './answers.interface';
export * from './stats.interface';
export * from './ngpvan.interface';
export * from './error.interface';

export type ResponseInterface<T> = {
    message: string;
    totalEntries?: number;
    page?: number;
    limit?: boolean;
    data: T[];
}
