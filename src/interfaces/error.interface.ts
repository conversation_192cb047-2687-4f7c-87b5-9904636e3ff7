import { UserInterface } from './user.interface';

export interface EndpointErrorInterface {
  id: string;
  createdAt: string | Date;
  updatedAt: string | Date;
  enabled: boolean;
  route: string;
  method: string;
  errorText: string;
  userId?: string;
  user?: UserInterface;
}

export interface EndpointErrorsResponse {
  message: string;
  totalEntries: number;
  page: number;
  limit: number;
  data: EndpointErrorInterface[];
}

export interface AdminStatEventInterface {
  id: string;
  createdAt: string | Date;
  updatedAt: string | Date;
  enabled: boolean;
  userId: string;
  clientId: string;
  eventAction: string;
  eventCategory: string;
  eventLabel: string;
  accessLevel: string;
  origin: string;
  host: string;
  ipa: string;
  userAgent?: string;
  screenSize?: string;
}

export interface AdminStatEventsResponse {
  message: string;
  totalEntries: number;
  page: number;
  limit: number;
  data: AdminStatEventInterface[];
}
