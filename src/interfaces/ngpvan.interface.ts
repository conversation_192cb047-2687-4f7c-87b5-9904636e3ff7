export interface NgpVanInterface {
  id: string;
  clientId: string;
  enabled: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  zip: string | null;
  location: string | null;
  savedListId: string;
  usersCount: string | null;
}

export interface ActivistCodeInterface {
  activistCodeId: string;
  type: string;
  name: string;
  mediumName: string | null;
  shortName: string | null;
  description: string | null;
  scriptQuestion: string | null;
  isMultiAssign: boolean;
  status: string | null;
  categories: string[];
}

export interface ActivistCodesInterface {
  warningCodes: string[];
  voterActivistCodes: ActivistCodeInterface[];
  campaignActivistCodes: ActivistCodeInterface[];
}

export interface ActivistCodeMatchInterface {
  codeId: string;
  code: string;
}

export interface SyncActivistCodeInterface {
  category: string;
  activistCodes: ActivistCodeMatchInterface[];
}
