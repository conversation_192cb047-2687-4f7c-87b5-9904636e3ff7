doctype html
html( lang="en" )
  head
    - var assetUrl = '/assets';
    - var year = new Date().getFullYear();
    - var path = path || '';

    include partials/_variables.pug

    //- Set og:url property based on path
    meta( property="og:url" content=`https://www.repd.us${path}` )
    meta( name="twitter:url" content=`https://www.repd.us${path}` )
    meta( property="og:site_name" content="Rep'd, Inc." )
    meta( property="og:type" content="website" )
    meta( name="twitter:site" content="@RepdUSA" )
    meta( name="twitter:creator" content="@RepdUSA" )
    meta( name="twitter:card" content="summary_large_image" )
    meta( property="twitter:domain" content="repd.us" )
    meta( name="twitter:description" content="" )
    meta( name="twitter:image" content="" )

    //- Custom meta tag for /emporia path
    if path === '/emporia'
      title Emporia
      meta( property="og:image" content="https://www.repd.us/assets/Emporia 2.jpg" )
      meta( name="twitter:image" content="https://www.repd.us/assets/Emporia 2.jpg" )
      meta( name="description" content="Emporia, Kansas. Get video answers to your questions." )
      meta( name="twitter:title" content="Emporia, Kansas" )
    else
      title Rep'd, Inc.
      //- meta( property="og:url" content=`https://api.repd.us/og`)
      meta( property="og:image" content="https://www.repd.us/assets/Preview 3.2.png" )
      meta( name="twitter:image" content="https://www.repd.us/assets/Preview 3.2.png" )
      meta( name="description" content="Humanizing Local Government. A video engagement platform for municipalities powered by AI." )
      meta( name="twitter:title" content="Rep'd, Inc." )

    link( rel="icon" href="favicon.ico" )
    link( rel="apple-touch-icon-precomposed" sizes="144x144" href="https://www.repd.us/favicon/apple-touch-icon-144x144.png" )
    link( rel="apple-touch-icon-precomposed" sizes="152x152" href="https://www.repd.us/favicon/apple-touch-icon-152x152.png" )
    link( rel="icon" type="image/png" href="https://www.repd.us/favicon/favicon-32x32.png" sizes="32x32" )
    link( rel="icon" type="image/png" href="https://www.repd.us/favicon/favicon-16x16.png" )

    //- Fonts
    link( rel="preconnect" href="https://fonts.googleapis.com" )
    link( rel="preconnect" href="https://fonts.gstatic.com" crossorigin )
    link( href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap" rel="stylesheet" )

    meta( name="application-name" content="Rep'd" )
    meta( name="msapplication-TileColor" content="#FFFFFF" )
    meta( name="msapplication-TileImage" content="https://www.repd.us/favicon/mstile-144x144.png" )

    style
      include ../dist/nunito.css
      include ../dist/fontawesome.css
      include ../dist/animate.css
      include ../dist/index.css

    script
      include ../dist/jquery.js
      include ../dist/lodash.js

    script
      include ../dist/index.js

    base( href="/" )
    meta( name="viewport" content="width=device-width, initial-scale=0.8, maximum-scale=1, minimum-scale=0.8, user-scalable=no" )
  body.hidden
    header
      .wrapper
        img.logo( src=assetUrl + "/required/logo/Logo-White.svg" alt="Logo" height="50px" )

        .menu.mobile-shown( data-function="showHideMenu()" style="background-image: url( " + assetUrl + "/required/icons/hamburger.svg )" )

        nav.link-group
          a( data-function="scrollToElement('.features')"     rel="nofollow" ) Product Features
          a( data-function="scrollToElement('.case-studies')" rel="nofollow" ) Case Studies
          a( data-function="scrollToElement('.press')"        rel="nofollow" ) Press
          a( data-function="scrollToElement('.about-us')"     rel="nofollow" ) About US

        button( data-function="scrollToElement()" ) SCHEDULE A DEMO

    main
      .alert-wrapper
        .alert.text-center.hidden

      include partials/home.pug

    footer
      img.logo( src=assetUrl + "/required/logo/Logo-White.svg" height="50px" )

      .link-group
        a( href=assetUrl + "/accessibility.pdf" target="_blank" rel="canonical external dofollow" )
          | Accessibility
        != '&bull;'
        a( href=assetUrl + "/terms.pdf" target="_blank" rel="canonical external dofollow" )
          | Terms of Service
        != '&bull;'
        a( href=assetUrl + "/privacy.pdf" target="_blank" rel="canonical external dofollow" )
          | Privacy Policy

      .copy
        != '&copy;'
        = year + " Rep'd"

  include ga.html
