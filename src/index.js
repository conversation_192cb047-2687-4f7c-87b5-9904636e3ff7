/**
 * This file is the entry point of the application.
 * It contains various functions and event handlers for the landing page.
 * The code initializes the necessary variables, sets up form inputs and buttons,
 * and handles scrolling and video playback.
 * It also has a contact form submission function and a function to switch between case studies.
 * @file FILEPATH: /Repd/Codebases/landing/src/index.js
 */
const API_URL = "https://api.repd.us";
// const API_URL = "localhost:3000";
var isMobile = false;
const headerAlertDisplayTime = 3000;
var headerAlertBanner,
    headerAlertIsHidden = true,
    pageIsHidden = true

$( document ).ready( function () {
  isMobile = window.innerWidth < 768;

  setUpButtons();
  setUpFormInputs();
  playAllFeatureVideos();
  setupPressCarousel();

  headerAlertBanner = $( '.alert-wrapper .alert' );

  setTimeout( () => $( 'body' ).removeClass( 'hidden' ), 1000 )

  if ( location.pathname.length > 1 ) {
    const referrer = document.referrer;
    const referrerParam = referrer ? `${ location.search ? '&' : '?' }referrer=${encodeURIComponent(referrer)}` : '';

    $( 'body' ).css( { margin: 0 } ).html( `
      <div style="height: 100vh; width: 100%; position: relative;">
        <iframe src="https://app.repd.us${ location.pathname }${ location.search }${ referrerParam }" height="100%" width="100%" style="border: none" allow="clipboard-read; clipboard-write"></iframe>
      </div>
    ` );
  }

  // auto switch between case studies
  switchCaseStudy();
  // setInterval( () => switchCaseStudy(), 10000 );
  autoPlayCaseStudyVideos();

  // play feature videos when scrolling is finished
  $( window ).scroll( _.debounce( playVideoWhenScrolledTo, 500 ) );
} );

const showOrHideHeaderAlert = function ( text = "" ) {
  headerAlertBanner.text( text );
  headerAlertIsHidden = headerAlertBanner.attr( 'class' ).match( /hidden/ ) !== null;

  if ( headerAlertIsHidden && text !== "" )
    headerAlertBanner.removeClass( 'hidden' );
  else
    headerAlertBanner.addClass( 'hidden' );

  if ( text !== "" )
    setTimeout( function () { showOrHideHeaderAlert( "" ); }, headerAlertDisplayTime );
};

const setUpFormInputs = function () {
  _.forEach( $( 'input, textarea' ), function ( element, i ) {
    const input = $( element );
    var name = input.attr( 'name' ).toString();

    if ( localStorage[ name ] !== undefined )
      input.val( localStorage[ name ] );

    input.keyup ( function ( event ) {
      const element = $( event.currentTarget );
      const errorElement = element.parents( 'form' ).find( `.error.${ element.attr( 'name' ) }` );
      const regex = element.data( 'regex' );
      const name = element.attr( 'name' );
      const value = element.val();
      localStorage[ name ] = value;

      var isInvalid = false;

      if ( regex !== undefined ) {
        const valueMatchesValidation = value.toString().match( eval( regex ) ) !== null;

        if ( !valueMatchesValidation && errorElement.length > 0 )
          isInvalid = true;
      };

      if ( isInvalid ) {
        errorElement.removeClass( 'hidden' );
        element.addClass( 'invalid' );
      } else {
        errorElement.addClass( 'hidden' );
        element.removeClass( 'invalid' );
      };
    } );
  } );
}

const setUpButtons = function () {
  _.forEach( $( '[data-function]' ), function ( element, i ) {
    $( element ).unbind( 'click' ).click( function ( event ) {
      const element = $( event.currentTarget );
      const callback = element.data( 'function' );

      eval( callback );
    } );
  } );
}

const scrollToElement = function ( pageSegmentId = '.footer-contact-form' ) {
  showHideMenu( true );

  const headerSize = $( 'header' ).height();
  var documentHeight = $( pageSegmentId ).offset().top - headerSize;

  const scrollFunction = function () {
    document.body.scrollTop = documentHeight; // For Safari
    document.documentElement.scrollTop = documentHeight; // For Chrome, Firefox, IE and Opera
  }

  // if ( isMobile )
  //   documentHeight = documentHeight + headerSize - 400;

  scrollFunction();
}

const showHideMenu = function ( forceMenuState = null ) {
  const menu = $( 'header .link-group' );
  const menuIsActive = (
    forceMenuState !== null ? forceMenuState : $( 'header .link-group.active' ).length > 0
  );

  if ( menuIsActive )
    menu.removeClass( 'active' );
  else
    menu.addClass( 'active' );
}

const pauseVideosWhenScrolling = function () {
  const videos = $( '.features .video video' );

  for (video of videos)
    video.pause()
}

const playAllFeatureVideos = function () {
  const videos = $( '.features .video video' );

  for (video of videos)
    video.play()
}

/**
 * Plays videos when scrolled to a certain position.
 */
const playVideoWhenScrolledTo = function () {
  const videos = $( '.features .video video' );
  const containerHeight = $( '.features .flex-container' ).first().height();

  // If mobile, play all videos, else pause all videos before playing the one in view
  if (isMobile) {
    for (video of videos)
      if (!video.paused)
        video.play()
    return
  } else {
    pauseVideosWhenScrolling()
  }

  for (video of videos) {
    const videoOffset = $(video).offset().top;
    const windowScrollY = window.scrollY;

    const videoIsVisible = (
      videoOffset > windowScrollY &&
      videoOffset < windowScrollY + containerHeight + 200
    );

    // if (!isMobile) pauseVideosWhenScrolling()
    if (videoIsVisible)
      video.play()
    // video.currentTime = 0.2;
  }
}

function switchCaseStudy( direction = 1 ) {
  // const caseStudies = $( '.case-study' );
  const currentCaseStudy = $( '.case-study.active' );

  var nextCaseStudy = direction === 1 ? currentCaseStudy.next() : currentCaseStudy.prev();

  if ( nextCaseStudy.length < 1 && direction === 1 )
    nextCaseStudy = $( '.case-study' ).first();
  if ( nextCaseStudy.length < 1 && direction === -1 )
    nextCaseStudy = $( '.case-study' ).last();

  if ( nextCaseStudy.length > 0 ) {
    currentCaseStudy.removeClass( 'active' );

    for (video of $('video'))
      video.volume = 0;

    nextCaseStudy.addClass( 'active' );
  }
}

function autoPlayCaseStudyVideos() {
  const videos = $( '.case-study.active video' );

  for (video of videos) {
    video.autoplay = true;

    // if (isMobile)
    //   video.controls = false;

    video.play();
  }
}

let currentPressIndex = 0;

function setupPressCarousel() {
  showPressSlide(currentPressIndex);

  const dots = document.querySelectorAll('.press-dot');

  dots.forEach((dot, i) => {
    dot.addEventListener('click', () => showPressSlide(i));
  });
}

function showPressSlide(index) {
  const items = document.querySelectorAll('.press-article-carousel-item');
  const totalItems = items.length;

  currentPressIndex = index;

  if (index >= totalItems) {
    currentPressIndex = 0;
  }
  if (index < 0) {
    currentPressIndex = totalItems - 1;
  }

  if (currentPressIndex == 0) {
    document.querySelector('.inner-press-wrapper').style.transform = `translateX(25px)`;
  } else {
    const windowWidth = window.innerWidth > 1300 ? 1300 : window.innerWidth;
    const offset = -windowWidth * currentPressIndex;

    let adjustment = 65 * currentPressIndex;

    if (currentPressIndex >= 2) {
      adjustment -= 25 * (currentPressIndex - 1);
    }

    document.querySelector('.inner-press-wrapper').style.transform = `translateX(${offset + adjustment}px)`;
  }

  // const offset = -currentPressIndex * 100;
  // document.querySelector('.inner-press-wrapper').style.transform = `translateX(${offset}%)`;

  const dots = document.querySelectorAll('.press-dot');

  dots.forEach((dot, i) => {
    dot.classList.toggle('active', i === currentPressIndex);
  });
}

function nextPressSlide() {
  showPressSlide(currentPressIndex + 1);
}

function prevPressSlide() {
  showPressSlide(currentPressIndex - 1);
}

var hasPlayedCoverVideo = false;

function playCoverVideo() {
  hasPlayedCoverVideo = true;
  const video = document.getElementById('cover-video');
  video.muted = false;
  video.volume = 0.5;
  video.play();
  const image = document.getElementById('cover-image')
  const image2 = document.getElementById('cover-image2')
  const playButton = document.getElementById('play-button')
  image.style.display = 'none';
  image2.style.display = 'none';
  playButton.style.display = 'none';
}

const submitContactForm = function ( event ) {
  event.preventDefault(); event.stopPropagation();

  if ( $( '.error.hidden' ).length < 1 )
    showOrHideHeaderAlert( "Please enter valid details." );
  else
    $.ajax( {
      url: `${ API_URL }/landing_page/contact_us`,
      method: 'POST',
      data: {
        name:    localStorage.contactName,
        email:   localStorage.contactEmail,
        message: localStorage.organization
      },
      success: function( result ) {
        localStorage.clear();
        $( 'input, textarea' ).val( '' );
        showOrHideHeaderAlert( "Your message has been sent!" );
      }
    } );
}
