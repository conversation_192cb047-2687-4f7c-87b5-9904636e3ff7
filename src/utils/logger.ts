/* eslint-disable no-nested-ternary */
import { datadogLogs } from '@datadog/browser-logs';

const { host } = window.location;
const env = host.includes('localhost')
  ? 'dev'
  : host.includes('staging')
    ? 'staging'
    : host.includes('mock')
      ? 'mock'
      : 'live';

datadogLogs.init({
  // TODO: move to ENV
  clientToken: process.env.REACT_APP_DATADOG_CLIENT_TOKEN || 'pub714f2806b9d0c4e20586d35799dbca69',
  site: 'datadoghq.com',
  env,
  sessionSampleRate: 100,
  forwardErrorsToLogs: true,
  beforeSend: () => env !== 'dev',
});

const loggerName =
  env === 'dev' || env === 'staging'
    ? `Repd-Admin-Staging`
    : env === 'mock'
      ? `Repd-Admin-Mock`
      : `Repd-Admin-Live`;

const logger = datadogLogs.createLogger(loggerName);

logger.info('Logger initialized');

let previousUserId: string | null = null;

const checkLocalStorageForUser = () => {
  const user = localStorage.getItem('user');
  const client = localStorage.getItem('client');
  const token = localStorage.getItem('token');

  if (user && client && token) {
    const userObj = JSON.parse(user);
    const clientObj = JSON.parse(client);
    const userId = userObj.id;
    const clientId = clientObj.id;
    const username = `${userObj.firstName} ${userObj.lastName}`;
    const clientName = clientObj.name;
    const userEmail = userObj.email;

    if (userId && userId !== previousUserId) {
      previousUserId = userId;

      logger.setContext({
        userId,
        clientId,
        username,
        clientName,
        userEmail,
        token,
      });

      logger.info('User info is set');
    }
  }
};

checkLocalStorageForUser();
setInterval(checkLocalStorageForUser, 5000); // Check every 5 seconds

export default logger;
