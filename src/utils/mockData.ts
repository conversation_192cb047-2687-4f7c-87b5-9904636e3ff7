import { useFilterStore } from "hooks/zustand/filterStore";

export const mockData = {
  totalTraffic: 108089,
  engagementRate: 0.98,
  timeSaved: 5698,
  socialSources: {
    facebook: 0.47,
    instagram: 0.23,
    twitter: 0.15,
    nextDoor: 0.05,
    search: 0.1
  },
  mobileUsage: 0.8,
  desktopUsage: 0.2,
  totalQueries: 253,
  categories: {
    homelessness: {
      sentiment: 0.87,
      volume: 0.58
    },
    transportation: {
      sentiment: 0.35,
      volume: 0.24
    },
    cityBudget: {
      sentiment: 0.76,
      volume: 0.12
    },
    infrastructure: {
      sentiment: 0.21,
      volume: 0.13
    },
    housing: {
      sentiment: 0.94,
      volume: 0.25
    },
    publicSafety: {
      sentiment: 0.67,
      volume: 0.09
    },
  },
  trendingQueries: 67,
  trendingQuestions: 26,
  trendingCategories: {
    homelessness: 0.87,
    cityBudget: 0.76,
    transportation: 0.35,
    publicSafety: 0.67,
    infrastructure: 0.21,
    housing: 0.94,
  },
  videoViews: 2150,
  watchtime: "2:24",
  watchRate: 0.25,
  deliveredEmails: 1089,
  openedEmails: 0.86,
  clickedEmails: 0.26,
}

export const getModifiedValue = (value: number) => {
  const filterState = useFilterStore.getState();
  const { locationFilters, timeFilter } = filterState;
  const filter = locationFilters[0] ? locationFilters[0].value : timeFilter?.value ? timeFilter.value : "100";
  const filterModifier = filter.charCodeAt(0);
  return Math.round(value * filterModifier / 100);
}

export const getModifiedPercentage = (percent: number, filter: string) => {
  const filterModifier = filter.charCodeAt(0);
  return Math.round(Number(Number(percent * filterModifier).toFixed(0)));
}

export const getPercentString = (percent: number) => {
  return `${percent}%`
}

export const getPercent = (percent: number) => {
  const filterState = useFilterStore.getState();
  const { locationFilters, timeFilter } = filterState;
  return getPercentString(getModifiedPercentage(percent, locationFilters[0] ? locationFilters[0].value : timeFilter?.value ? timeFilter.value : "100"))
}