import moment from "moment";
import * as interfaces from 'interfaces';

export const processChartData = (rawData: any[], primaryKey: string = 'opens', secondaryKey?: string) => {
  if (rawData.length === 0) return [];

  const sortedData = rawData.sort((a, b) => moment(a.created_at).diff(moment(b.created_at)));
  const firstEventDate = moment(sortedData[sortedData.length - 1].created_at);
  const lastEventDate = moment(sortedData[0].created_at);
  const isYearOrMore = lastEventDate.diff(firstEventDate, 'months') <= -6;
  const format = isYearOrMore ? 'YYYY-MM' : 'YYYY-MM-DD';

  const groupedData = sortedData.reduce((acc: any, event: any) => {
    const date = moment(event.created_at).format(format);

    if (!acc[date]) acc[date] = { [primaryKey]: 0, ...(secondaryKey ? { [secondaryKey]: 0 } : {}) };

    acc[date][primaryKey] += parseInt(event[primaryKey]);

    if (secondaryKey) acc[date][secondaryKey] += parseInt(event[secondaryKey]);

    return acc;
  }, {});

  return Object.keys(groupedData).map(date => ({
    date,
    data: groupedData[date][primaryKey],
    secondary: secondaryKey ? groupedData[date][secondaryKey] : undefined,
  }));
};
