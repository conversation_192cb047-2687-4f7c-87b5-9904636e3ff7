import { Parser } from "@json2csv/plainjs";
import { clsx, type ClassValue } from "clsx"
import moment from "moment";
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const countType = (array: any[], property: string, value: any) => {
  return array.reduce((acc, cur) => cur[property] === value ? ++acc : acc, 0);
}

export const filterToDateRange = (filter: string | undefined, userAccessLevel?: string) => {
  switch (filter) {
    case "today":
      return { start: moment().subtract(1, "days").toISOString(), end: null };
    case "7d":
      return { start: moment().subtract(7, "days").toISOString(), end: null };
    case "30d":
      return { start: moment().subtract(30, "days").toISOString(), end: null };
    case "90d":
      return { start: moment().subtract(90, "days").toISOString(), end: null };
    case "ytd":
      return { start: moment().startOf("year").toISOString(), end: null };
    case "1y":
      return { start: moment().subtract(1, "years").toISOString(), end: null };
    case "2y":
      return { start: moment().subtract(2, "years").toISOString(), end: null };
    case "all":
      if (userAccessLevel === "superadmin") {
        return { start: moment().subtract(30, "days").toISOString(), end: null };
      } else {
        return { start: moment("01-01-1970").toISOString(), end: null };
      }
      break;
    default:
      if (filter?.includes("-")) {
        const [start, end] = filter.split("-");
        return { start: moment(start).toISOString(), end: moment(end).toISOString() };
      } else {
        return { start: null, end: null };
      }
  }
}

export const numberWithCommas = (x: number) => {
  return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

const sanitizeData = (data: any): any => {
  if (Array.isArray(data)) {
    return data.map(sanitizeData);
  } else if (typeof data === 'object' && data !== null) {
    const sanitizedObject: any = {};
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        sanitizedObject[key] = sanitizeData(data[key]);
      }
    }
    return sanitizedObject;
  } else if (typeof data === 'string') {
    return data.replace(/"/g, ''); // Escape double quotes
  } else {
    return data;
  }
};

export const getCsv = (data: Array<any>) => {
  let csvString = "";

  if (!data.length) return;

  data.forEach((item) => {
    const parser = new Parser();
    const sanitizedItem = sanitizeData(item);
    if (Array.isArray(sanitizedItem) && sanitizedItem.length === 0) {
      return;
    } else if (typeof sanitizedItem === 'object' && Object.keys(sanitizedItem).length === 0) {
      return;
    }
    csvString = csvString + "\n" + parser.parse(sanitizedItem);
  });

  return sanitizeData(csvString);
}
