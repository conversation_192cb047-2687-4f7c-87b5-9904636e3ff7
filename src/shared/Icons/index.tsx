import React from "react";

import FilterIcon from "./images/FilterIcon";
import SearchIcon from "./images/SearchIcon";
import BookOpen from "./images/BookOpen";
import Bus from "./images/Bus";
import Child from "./images/Child";
import Flag from "./images/Flag";
import Gavel from "./images/Gavel";
import Handshake from "./images/Handshake";
import Heartbeat from "./images/Heartbeat";
import Home from "./images/Home";
import Microphone from "./images/Microphone";
import Mobile from "./images/Mobile";
import MoneyBill from "./images/MoneyBill";
import Passport from "./images/Passport";
import Shield from "./images/Shield";
import Tree from "./images/Tree";
import Comment from "./images/Comment";
import DownChevron from "./images/DownChevron";
import Scroll from "./images/Scroll";

export const IconsDictionaryByCategory: { [key: string]: RegExp } = {
  bookOpen: /Safety|Education|Charter|Educational|Pre(.|)K|School|Student|Teacher/i,
  tree: /Climate|Green|Renewable Energy/i,
  child: /Child|Minor|Childcare|Parenting/i,
  heartbeat: /Health|Healthcare|Addiction|COVID(.|)19|Medicaid|Medicare|Mental|Opioid|Epidemic|Opioid Crisis|Prescrition|Drug/i,
  bus: /Transportation|Accessibility|Infrastructure/i,
  gavel: /Gun|Safety|Criminal|Justice Reform|Incarceration|Bail|Crime|Death|penalty|Legalizing|marijuana|Police|Policing|Prosecutorial|Discretion|Immunity|Conviction/i,
  home: /House|Home|Housing|Evictions|Homeless|Landlord|Tenant/i,
  mobile: /Technology|Broadband access|Online Learning|Regulation/i,
  moneyBill: /Economy|Diversity|Inclusion|Economic|Development|Fiscal|Trade|Job|Jobs|Monetary|Business|Social Security|Taxes|Worker/i,
  passport: /Immigration|Border Security|Refugee|Asylum/i,
  flag: /Foreign|Relation|Indo-Pacific|China|Europe|Israel|Palestine|Middle.East|Nuclear|Proliferation|Russia/i,
  microphone: /^About.*|^Why.*/i,
  shield: /^ *Security *$|Cybersecurity|Data Privacy|Defense|National Security|War on Terror/i,
  handshake: /Civil|LGB[a-z]+|Racial Justice|Redistricting|Religious|Reproductive|Voting|Women|Rights/i,
  comment: /Other/i,
};
const defaultIcon: string = "comment";
const predefinedIconTypes = ["filter", "search"];

export function getIconByType(topicType: string | undefined): string {
  let selectedIcon = topicType;

  if (!topicType) {
    return defaultIcon;
  }

  for (const category in IconsDictionaryByCategory) {
    if (
      topicType.toString().trim().match(IconsDictionaryByCategory[category]) !==
      null
    ) {
      selectedIcon = category;
    }
  }

  return selectedIcon || defaultIcon;
}

export default function Icons(props: { iconType: string; iconClass?: string }) {
  const { iconType, iconClass } = props;
  let currentIconType = iconType;

  if (iconType && !predefinedIconTypes.includes(iconType)) {
    currentIconType = getIconByType(iconType);
  }

  if (
    (!iconType || !currentIconType) &&
    !predefinedIconTypes.includes(iconType)
  ) {
    currentIconType = defaultIcon;
  }

  switch (currentIconType) {
    case "filter": {
      return <FilterIcon iconClass={iconClass} />;
    }
    case "search": {
      return <SearchIcon iconClass={iconClass} />;
    }
    case "bookOpen": {
      return <BookOpen iconClass={iconClass} />;
    }
    case "tree": {
      return <Tree iconClass={iconClass} />;
    }
    case "child": {
      return <Child iconClass={iconClass} />;
    }
    case "heartbeat": {
      return <Heartbeat iconClass={iconClass} />;
    }
    case "bus": {
      return <Bus iconClass={iconClass} />;
    }
    case "gavel": {
      return <Gavel iconClass={iconClass} />;
    }
    case "home": {
      return <Home iconClass={iconClass} />;
    }
    case "mobile": {
      return <Mobile iconClass={iconClass} />;
    }
    case "moneyBill": {
      return <MoneyBill iconClass={iconClass} />;
    }
    case "passport": {
      return <Passport iconClass={iconClass} />;
    }
    case "flag": {
      return <Flag iconClass={iconClass} />;
    }
    case "microphone": {
      return <Microphone iconClass={iconClass} />;
    }
    case "shield": {
      return <Shield iconClass={iconClass} />;
    }
    case "handshake": {
      return <Handshake iconClass={iconClass} />;
    }
    case "scroll": {
      return <Scroll iconClass={iconClass} />;
    }
    case "downChevron": {
      return <DownChevron iconClass={iconClass} />;
    }
    case "comment":
    default: {
      return <Comment iconClass={iconClass} />;
    }
  }
}
