@import 'styles/variables';

.<PERSON><PERSON> {
  padding: 5px 15px;
  border: none;
  border-radius: 40px;
  background-color: white;
  font-size: 16px;
  color: $dark-blue;
  border: 1px solid $grey;

  &.active,
  &:hover {
    background-color: $dark-blue;
    color: white;
  }

  &.error-outline {
    color: #F00;
    border-color: #F00;

    &.active,
    &:hover {
      color: white;
      background: #F00;
    }
  }

  &.success-outline {
    color: $mint-green;
    border-color: $mint-green;

    &.active,
    &:hover {
      color: white;
      background: $mint-green;
    }
  }

  &.primary {
    background-color: $dark-blue;
    color: white;

    &.active,
    &:hover {
      opacity: 0.8;
    }
  }

  &.disabled {
    color: $grey !important;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  &.iconOnly svg {
    margin: 0;
  }

  svg {
    margin: 0 0 0 5px;
  }
}