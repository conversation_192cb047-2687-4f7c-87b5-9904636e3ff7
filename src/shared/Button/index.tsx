import React from "react";
import cn from "classnames";

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { IconName } from "@fortawesome/fontawesome-svg-core";

import classes from "./Button.module.scss";

interface ButtonProps {
  callback?: VoidFunction | Function | any;
  iconText?: IconName;
  text: string;
  customClass?: string;
  children?: any;
}

export default function Button(props: ButtonProps) {
  const { callback, iconText, text, customClass, children } = props;

  function handleFormEvent(event: any) {
    event.preventDefault();
    event.stopPropagation();

    if (callback) callback();
  }

  const parsedCustomClass = !customClass ? false : customClass?.match(',') ? customClass.split(',').map((s: string) => classes[s]).join(' ') : classes[customClass];

  return (
    <button
      className={cn(classes.Button, (parsedCustomClass && parsedCustomClass), (!text && iconText && classes.iconOnly))}
      onClick={handleFormEvent}
    >
      {iconText && <FontAwesomeIcon icon={['fas', iconText]} />}
      <span className={classes.text}> {text}</span>
      {children}
    </button>
  );
}
