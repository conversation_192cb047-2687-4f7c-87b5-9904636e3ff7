import ReactDOM from 'react-dom';
import cn from "classnames";

import PopUpClasses from 'popups/PopUp.module.scss';

interface ModalProps {
  opaque?: boolean;
  contentWrapperStyle?: React.CSSProperties;
  children: any;
}

const Modal = ({ opaque, contentWrapperStyle, children }: ModalProps) =>
  ReactDOM.createPortal(
    <div className={cn(PopUpClasses.PopUpOverlay, opaque && PopUpClasses.opaque)}>
      <div className={PopUpClasses.ContentWrapper} style={contentWrapperStyle}>
        {children}
      </div>
    </div>,
    document.body
  )

export default Modal;
