/* Center the modal */
.ReactModal__Overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Modal content styling */
.ReactModal__Content {
  position: relative !important;
  width: 300px;
  padding: 30px 40px !important;
  border: 1px solid #ccc;
  background: #fff;
  border-radius: 5px !important;
  outline: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: calc(100% - 40px) !important;
  inset: 0 !important;
  top: -50px !important;
}

/* Title styling */
.ReactModal__Content h2 {
  margin-top: 0;
  font-size: 18px;
  font-weight: bold;
}

/* Message styling */
.ReactModal__Content p {
  margin: 20px 0;
  font-size: 14px;
}

/* Button styling */
.ReactModal__Content button {
  margin: 0 5px;
  padding: 5px 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  background: #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
}

.ReactModal__Content button:hover {
  background: #e0e0e0;
}
