import React from 'react';
import Modal from 'react-modal';
import './custom.css'; // Import the CSS file

Modal.setAppElement('#root'); // Set the app element for accessibility

interface CustomConfirmModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const CustomConfirmModal: React.FC<CustomConfirmModalProps> = ({ isOpen, title, message, onConfirm, onCancel }) => {
  return (
    <Modal isOpen={isOpen} onRequestClose={onCancel} contentLabel={title}>
      <h2>{title}</h2>
      <p>{message}</p>
      <button onClick={onCancel}>Cancel</button>
      <button onClick={onConfirm}>Confirm</button>
    </Modal>
  );
};

export default CustomConfirmModal;
