"use client"

import { forwardRef } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Toolt<PERSON>, Legend } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "shared/ui/card"
import { ChartContainer, ChartConfig } from "shared/ui/chart"

export const PieChartColors = ["#5BC776", "#445472", "#FFCE56"]

const chartConfig = {
  mobile: { label: "Mobile", color: PieChartColors[0] },
  desktop: { label: "Desktop", color: PieChartColors[1] },
  tablet: { label: "Tablet", color: PieChartColors[2] },
} as ChartConfig;

const formatNumberWithComma = (value: number) => {
  return value.toLocaleString() + "%";
};

interface PieChartComponentProps {
  data: { name: string; value: number }[];
}

export const PieChartComponent = forwardRef<HTMLDivElement, PieChartComponentProps>(({ data }, ref) => (
  <PieChart width={175} height={175}>
    <Pie
      data={data}
      cx="50%"
      cy="50%"
      outerRadius={80}
      innerRadius={60} // Added innerRadius to create a donut chart
      fill="#8884d8"
      dataKey="value"
      // label
      stroke="#ffffff" // Added stroke color
      strokeWidth={3} // Added stroke width
    >
      {data.map((entry: any, index: number) => (
        <Cell key={`cell-${index}`} fill={PieChartColors[index % PieChartColors.length]} />
      ))}
    </Pie>
    <Tooltip separator=": " formatter={(value: number) => formatNumberWithComma(value)} />
  </PieChart>

  // <Card className="flex flex-col" ref={ref}>
  //   <CardHeader className="items-center pb-0">
  //     <CardTitle>Device Usage</CardTitle>
  //     <CardDescription>Distribution of device usage</CardDescription>
  //   </CardHeader>
  //   <CardContent className="flex-1 pb-0">
  //     <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[250px]">
  //       <PieChart width={250} height={250}>
  //         <Pie
  //           data={data}
  //           cx="50%"
  //           cy="50%"
  //           outerRadius={80}
  //           fill="#8884d8"
  //           dataKey="value"
  //           label="name"
  //         >
  //           {data.map((entry: any, index: number) => (
  //             <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
  //           ))}
  //         </Pie>
  //         <Tooltip />
  //         <Legend />
  //       </PieChart>
  //     </ChartContainer>
  //   </CardContent>
  // </Card>
))
