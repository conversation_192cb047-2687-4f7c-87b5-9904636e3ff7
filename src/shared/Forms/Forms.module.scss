@import 'styles/variables';

.Form {
    display: flex;
    flex-direction: column;
    font-size: 16px;

    .title {
        width: 100%;
        color: $blue;
        font-size: 18px;
        font-weight: 300;
        margin: 30px 0 15px 0;
    }

    .radioInputBlock {
        display: flex;
        flex-direction: column;
        margin-bottom: 10px;
        input {
            margin: 5px;
        }
    }

    .SideBySide {
        display: flex;
        flex-direction: row;

        :nth-child(1) {
            margin-right: 20px;
        }
    }

    .InputWithLabel {
        width: 100%;

        * {
            width: 100%;
        }

        label {
            display: block;
            color: $dark-grey;
            font-weight: 300;
        }

        input,
        select,
        textarea,
        .suffix {
            appearance: none;
            padding: 10px 10px;
            border-radius: 5px;
            border: 1px solid $grey;
            font-size: 16px;
            margin-bottom: 20px;
        }

        select {
            cursor: pointer;
        }

        &.suffixed {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 20px;

            * {
                width: auto;
                margin-bottom: 0;
            }

            label {
                padding-right: 10px
            }

            select,
            input,
            textarea {
                width: 50%;
            }

            .suffix {
                font-weight: bold;
                margin-left: -5px;
                padding: 10px;
                background-color: #F0F0F0;
                border: 2px solid #F0F0F0;
                border-left: 1px solid $grey;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }

    .hasError {
        border: 1px solid #F00 !important;
    }

    .errorText {
        color: #F00;
    }

    .checkbox {
        display: flex;
        flex-direction: row;
        align-items: center;

        input {
            margin: 5px;
        }
    }

    small {
        color: $dark-grey;
        font-size: 14px;
    }
}