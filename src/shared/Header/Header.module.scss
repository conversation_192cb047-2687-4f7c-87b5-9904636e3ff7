@import 'styles/variables.scss';

.Header {
  position: fixed;
  display: flex;
  justify-content: center;
  top: 0;
  left: 0;
  z-index: 15;
  width: 100%;
  height: 80px;

  background-color: $header-blue;
  border-bottom: 2px solid $offwhite;

  .wrapper {
    max-width: 1400px;
    width: 100%;
    height: 100%;
    padding: 15px 30px;
    display: flex;
    align-items: center;
    position: relative;
  }

  h1 {
    color: white;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-weight: 600;
    font-size: 32px;
  }

  .logo {
    max-height: 50px;
    border-radius: 5px;
    background-color: white;
    padding: 3px;
  }

  .signOut {
    color: white;
    position: absolute;
    right: 40px;
    cursor: pointer;
  }

  .signOut:hover {
    color: lightblue;
  }

  .pdfHeader {
    display: flex;
    align-items: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);

    h2 {
      color: white;
      font-size: 50px;
      margin: 0 10px;
    }
  }
}

.hamburgerButton {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  z-index: 10;
}

.hamburgerIcon {
  width: 24px;
  height: 18px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: white;
    border-radius: 2px;
    transition: all 0.3s ease-in-out;
  }

  &.open {
    span:first-child {
      transform: translateY(8px) rotate(45deg);
    }

    span:nth-child(2) {
      opacity: 0;
    }

    span:last-child {
      transform: translateY(-8px) rotate(-45deg);
    }
  }
}

@media (max-width: 640px) {
  .Header {
    height: 120px;
    padding-top: 40px;
    border: none;

    .wrapper {
      padding: 20px;
      position: relative;
    }

    .hideOnMobile {
      display: none;
    }

    .logo {
      max-height: 40px;
    }
  }

  .hamburgerButton {
    display: block;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
  }
}