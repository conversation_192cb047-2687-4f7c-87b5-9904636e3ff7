@import 'styles/variables';

.TableScrollWrapper {
  max-height: 600px;
  overflow-y: auto;
  border-top: 1px solid lightgray;
  border-bottom: 1px solid lightgray;
  border-right: 1px solid lightgray;
  border-left: 1px solid lightgray;
  border-radius: 10px;

  &.selected {
    overflow: visible;
  }

  // &:hover {
  //   max-height: calc(100vh - 400px);
  // }
}

table.table {
  width: 100%;
  border-radius: 10px;

  thead {
    position: sticky;
    top: 0;
    z-index: 2;

    tr {
      border: none;
      border-bottom: 1px solid lightgray;
      background: rgb(237, 237, 237);
      border-top-left-radius: 0;
      border-top-right-radius: 10px;

      th:first-child {
        border-top-left-radius: 10px;
      }

      th:last-child {
        border-top-left-radius: 0;
        border-top-right-radius: 10px;
      }
    }

    tr:first-child {
      border-top-left-radius: 10px;
    }

    tr:last-child {
      border-top-left-radius: 0;
      border-top-right-radius: 10px;
    }

    th {
      text-align: center;
      padding: 10px 0;

      svg {
        display: inline;
        max-width: 20px;
        position: relative;
        top: -2px;
      }
    }
  }

  tbody {
    tr {
      border-bottom: 1px solid #00000011;

      &:hover {
        background-color: hsla(0, 0, 0, 0.05);
      }

      &:nth-child(even) {
        background: #00000005;

        &:hover {
          background-color: hsla(0, 0, 0, 0.075);
        }
      }

      td:last-child {
        border-right: none;
      }
    }

    tr:last-child {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 10px;
      border-bottom: none;

      td:first-child {
        border-bottom-left-radius: 10px;
      }

      td:last-child {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 10px;
      }
    }

    td {
      padding: 10px;
      border-right: 1px solid lightgray;
      border-left: none;

      >svg {
        display: inline;
        max-width: 20px;
        position: relative;
        top: -2px;
      }

      .link {
        color: $blue;
        text-decoration: underline;
        cursor: pointer;

        &:hover {
          color: black;
        }
      }

      button {
        padding: 5px 10px;
      }

      &.center {
        text-align: center;
        vertical-align: middle;
      }

      .buttonContainer {
        position: relative;

        button {
          position: relative;
          z-index: 0;
        }
      }

      &:hover {
        background-color: hsla(0, 0%, 100%, 0.4);
      }
    }
  }
}

.dropdownBar {
  position: absolute;
  z-index: 20;
  left: -10px;
  margin-top: 10px;
  padding: 10px;
  border-radius: 10px;
  background: white;
  box-shadow: 5px 5px 20px 5px $transparent-black-low;
  transition: opacity 5s, height 5s;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;

  button,
  div {
    padding: 10px;
    font-size: 16px;
    min-width: 170px;
    text-align: center;
  }
}

.infoIcon {
  height: 18px;
  width: 18px;
  display: inline-block;
  vertical-align: middle;
}

/* Mobile media queries grouped at the bottom */
@media (max-width: 768px) {
  .TableScrollWrapper {
    max-height: none;
    overflow-x: auto;
    width: 100%;
  }

  table.table {
    width: 100%;
    /* Ensures table doesn't get too compressed */
  }

  table.table thead th,
  table.table tbody td,
  table.table tbody td button {
    padding: 5px;
    font-size: 14px;
  }

  table.table tbody td .buttonContainer {
    min-width: 90px;
  }

  table.table tr {
    display: flex;
    flex-direction: column;
    border: none;

    td {
      border: none;
    }
  }

  .dropdownBar button,
  .dropdownBar div {
    min-width: 0px;
    padding: 5px;
    font-size: 14px;
  }

  /* Page subtitle responsive adjustments */
  :global(.subTitle) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    div {
      align-self: flex-end;
    }
  }
}