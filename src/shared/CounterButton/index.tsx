import React from 'react';

import classes from './CounterButton.module.scss';

export default function CounterButton(props: { callback?: VoidFunction, buttonCount: number; buttonText: string }) {
  const { callback, buttonCount, buttonText } = props;

  // callback = "askQuestion( event, 564 )"

  return (
    <button className={classes.CounterButton} onClick={callback}>
      <span className={classes.amount}>{buttonCount}</span>
      <span className={classes.text}>{buttonText}</span>
    </button>
  );
}
