import { ReactNode } from 'react';
import * as interfaces from 'interfaces';
import Header from 'shared/Header';
import Sidebar from 'shared/Sidebar';
import cn from 'classnames';
import { AuthService } from 'services';

import pageClasses from 'styles/PageWrapper.module.scss';
import classes from './PageLayout.module.scss';

export default function PageLayout(props: { user?: interfaces.UserInterface, client?: interfaces.ClientInterface | null, className?: string, children: ReactNode, authService: AuthService, }) {
  const { user, client, className, children, authService } = props;
  return (
    <div className={cn(pageClasses.Page, pageClasses.wide, className)}>
      <Header user={user} client={client} authService={authService} />

      <div className={classes.Wrapper}>
        <Sidebar user={user} />
        <div className={classes.Content}>
          {children}
        </div>
      </div>
    </div>
  )
}