.sidebar,
.sidebarLinksContainer,
.shortSidebarLinksContain {
  position: sticky;
  top: 80px;
  width: 240px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 4px;
  z-index: 1;
}

.shortSidebarLinksContain {
  background-color: #f6f8fa;
  z-index: 2;
  top: 500px;
  // bottom: 200px;
  // position: fixed;
}

.sidebar {
  background-color: #f6f8fa;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: min-content;
  height: 100%;
  min-height: 100vh;
  padding: 30px 20px 30px 0px;
}

.sidebarMobile {
  display: none;
}

.sidebarDesktop {
  display: flex;
}

@media screen and (max-width: 768px) {
  .sidebar {
    width: 100%;
    min-height: auto;
    padding: 15px;
  }

  .shadow {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    background: #00000055;
    height: 100vh;
    width: 100vh;
    z-index: 10;
  }

  .sidebarLinksContainer,
  .shortSidebarLinksContain {
    width: 100%;
    position: relative;
    top: 0;
  }

  .shortSidebarLinksContain {
    position: relative;
    bottom: 0;
    margin-top: 20px;
  }
}

@media (max-width: 640px) {
  .hideOnMobile {
    display: none;
  }

  .sidebarDesktop {
    display: none;
  }

  .sidebarMobile {
    display: block;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 50vh;
    max-height: 400px;
    background-color: #f6f8fa;
    z-index: 1000;
    border-top: 1px solid #DCE5ED;
    border-radius: 10px 10px 0 0;
    box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.05);
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;

    &.open {
      transform: translateY(0);
    }
  }

  .sidebarMobileContent {
    height: 100%;
    overflow-y: auto;
    padding: 15px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    /* Add some padding at the bottom to ensure visibility of last items */
    padding-bottom: 60px;

    /* Hide scrollbar for cleaner look */
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .sidebarLinksContainer,
    .shortSidebarLinksContain {
      width: 100%;
    }

    .shortSidebarLinksContain {
      position: relative;
      bottom: auto;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #DCE5ED;
    }

    /* Ensure NavLink active states work in mobile */
    :global {

      a.active,
      a.active span {
        color: var(--color-crimson-100) !important;
      }

      a.active svg path {
        fill: var(--color-crimson-100) !important;
      }
    }
  }

  // Remove this style as we're using the shared sidebar content
  .sidebarMobileLinks {
    display: none;
  }
}

@media screen and (max-width: 450px) {
  .sidebarLinksContainer {
    height: auto;
  }

  .shortSidebarLinksContain {
    height: auto;
  }
}