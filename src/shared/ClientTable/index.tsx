import { useState, useEffect } from 'react';
import cn from 'classnames';
import { toast, ToastContainer } from 'react-toastify';

import { ClientService } from 'services';
import { ClientInterface, SessionInterface } from 'interfaces';

import pageClasses from 'styles/PageWrapper.module.scss';
import tableClasses from 'shared/Table/Table.module.scss';
import classes from './ClientTable.module.scss';

import EditClient from 'popups/EditClient';
import Button from 'shared/Button';
import DropDown from 'shared/Dropdown';
import { InformationCircleIcon, LockClosedIcon, LockOpenIcon } from '@heroicons/react/20/solid';

export default function ClientTable(props: {
  clientService?: ClientService;
  clientSessionRecords: SessionInterface[];
  selectRecord: (record: ClientInterface | null) => void;
  selectedClient: ClientInterface | null;
  fetchClients: () => void;
}) {
  const { clientService, clientSessionRecords, selectRecord, selectedClient, fetchClients } = props;
  const [livePages, setLivePages] = useState(0);

  const [records, setRecords] = useState<ClientInterface[]>([]);
  const [editRecord, setEditRecord] = useState<ClientInterface | null>(null);

  const [clientSessions, setClientSessions] = useState<SessionInterface[]>([]);
  const [selectedClientsSession, setSelectedClientsSession] = useState<SessionInterface | null>(null);
  const [adminClientId, setAdminClientId] = useState<string | null>(null);
  const [prettySession, setPrettySession] = useState<boolean>(false);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const selectSession = (id?: string | null) => {
    const clientSession = clientSessions.find((s) => s.user?.client?.id === id);

    setSelectedClientsSession(!!clientSession ? clientSession : null);

    if (!clientSession) {
      const superAdminSession = clientSessions.find((s) => s.user?.accessLevel === 'super admin');

      if (superAdminSession) {
        localStorage.setItem('token', superAdminSession.token);
        localStorage.setItem('user', JSON.stringify(superAdminSession.user));
        localStorage.setItem('client', JSON.stringify(superAdminSession.user.client));
      }
    }
  };

  const disableRecord = (client: ClientInterface) => {
    // eslint-disable-next-line no-restricted-globals
    if (confirm(`Are you sure you want to disable ${client.name}?`)) {
      updateRecord({id: client.id, enabled: false});
      selectRecord(null);
      resetClients();
    }
  }
  const enableRecord = (client: ClientInterface) => {
    updateRecord({id: client.id, enabled: true});
  }
  const updateRecord = (client: Partial<ClientInterface>) => {
    clientService?.update(client, (item: ClientInterface) => {
      if (!item?.id) {
        toast.error(
          "Saving the client failed. Please try again later.",
        );
      } else {
        fetchClients();
        toast.success("Client saved successfully.");
      }
    });
  }

  const resetClients = (updatedClientSessions: SessionInterface[] | null = null) => {
    let filteredRecords = 
      (updatedClientSessions || clientSessionRecords).map(session => session?.user?.client)
        .filter((client): client is ClientInterface => client !== undefined)
        .sort((a, b) => a.name.toString().replace(/City of /, '').localeCompare(b.name.toString().replace(/City of /, '')))
        .sort((a, b) => (b?.clientType ?? '').localeCompare(a?.clientType ?? ''))
        .sort((a, b) => (a?.enabled === b?.enabled ? 0 : a?.enabled ? -1 : 1))
        .filter((c, i, self) => self.findIndex((t) => t.id === c.id) === i);

    setLivePages(filteredRecords.filter((client) => client.isPublished && client.enabled).length);

    if (selectedClient)
      filteredRecords = filteredRecords.filter((c) => c.id === selectedClient.id);

    setRecords(filteredRecords);

    const filteredSessions = (updatedClientSessions || clientSessionRecords).filter((s) => s.user.client !== undefined);
    setClientSessions(filteredSessions);

    const superAdminSession = filteredSessions.find((s) => s.user?.accessLevel === 'super admin');
    setAdminClientId(superAdminSession?.user?.client?.id || null);
  }

  useEffect(resetClients, [clientSessionRecords, selectedClient]);

  const sessionURL = (
    window.location.host.match('localhost') ? `http://localhost:3001` :
    window.location.host.match('mock') ? `https://admin-mock.repd.us` : 
    window.location.host.match('staging') ? `https://admin-staging.repd.us` : 
    'https://admin.repd.us'
  );

  return <div className={cn(pageClasses.Page, pageClasses.wide, classes.Clients)}>
    {selectedClientsSession && (
      <div className={cn(classes.SessionIframeWrapper, prettySession && classes.pretty)}>
        <select onChange={(e) => selectSession(!e.target.value ? null : e.target.value)}>
          <option value={adminClientId || ''}>Close</option>
          <option value={adminClientId || ''}></option>
          {clientSessions
            .filter((c, i, self) => self.findIndex((t) => t.user?.client?.id === c.user?.client?.id && t.user?.client?.enabled === true) === i)
            .sort((a, b) => (a.user?.client?.name?.toString().replace(/City of /, '') || '').localeCompare(b.user?.client?.name?.toString().replace(/City of /, '') || ''))
            .map((s, i) => 
              <option key={i} value={s.user?.client?.id} selected={selectedClientsSession?.token === s?.token}>{s.user?.client?.name}</option>
            )
          }
        </select>

        <Button callback={() => selectSession(null)} customClass="iconOnly" text="" iconText="window-close" />

        {selectedClientsSession ?
          <iframe src={`${sessionURL}/?token=${selectedClientsSession.token}&uti=${selectedClientsSession.user.id}`} title="Client Session" /> :
          <div className={classes.noSession}>
            To begin, select a client session from the dropdown
            <select onChange={(e) => selectSession(e.target.value)}>
              <option value="">Select an Option</option>
              <option value=""></option>
              {clientSessions.map((s, i) => <option key={i} value={i}>{s.user?.client?.name}</option>)}
            </select>
          </div>
        }
      </div>
    )}

    <div className={cn(pageClasses.Wrapper, classes.wrapper)}>
      <h2 className={pageClasses.subTitle}>
        {!selectedClient && 
          <>
            <p>
              <b>Total Pages: {records.length}</b>
              <br/>
              <b>Live Pages: {livePages}</b>
            </p>
          </>
        }

        {selectedClient && <>
          <p>
            Selected Page: <b>{selectedClient.name}</b>
            <br/>
            Live pages: <b>{livePages}</b>
          </p>
          <Button text="Back" callback={() => selectRecord(null)} />
        </>}
      </h2>

      <div className={cn(tableClasses.TableScrollWrapper, selectedClient && tableClasses.selected)}>
        <table className={tableClasses.table}>
          <thead>
            <tr>
              <th style={{width: '175px'}}>Name</th>
              <th>Type</th>
              <th>Live?</th>
              <th>Locked?</th>
              <th>Last Updated</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {records.length === 0 && (
              <tr>
                <td colSpan={4} className={tableClasses.center}>Fetching clients...</td>
              </tr>
            )}
            {records.map((client, i) => (
              <tr key={i}>
                <td className={tableClasses.center}>
                  { !client.enabled ? client.name : 
                    <span className={tableClasses.link} onClick={() => selectRecord(client)}>
                      {client.name}
                    </span>
                  }
                </td>
                <td className={tableClasses.center}>
                  <b>{client.clientType}</b>
                </td>
                <td className={tableClasses.center}>
                  {client.enabled ? <b>Yes</b> : 'No'}
                </td>
                <td className={tableClasses.center}>
                  {!client.isLocked && client.isPublished && client.enabled ? 
                    <LockOpenIcon color='#567deb' className={tableClasses.infoIcon} /> : 
                    <LockClosedIcon className={tableClasses.infoIcon} />
                  }
                </td>
                <td className={tableClasses.center}>{(new Date(client.updatedAt)).toLocaleDateString()}</td>
                <td className={tableClasses.center}>
                  <div className={tableClasses.buttonContainer}>
                    {!client.enabled ? 
                      <Button callback={() => enableRecord(client)} text="Enable" iconText="plus-circle" />
                      : 
                      <DropDown key={client.id} kind="table" text="Options">
                        <Button callback={() => { setPrettySession(false); selectSession(client.id); }} text="Admin" />
                        <Button callback={() => { setPrettySession(true); selectSession(client.id); }} text="Present" iconText="desktop" />
                        <Button callback={() => { setEditRecord(client); }} text="Edit Client" iconText="pen" />
                        <Button callback={() => disableRecord(client)} customClass="error-outline" text="Disable Client" iconText="minus-circle" />
                        <Button callback={() => window.open(`${clientService?.appURL}/${client.name}`)} text="Public Site" iconText="link" />
                      </DropDown>
                    }
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {editRecord && (
          <EditClient
            clientService={clientService}
            client={editRecord}
            records={records}
            setRecords={setRecords}
            handleClose={() => setEditRecord(null)}
          />
        )}
      </div>
    </div>
    <ToastContainer position="top-right" />
  </div>;
}
