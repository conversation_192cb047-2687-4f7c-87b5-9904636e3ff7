@import 'styles/variables';

.SessionIframeWrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 21;

  button { 
    position: fixed;
    top: 30px;
    right: 30px;
    z-index: 1;
    width: 50px;
    height: 50px;
    line-height: 10px;
    // padding: 10px;
    border-radius: 100px;
    background: #0e4f9b;
    color: white;
    font-size: 1.2em;

    &:hover {
      opacity: 0.8;
    }
  }

  select {
    font-weight: bold;
    font-size: 1.2em;
    position: fixed;
    min-width: 200px;
    padding: 5px 10px;
    border: 20px solid #0e4f9b;
    border-bottom-right-radius: 40px;
    color: #0e4f9b;
    overflow: hidden !important;
    z-index: 1;
    cursor: pointer;
  }

  iframe {
    width: 100%;
    height: 100%;
    border: 20px solid #0e4f9b;
  }

  .noSession {
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20px;
    width: 100%;
    height: 100%;
    border: 20px solid #0e4f9b;
    background: #0e4f9b99;
    text-align: center;
    color: white;
    font-weight: bold;
    backdrop-filter: blur(10px);
    z-index: 0;

    select {
      border-width: 5px;
      border-radius: 5px;
      position: static;
    }
  }

  &.pretty { 
    select {
      max-width: 230px;
      top: auto;
      bottom: 32px;
      left: 90px;
      border: none;
      border-radius: 5px;
      box-shadow: 0 0 10px 0 #0e4f9b55;
      padding: 10px 20px;
      font-weight: normal;
    }

    iframe { 
      border: none;
    }

    .noSession { 
      border: none;
    }

    button { 
      top: auto;
      bottom: 30px;
      left: 30px;
      right: auto;
      background: white;
      color: #0e4f9b;
      box-shadow: 0 0 10px 0 #0e4f9b55;
    }
  }

  @media (max-height: 730px) and (min-width: 800px) {
    iframe {
      height: 125%;
      width: 125%;
    }
  }
}