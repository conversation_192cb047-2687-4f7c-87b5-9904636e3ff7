import React from 'react';
import cn from 'classnames';

import classes from './IconAvatar.module.scss';
import Icons, { getIconByType } from 'shared/Icons';

export default function IconAvatar(props: { imageUrl?: string | null; categoryIcon: string }) {
  const selectedIconType = getIconByType(props.categoryIcon);

  return (
    <div
      className={cn(classes.IconAvatar, classes[selectedIconType])}
      style={{ backgroundImage: props?.imageUrl || 'none' }}
    >
      <Icons iconType={props.categoryIcon} iconClass={classes.iconClass} />
    </div>
  );
}
