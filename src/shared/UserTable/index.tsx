/* eslint-disable no-restricted-globals */

import { useState, useEffect } from 'react';
import cn from 'classnames';
import { toast, ToastContainer } from 'react-toastify';

import { Tooltip } from '@material-ui/core'; // or any other tooltip library you are using
import { InformationCircleIcon } from '@heroicons/react/20/solid';
import titleCase from 'utils/titleCase';

import { UserService } from 'services';
import { ClientInterface, SessionInterface, UserInterface } from 'interfaces';
import { useServiceContext } from 'services/ServiceProvider';

import pageClasses from 'styles/PageWrapper.module.scss';
import tableClasses from 'shared/Table/Table.module.scss';

import EditUser from 'popups/EditUser';
import Button from 'shared/Button';
import DropDown from 'shared/Dropdown';

export default function UserTable(props: {
  userService?: UserService;
  clientSessionRecords: SessionInterface[];
  selectedClient: ClientInterface | null;
  authUser?: UserInterface;
  fetchClients: () => void;
}) {
  const { userService, clientSessionRecords, selectedClient, authUser, fetchClients } = props;
  const { adminStatsService } = useServiceContext();
  const [records, setRecords] = useState<UserInterface[]>([]);
  const [newUserRecords, setNewUserRecords] = useState<UserInterface[]>([]);
  const [editRecord, setEditRecord] = useState<UserInterface | null>(null);
  const newUserRecord = {
    id: '',
    email: '',
    firstName: '',
    lastName: '',
    accessLevel: authUser?.accessLevel === 'super admin' ? (selectedClient ? 'admin' : 'super admin') : 'manager',
    enabled: true,
    clientId: selectedClient?.id ?? '',
    client: selectedClient,
  };

  const disableRecord = (user: UserInterface) => {
    if (confirm(`Are you sure you want to disable ${user.firstName}?`)) {
      adminStatsService?.trackEvent('Team', 'disable_user');
      updateRecord({ id: user.id, enabled: false });
    }
  };

  const enableRecord = (user: UserInterface) => {
    adminStatsService?.trackEvent('Team', 'enable_user');
    updateRecord({ id: user.id, enabled: true });
  };

  const updateRecord = (user: Partial<UserInterface>) => {
    userService?.updateUser(user as UserInterface, (item: UserInterface) => {
      if (!item?.id) {
        toast.error("Saving the user failed. Please try again later.");
      } else {
        setRecords((prevRecords) =>
          prevRecords.map((u) => (u.id === item.id ? item : u))
        );
        fetchClients();
        toast.success("User saved successfully.");
      }
    });
  };

  const resetUsers = (updatedClientSessions: SessionInterface[] | null = null) => {
    let filteredRecords = clientSessionRecords
      .map((session: SessionInterface) => session.user)
      .filter((user): user is UserInterface => user !== undefined)
      .filter(u => !!u?.client)
      .sort((a, b) => (a?.firstName ?? '').localeCompare(b?.firstName ?? ''))
      .sort((a, b) => (b?.accessLevel ?? '').localeCompare(a?.accessLevel ?? ''))
      .sort((a, b) => (a?.enabled === b?.enabled ? 0 : a?.enabled ? -1 : 1))
      .filter((c, i, self) => self.findIndex((t) => t.id === c.id) === i);

    if (selectedClient) {
      filteredRecords = filteredRecords.filter((user) => user.client?.id === selectedClient.id);
    } else {
      filteredRecords = filteredRecords.filter((user) => user.accessLevel === 'super admin');
    }

    setRecords(filteredRecords);
  };

  useEffect(resetUsers, [clientSessionRecords, selectedClient]);

  return (
    <div className={cn(pageClasses.Page, pageClasses.wide)}>
      <div className={cn(pageClasses.Wrapper)}>
        <h2 className={pageClasses.subTitle}>
          Users ({records.length})
          <div>
            <Button text='Add New User' callback={() => {
              adminStatsService?.trackEvent('Team', 'add_new_user');
              setEditRecord(newUserRecord as UserInterface);
            }} />
          </div>
        </h2>
        <div className={cn(tableClasses.TableScrollWrapper, records.length <= 6 && tableClasses.selected)}>
          <table className={tableClasses.table}>
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>
                  <Tooltip title={
                    authUser?.accessLevel === 'super admin' ?
                      'Super Admins have access to all clients and all features.\n\nAdmins have access to all features for a specific client.' :
                      'Admins & Managers can access the admin panel to make changes.'
                  } interactive>
                    <span>
                      Role
                      {' '}
                      <InformationCircleIcon className={tableClasses.infoIcon} />
                    </span>
                  </Tooltip>
                </th>
                <th>Last Updated</th>
                <th style={{ width: '221px' }}></th>
              </tr>
            </thead>
            <tbody>
              {records.length === 0 && (
                <tr>
                  <td colSpan={4} className={tableClasses.center}>Fetching users...</td>
                </tr>
              )}
              {records.map((user, i) => (
                <tr key={i}>
                  <td className={tableClasses.center}>
                    <b>{user.firstName} {user.lastName}</b>
                  </td>
                  <td className={tableClasses.center}>{user.email}</td>
                  <td className={tableClasses.center}>
                    <b>{titleCase(user.accessLevel)}</b>
                  </td>
                  <td className={tableClasses.center}>{(new Date(user.updatedAt)).toLocaleDateString()}</td>
                  <td className={cn(tableClasses.center, tableClasses.buttonContainer)}>
                    <div className={tableClasses.buttonContainer}>
                      {!user.enabled ?
                        <Button callback={() => enableRecord(user)} text="Enable" iconText="plus-circle" /> :
                        <DropDown key={user.id} kind="table" text="Options">
                          <Button callback={() => {
                            adminStatsService?.trackEvent('Team', 'edit_user');
                            setEditRecord(user);
                          }} text="Edit" iconText="pen" />
                          <Button callback={() => disableRecord(user)} customClass="error-outline" text="Disable" iconText="minus-circle" />
                        </DropDown>
                      }
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {editRecord && (
            <EditUser
              authUser={authUser}
              userService={userService}
              user={editRecord}
              records={records}
              setRecords={setRecords}
              setNewUserRecords={setNewUserRecords}
              handleClose={() => { setEditRecord(null); fetchClients(); }}
            />
          )}
        </div>
      </div>
      <ToastContainer position="top-right" />
    </div>
  );
}