import { useState, useRef } from 'react';
import tableClasses from 'shared/Table/Table.module.scss';
import classes from './Dropdown.module.scss';

import Button from 'shared/Button';
import { useServiceContext } from 'services/ServiceProvider';

export default function DropDown(props: {
  children: React.ReactNode | React.ReactNode[];
  key: string;
  kind?: 'table' | 'page';
  text?: string;
  customClass?: string;
}) {
  const { children, key, kind = 'page', text = 'Options', customClass } = props;
  const { adminStatsService } = useServiceContext();

  const dropdownRef = useRef<HTMLDivElement>(null);
  const [openedDropdownKey, setOpenedDropdownKey] = useState<string | null>(null);
  const [dropdownOpened, setDropDownOpened] = useState(false);

  const handleDropdownClick = (id: string) => {
    const openedId = openedDropdownKey === id ? null : id;
    const isOpening = openedId !== null;

    setDropDownOpened(isOpening);
    setOpenedDropdownKey(openedId);

    adminStatsService?.trackEvent('Team', isOpening ? 'open_options_dropdown' : 'close_options_dropdown');
  };

  // If there's a mouse click on the page that is not the button, children or dropdown element, close the dropdown
  const handleClick = (e: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
      setDropDownOpened(false);
      setOpenedDropdownKey(null);
    }
  };
  document.addEventListener('mousedown', handleClick);

  return <>
    <Button
      text={text}
      customClass={customClass || "primary"}
      iconText={dropdownOpened && openedDropdownKey === key ? "caret-up" : "caret-down"}
      callback={() => handleDropdownClick(key)}
    />

    {dropdownOpened && openedDropdownKey === key && (
      <div ref={dropdownRef} className={kind === 'table' ? tableClasses.dropdownBar : classes.dropdownBar}>
        {children}
      </div>
    )}
  </>;
}
