import React, { useState, useEffect, ReactChild, useMemo } from "react";
import { Routes, Route, Navigate, useNavigate, useLocation } from "react-router-dom";
import { Helmet, HelmetProvider } from "react-helmet-async";

import * as Yup from "yup";
import YupPassword from "yup-password";

import { library } from "@fortawesome/fontawesome-svg-core";
import { fab } from "@fortawesome/free-brands-svg-icons";
import { fas } from "@fortawesome/free-solid-svg-icons";
import { far } from "@fortawesome/free-regular-svg-icons";

import Header from "./shared/Header";
import SiteSettings from "./pages/SiteSettings";
import VoterAnalytics from "./pages/VoterAnalytics";
import Questions from "./components/VoterAnalytics/Questions";
import Answers from "./pages/Answers";
// import VoteBuilder from "./pages/VoteBuilder";
import Team from "./pages/Team";
import Super from "./pages/Super";
import AnswerInvite from "./pages/AnswerInvite";
import AIPanel from "pages/AIPanel";

import Login from "./popups/Login";
import * as interfaces from "./interfaces";
import * as services from "./services";
import { NotificationContextProvider, PublishContextProvider } from "./hooks";
import classes from "./App.module.scss";
import "react-toastify/dist/ReactToastify.css";
import AccountCreation from "./popups/AccountCreation";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { ServiceProvider } from "./services/ServiceProvider";
import { AuthProvider } from "./services/AuthProvider";

// The logger is initialized, do not delete because it's not directly used here in this file.
import logger from "./utils/logger";
import Videos from "pages/VoterAnalytics/Videos";
import Emails from "pages/VoterAnalytics/Emails";
import QuestionsReview from "pages/QuestionsReview";
import QuestionsArchive from "pages/QuestionsArchive";
import AnswersDrafts from "pages/AnswerDrafts";
import CreateNewClient from "./pages/CreateNewClient"; // Correct the import path
import NotFound from "pages/NotFound"; // Import the NotFound component
import QuestionsShared from "pages/QuestionsShared";
import ErrorPage from "pages/ErrorPage"; // Import the ErrorPage component
import Loader from "components/Loader";
import { useUIStore } from "./hooks/zustand/uiStore";

import { scrollToTop } from './utils/scroll';

YupPassword(Yup); // extend yup.

library.add(fab, fas, far);

function App() {
  const [token, setToken] = useState<string>();
  const [client, setClient] = useState<interfaces.ClientInterface>();
  const [user, setUser] = useState<interfaces.UserInterface>();
  const [questionIdFromSearch, setQuestionIdFromSearch] = useState<string>("");
  const isPdfExporting = useUIStore(state => state.isPdfExporting);

  const navigate = useNavigate();
  const location = useLocation();

  const initialPath = window.location.pathname;
  const isNewClientFlow = initialPath.match("new-client") !== null;
  const isOnSuperPath = initialPath.match("super") !== null;
  const isAnswerInviteFlow = initialPath.match("answer-invite") !== null;
  const sessionTokenFromSearch =
    new URLSearchParams(window.location.search).get("token") || "";

  if (sessionTokenFromSearch) localStorage.token = sessionTokenFromSearch;

  const questionService = useMemo(() => new services.QuestionService(token, client?.id), [token, client?.id]),
    answersService = useMemo(() => new services.AnswersService(token, client?.id), [token, client?.id]),
    userService = useMemo(() => new services.UserService(token, client?.id), [token, client?.id]),
    clientService = useMemo(() => new services.ClientService(token, client?.id, client), [token, client?.id, client]),
    fileService = useMemo(() => new services.FileService(token, client?.id), [token, client?.id]),
    statService = useMemo(() => new services.StatService(token, client?.id, user?.accessLevel), [token, client?.id, user?.accessLevel]),
    ngpVanService = useMemo(() => new services.NgpVanService(token, client?.id), [token, client?.id]),
    adminStatsService = useMemo(() => new services.AdminStatsService(token, client?.id), [token, client?.id]);

  useEffect(() => {
    scrollToTop();
  }, [location.pathname]);

  useEffect(() => {
    if (!userService?.user && user) {
      userService.setUser(user)
    }
  }, [userService, user])

  function setlocalAuthState() {
    if (!token && localStorage.token) setToken(localStorage.token);
    if (!user && localStorage.user) setUser(JSON.parse(localStorage.user));
    if (!client && localStorage.client)
      setClient(JSON.parse(localStorage.client));
  }

  function initiateNewClientFlow() {
    if (!isNewClientFlow || !sessionTokenFromSearch) return;

    authService.getCurrent(
      sessionTokenFromSearch,
      (session: interfaces.SessionInterface | null) => {
        loginAndSetAuthState(session);
        navigate("/new-client");
      }
    );
  }

  function loginAndSetAuthState(session: interfaces.SessionInterface | null) {
    setlocalAuthState();

    if (session?.user?.accessLevel === "admin" && !isNewClientFlow)
      navigate("/voter-analytics");

    if (!session?.id || !session.token) return;

    setToken(session.token);
    setUser(session.user);
    // Ensure we set the client from the user object
    setClient(session.user.client);
  }

  const authService = new services.AuthService(sessionTokenFromSearch);

  setlocalAuthState();
  if (isNewClientFlow) initiateNewClientFlow();

  const isAdmin = user?.accessLevel === "admin" || user?.accessLevel === "manager";
  const isSuperAdmin = user?.accessLevel === "super admin";
  const isGuestAdmin = user?.accessLevel === "guest admin";
  const showLoginScreen = !(token && client);
  const redirectAwayFromSuper = !isSuperAdmin && isOnSuperPath;

  if (redirectAwayFromSuper) navigate("/questions-list");

  useEffect(() => {
    if (!isAnswerInviteFlow && localStorage.token)
      if ((user?.accessLevel || "").match("guest admin")) authService.logOut();

    if (isAnswerInviteFlow && sessionTokenFromSearch) {
      authService.getCurrent(
        sessionTokenFromSearch,
        (session: interfaces.SessionInterface | null) => {
          loginAndSetAuthState(session);
          setQuestionIdFromSearch(
            new URLSearchParams(window.location.search).get("questionId") || ""
          );
        }
      );
      return;
    }

    if (sessionTokenFromSearch) {
      authService.getCurrent(
        sessionTokenFromSearch,
        (session: interfaces.SessionInterface | null) => {
          loginAndSetAuthState(session);
        }
      );
    }
  }, [isAnswerInviteFlow, sessionTokenFromSearch]);

  if (showLoginScreen)
    return <Login callback={loginAndSetAuthState} authService={authService} />;

  if (localStorage.token)
    authService.getCurrent(
      localStorage.token,
      (session: interfaces.SessionInterface | null) => {
        if (isAnswerInviteFlow) return;
        if (!session || (session?.user?.accessLevel || "").match("visitor"))
          authService.logOut();
      }
    );

  const getRoutes = (): ReactChild => {
    if (isAdmin)
      return (
        <Routes>
          <Route path="/super" element={<Navigate replace to="/questions-list" />} />
          <Route
            path="/"
            element={<Navigate replace to="/questions-list" />}
          />
          <Route
            path="/voter-analytics/videos"
            element={<Videos client={client} service={statService} user={user} authService={authService} />}
          />
          {/* <Route
            path="/voter-analytics/emails"
            element={<Emails client={client} service={statService} user={user} authService={authService} />}
          /> */}
          <Route
            path="/voter-analytics"
            element={<VoterAnalytics client={client} clientService={clientService} service={statService} user={user} authService={authService} />}
          />

          <Route
            path="/questions-list"
            element={
              <Questions
                client={client}
                user={user}
                service={questionService}
                answersService={answersService}
                fileService={fileService}
                userService={userService}
                clientService={clientService}
                authService={authService}
              />
            }
          />

          <Route
            path="/questions-list/review"
            element={
              <QuestionsReview
                client={client}
                user={user}
                service={questionService}
                answersService={answersService}
                fileService={fileService}
                userService={userService}
                clientService={clientService}
                authService={authService}
              />
            }
          />

          <Route
            path="/questions-list/archive"
            element={
              <QuestionsArchive
                client={client}
                user={user}
                service={questionService}
                answersService={answersService}
                fileService={fileService}
                userService={userService}
                clientService={clientService}
                authService={authService}
              />
            }
          />

          <Route
            path="/questions-list/shared"
            element={
              <QuestionsShared
                client={client}
                user={user}
                service={questionService}
                answersService={answersService}
                fileService={fileService}
                userService={userService}
                clientService={clientService}
                authService={authService}
              />
            }
          />

          <Route
            path="/answers-list"
            element={
              <Answers
                user={user}
                answersService={answersService}
                ngpVanService={ngpVanService}
                fileService={fileService}
                authService={authService}
              />
            }
          />

          <Route
            path="/answers-list/drafts"
            element={
              <AnswersDrafts
                user={user}
                answersService={answersService}
                ngpVanService={ngpVanService}
                fileService={fileService}
                authService={authService}
              />
            }
          />
          {/* {client?.clientType !== "Government" && (
            <Route
              path="/votebuilder"
              element={
                <VoteBuilder
                  setClient={setClient}
                  clientService={clientService}
                  ngpVanService={ngpVanService}
                />
              }
            />
          )} */}
          <Route
            path="/edit-client"
            element={
              <SiteSettings setClient={setClient} service={clientService} user={user} authService={authService} />
            }
          />
          <Route
            path="/new-client"
            element={
              <AccountCreation
                setClient={setClient}
                clientService={clientService}
                userService={userService}
                ngpVanService={ngpVanService}
              />
            }
          />
          <Route
            // path="my-profile"
            path="/team"
            element={
              <Team
                user={user}
                setUser={setUser}
                userService={userService}
                clientService={clientService}
                authService={authService}
              />
            }
          />
          <Route
            path="/ai"
            element={
              <AIPanel
                user={user}
                setUser={setUser}
                userService={userService}
                clientService={clientService}
                authService={authService}
              />
            }
          />
          <Route path="*" element={<NotFound />} /> {/* Add this line */}
        </Routes>
      );
    else if (isSuperAdmin)
      return (
        <Routes>
          <Route path="/" element={<Navigate replace to="/super" />} />
          <Route
            path="/voter-analytics/videos"
            element={<Videos client={client} user={user} authService={authService} service={statService} />}
          />
          {/* <Route
            path="/voter-analytics/emails"
            element={<Emails client={client} user={user} authService={authService} service={statService} />}
          /> */}
          <Route
            path="/voter-analytics"
            element={<VoterAnalytics client={client} clientService={clientService} service={statService} user={user} authService={authService} />}
          />
          <Route
            path="/super"
            element={
              <Super clientService={clientService} authService={authService} userService={userService} authUser={user} />
            }
          />
          <Route
            path="/super/new"
            element={
              <CreateNewClient clientService={clientService} authService={authService} userService={userService} authUser={user} />
            }
          />
          <Route
            path="/error-page"
            element={
              <ErrorPage user={user} client={client} authService={authService} />
            }
          />
          <Route path="*" element={<NotFound />} /> {/* Add this line */}
        </Routes>
      );
    else if (isGuestAdmin)
      return (
        <Routes>
          <Route
            path="/answer-invite"
            element={<AnswerInvite questionId={questionIdFromSearch} />}
          />
          <Route path="*" element={<NotFound />} /> {/* Add this line */}
        </Routes>
      );
    else
      return (
        <Routes>
          <Route
            path="/answer-invite"
            element={<AnswerInvite questionId={questionIdFromSearch} />}
          />
          <Route path="*" element={<NotFound />} /> {/* Add this line */}
        </Routes>
      );
  };

  return (
    <div className={classes.App}>
      {isPdfExporting && <Loader title="Exporting your PDF" customId="pdfExport" absolute />}
      <div id="page">
        <HelmetProvider>
          <Helmet>
            <title>Admin | Rep'd</title>
            <meta
              name="viewport"
              content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"
            />
            {/* <meta http-equiv="refresh" content="1800" /> Reload the page every 30 minutes */}
          </Helmet>
        </HelmetProvider>

        {isAdmin && <Header client={client} />}

        <NotificationContextProvider>
          <PublishContextProvider service={clientService}>
            <AuthProvider token={token} user={user} client={client}>
              <ServiceProvider
                fileService={fileService}
                userService={userService}
                clientService={clientService}
                ngpVanService={ngpVanService}
                questionService={questionService}
                answersService={answersService}
                statService={statService}
                adminStatsService={adminStatsService}
              >
                {getRoutes()}
              </ServiceProvider>
            </AuthProvider>
          </PublishContextProvider>
        </NotificationContextProvider>
        <ToastContainer />
      </div>
    </div>
  );
}

export default App;
