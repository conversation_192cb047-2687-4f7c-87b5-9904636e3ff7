@import 'src/styles/variables';

.importingVisitors {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .title {
    font-size: 24px;
    font-weight: 700;
    margin: 19.92px 0;
  }

  .statusIcon {
    margin-top: 20px;
    margin-bottom: 40px;
    font-size: 80px;
  }

  .processingIcon {
    color: #444;
  }

  .checkIcon {
    color: $blue;
  }

  .errorIcon {
    color: #f44;
  }

  .description {
    text-align: center;
    font-size: 16px;
  }

  .fileName {
    font-weight: 700;

    &.processing {
      margin-bottom: 20px;
    }
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      &:first-child {
        margin-right: 10px;
      }

      margin-top: 25px;
      padding: 7px 35px;
    }
  }

  .importedUsers {
    margin-top: 20px;
    font-size: 16px;
    max-height: 150px;
    overflow: scroll;
    padding: 10px;
    background: #F0F0F0;
    border-radius: 10px;

    h3 { text-align: center; }

    ul {
      text-align: left;
    }
  }
}