import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFly } from "@fortawesome/free-brands-svg-icons";
import { faCheckCircle, faExclamationCircle, faSpinner } from "@fortawesome/free-solid-svg-icons";
import cn from "classnames"

import Modal from "shared/Modal";
import Button from "shared/Button";

import classes from "./ImportingVisitors.module.scss"

export default function ImportingVisitors(
  { handleClose, status, fileName, importedUsers }: {
    handleClose: () => void;
    status: "processing" | "completed" | "failed",
    fileName: string
    importedUsers?: any[]
  }
) {
  const statusContent = {
    processing: {
      title: "Processing Visitors...",
      subtitle: "Please remain on the page while we import your users",
      fileName,
      icon: faSpinner,
      iconClass: "spinnerIcon",
      button: false
    },
    failed: {
      title: "",
      subtitle: "We could not import all your users:",
      fileName,
      icon: faExclamationCircle,
      iconClass: "errorIcon",
      button: <Button text="Close" callback={handleClose} />

    },
    completed: {
      title: "",
      fileName,
      subtitle: `Congratulations! Your voter list has been uploaded successfully.`,
      icon: faCheckCircle,
      iconClass: "checkIcon",
      button: <Button text="Ok" callback={handleClose} />,
    }
  }

  const buttonText = status === "completed" ? "Ok" : "Close";
  const buttonClass = status === "completed" ? "primary" : "";

  return (
    <Modal contentWrapperStyle={{ maxWidth: "460px" }}>
      <div className={classes.importingVisitors}>
        {status === "processing" && (<h2 className={classes.title}>{statusContent[status].title}</h2>)}

        <FontAwesomeIcon
          icon={statusContent[status].icon}
          className={cn(classes.statusIcon, classes[statusContent[status].iconClass])}
          spin={status === "processing" ? true : false}
        />

        <div className={classes.description}>
          {statusContent[status].subtitle}
        </div>

        <p className={cn(classes.fileName, classes[statusContent[status].iconClass], status)}>
          {statusContent[status].fileName}
        </p>

        {status !== "processing" && (
          <div className={classes.buttonWrapper}>
            <Button text={buttonText} callback={handleClose} customClass={buttonClass} />
          </div>
        )}

        {importedUsers && (
          <div className={classes.importedUsers}>
            <h3><b>User Import List</b></h3>
            <ul>
              {importedUsers.slice(0, 200).map((user, i) => (
                <li key={i}>
                  <FontAwesomeIcon icon="envelope" />
                  {' '}
                  {user.firstName} {user.lastName} -
                  {' '}
                  {user.email || '[No Email Provided]'}
                </li>
              ))}
              {importedUsers.length > 200 && <li key={'default'}>Results truncated...</li>}
            </ul>
          </div>
        )}
      </div>
    </Modal>
  )
}