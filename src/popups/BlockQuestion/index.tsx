import Modal from 'shared/Modal';
import Button from 'shared/Button';

import classes from './BlockQuestion.module.scss';

interface BlockQuestionProps {
  isOpen: boolean;
  handleClose: () => void;
  handleBlock: () => void;
}

export default function BlockQuestion({
  isOpen,
  handleClose,
  handleBlock,
}: BlockQuestionProps) {

  return isOpen ? (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.BlockQuestion}>
        <h5>Block question and user</h5>

        <p>
          When blocking a user, their approved questions and answered questions will be unaffected
          but all their questions that are pending review will be moved to the bottom of this list
          and will not show in the public facing web app.
          <br /><br />
          The blocked user will not be able to ask any more questions.
        </p>

        <div className={classes.buttonWrapper}>
          <Button text="Cancel" callback={handleClose} />
          <Button text="Block" callback={handleBlock} />
        </div>
      </div>
    </Modal>
  ) : null;
}
