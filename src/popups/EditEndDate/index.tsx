import { useRef, useState } from 'react';

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import { AnswersService } from 'services';
import { AnswerInterface } from 'interfaces';

import classes from './index.module.scss';

interface EditEndDateProps {
  handleClose: () => void;
  answer: AnswerInterface;
  answersService: AnswersService;
  setAnswers: any;
}

export default function EditEndDate({
  handleClose,
  answer,
  answersService,
  setAnswers,
}: EditEndDateProps) {
  // format default date as yyyy-mm-dd
  const [value, setValue] = useState<string | null>((answer.endDate || '').toString().split('T')[0]);
  const ref = useRef<(HTMLInputElement | null)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleUpdate = (value: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      const updatedValue = value;

      setValue(updatedValue);
    }, 1000); // Adjust the delay as needed
  }

  const handleSave = () => {
    const payload = {
      id: answer.id,
      clientId: answer.clientId,
      endDate: value,
    }

    answersService.updateAnswer(payload, () => answersService.getAnswers(setAnswers)).then( () => {
      handleClose();
    }).catch((error: any) => {
      console.error('Error updating:', error);
    });
  };

  return (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.Content}>
        <div className={classes.title}>Set An End-Date</div>
        <div className={classes.bodySection}>
          <div className={classes.Item}>
            {value && <span className={classes.label}>{(new Date(value)).toLocaleDateString()}</span>}
            <input
              ref={(el) => ref.current[0] = el}
              type='date'
              value={value || ''}
              className={classes.input}
              contentEditable="true"
              placeholder="Enter an end date"
              onInput={(e) => handleUpdate((e.target as HTMLInputElement).value)}
            />
          </div>
          <div>On or after this date, the video will be hidden.</div>


          <div className={classes.buttonWrapper}>
            <Button text="Cancel" customClass={classes.button} callback={handleClose} />
            <Button text="Save" customClass={classes.button} callback={handleSave} />
          </div>
        </div>
      </div>
    </Modal>
  );
}