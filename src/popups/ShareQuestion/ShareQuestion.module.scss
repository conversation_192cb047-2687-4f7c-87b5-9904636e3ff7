@import 'styles/variables';

.shareQuestion {
  width: 100%;
  font-size: 16px;

  .title {
    margin-bottom: 10px;
    font-weight: 500;
    text-align: center;
  }

  .subTitle {
    font-size: 18px;
    color: $dark-blue;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 5px;
  }

  .clearDueDate {
    cursor: pointer;
  }

  .clearDueDate:hover {
    text-decoration: underline;
  }

  .bodySection {
    padding: 20px;

    .categoryForm {
      position: relative;
      display: flex;
      justify-content: start;
      align-items: center;
      flex-wrap: wrap;
      padding: 0 10px 10px;
      // min-height: 90px;
      background: white;
      border: 1px solid $offwhite;
      border-radius: 10px;
      font-size: 16px;

      .email {
        display: flex;
        align-items: center;
        margin-top: 10px;
        margin-right: 10px;
        padding: 5px 5px 5px 15px;
        background: $pale-blue;
        border-radius: 40px;
        color: $blue;

        .textInput {
          display: inline-block;
          vertical-align: middle;
          min-width: auto;
          background: transparent;
          border: none;
          border-radius: 40px;
          line-height: 1;
        }

        button {
          position: relative;
          top: -1px;
          margin-left: 10px;
          padding: 3px 8px 5px;
          border: 1px solid $blue;
          border-radius: 40px;
          font-size: 16px;
          color: $blue;
          line-height: 1;
          background: none;

          &:hover {
            color: $dark-blue;
            border-color: $dark-blue
          }
        }
      }
    }
  }

  textarea {
    width: 100%;
    height: 322px;
    max-height: 500px;
    border: 1px solid $offwhite;
    padding: 10px;
    font-size: 16px;
    border-radius: 5px;
    white-space: pre-line;
    font-weight: 500;
    color: black;
    font-family: monospace;

    &:focus {
      outline: $blue 2px dashed !important;
    }

    &::placeholder {
      color: $grey;
      font-weight: 500;
    }
  }

  .emailInputWrapper {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;

    input {
      padding: 5px 10px;
      border-radius: 5px;
      border: 1px solid $grey;
      appearance: none;
      font-size: 16px;
      width: 100%;
    }

    .hasError {
      border-color: red;
    }

    button {
      color: $blue;
      padding: 7px 15px;
      margin-left: 10px;

      &:hover {
        background-color: $pale-blue;
      }
    }

  }

  .errorText {
    color: red;
    margin-top: 5px;
    margin-left: 20px;
  }

  .buttonWrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      color: $blue;
      padding: 7px 35px;

      &:first-child {
        margin-right: 20px;
      }

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}

@media screen and (max-width: 640px) {
  .shareQuestion {
    .bodySection {
      textarea {
        padding: 10px;
      }
    }
  }
}