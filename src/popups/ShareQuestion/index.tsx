import { useState } from 'react';
import { Field, Form, FormikProvider, useFormik } from 'formik';
import * as Yup from 'yup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { ClientService, QuestionService } from 'services';
import { QuestionInterface } from 'interfaces';
import { useNotification } from "hooks";
import { useServiceContext } from "services/ServiceProvider";

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import classes from './ShareQuestion.module.scss';
import { DatePicker } from 'components/VoterAnalytics/DatePicker';
import tnClasses from "../../components/TeleprompterNotes/TeleprompterNotes.module.scss";

interface ShareQuestionProps {
  isOpen: boolean;
  handleClose: () => void;
  question: QuestionInterface;
  questionService?: QuestionService;
  clientService?: ClientService;
}

export default function ShareQuestion({
  isOpen,
  handleClose,
  question,
  questionService,
  clientService,
}: ShareQuestionProps) {
  const [invitees, setInvitees] = useState<string[]>([]);
  const [suggestedTeleprompterNotes, setSuggestedTeleprompterNotes] = useState<string>('');
  const [generated, setGenerated] = useState<false | 'generating' | true>(false);
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const { showAlert } = useNotification();
  const { adminStatsService } = useServiceContext();

  const { client } = clientService || {};

  const handleParseResponse = (response: any) => {
    setGenerated('generating');
    adminStatsService?.trackEvent('QuestionsArchive', 'generate_ai_response');

    clientService?.getAiResponse(question.text, (responseData: any) => {
      setSuggestedTeleprompterNotes(responseData);
      setGenerated(true);
      adminStatsService?.trackEvent('QuestionsArchive', 'ai_response_generated');
    })
  }

  const handleSendQuestion = () => {
    questionService?.addSuggestedNotesToQuestion({ id: question.id, suggestedNotes: suggestedTeleprompterNotes });
    adminStatsService?.trackEvent('QuestionsArchive', 'share_question');

    questionService?.shareQuestion({
      id: question.id,
      invitees: invitees,
      dueDate: dueDate ? dueDate.toISOString() : undefined
    }).then(() => {
      handleClose();
      showAlert("Link to answer question sent");
      adminStatsService?.trackEvent('QuestionsArchive', 'question_shared_success');
    })
  }

  function removeInvitee(event: any, index: number) {
    event.preventDefault();
    setInvitees(invitees.filter((_, i) => i !== index));
  }

  const emailValidationSchema = Yup.object().shape({
    email: Yup.string().email('Please Enter valid Email')
  });

  const validateEmail = (email: string) => invitees.includes(email) ? 'Email already added' : null;

  const emailFormik = useFormik({
    initialValues: { email: '' },
    validationSchema: emailValidationSchema,
    validateOnChange: false,
    onSubmit: (values, { resetForm }) => {
      if (values.email) {
        setInvitees(invitees => [...invitees, values.email]);
        resetForm();
      }
    }
  });

  return isOpen ? (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.shareQuestion}>
        <div className={classes.title}>Share Answer Link</div>
        <div className={classes.bodySection}>

          <div className={classes.subTitle}>Enter Emails</div>

          <div className={classes.categoryForm}>
            {invitees.map((invitee, index: number) => {
              return <div key={index} className={classes.email}>
                <div className={classes.textInput}>
                  {invitee}
                </div>
                <button onClick={(event) => removeInvitee(event, index)}>x</button>
              </div>
            })}
            <FormikProvider value={emailFormik}>
              <Form className={classes.emailInputWrapper}>
                <Field
                  type="email"
                  name="email"
                  placeholder="Enter email..."
                  validate={validateEmail}
                  className={emailFormik.errors.email && emailFormik.touched.email && classes.hasError}
                />
                <Button text="Add" callback={() => {
                  adminStatsService?.trackEvent('QuestionsArchive', 'add_email_to_share');
                  emailFormik.handleSubmit();
                }} />
              </Form>
            </FormikProvider>
          </div>
          {emailFormik.touched.email && emailFormik.errors.email && <div className={classes.errorText}>{emailFormik.errors.email}</div>}

          <div className={classes.subTitle}>
            Due Date
            {dueDate && (
              <span className={classes.clearDueDate} onClick={() => setDueDate(undefined)}> - Clear</span>
            )}
          </div>
          <div className={classes.dueDatePicker}>
            <DatePicker
              date={dueDate}
              setDate={setDueDate}
            />
          </div>

          <div className={classes.subTitle}>Enter Teleprompter Notes</div>

          <textarea
            placeholder="Enter Teleprompter Notes here..."
            onChange={(event) => setSuggestedTeleprompterNotes(event.target.value)}
            value={suggestedTeleprompterNotes}
          ></textarea>
          {/* {client?.aiQuestionsEnabled && ( */}
            <div className={tnClasses.aiBar}>
              <p>
                <FontAwesomeIcon icon={['fas', 'magic']} />
                Generate an answer to use as a script
              </p>

              <Button text={generated === 'generating' ? "Generating..." : generated === true ? "Re-Generate" : "Generate"} callback={handleParseResponse} />
            </div>
          {/* )} */}

          <div className={classes.buttonWrapper}>
            <Button text="Cancel" callback={() => {
              handleClose();
              adminStatsService?.trackEvent('QuestionsArchive', 'cancel_share_question');
            }} />
            <Button
              text="Send"
              customClass={invitees.length > 0 ? "" : "disabled"}
              callback={() => {
                if (invitees.length > 0) {
                  handleSendQuestion();
                  adminStatsService?.trackEvent('QuestionsArchive', 'send_share_question');
                }
              }}
            />
          </div>
        </div>
      </div>
    </Modal>
  ) : null;
}