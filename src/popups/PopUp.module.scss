@import 'src/styles/variables';

.PopUpOverlay {
    position: fixed;
    background-color: $transparent-blue;
    width: 100%;
    height: 100%;
    z-index: 1001;
    left: 0;
    top: 0;
    overflow-y: auto;
    backdrop-filter: blur(20px);

    &.opaque {
        background-color: $dark-blue;
        cursor: auto;
    }

    &.opaque.white {
        background-color: white;
        cursor: auto;

        .ContentWrapper {
            box-shadow: none;
        }
    }


    .ContentWrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 10vh auto 0 auto;
        padding: 30px;
        max-width: 450px;
        background-color: white;
        border-radius: 15px;
        box-shadow: 0 10px 20px 0 $transparent-black-low;
        cursor: auto;
    }
}

@media all and (max-height: 800px) and (min-width: 800px) {
    .PopUpOverlay {
        transform: scale(0.8);
        transform-origin: 0 0 0;
        width: 125%;
        height: 125%;
    }
}

@media (max-width: 640px) {
    .PopUpOverlay {
        background: white;

        .ContentWrapper {
            align-items: flex-start;
            margin: 0 auto;
            padding: 10px;
            max-width: calc(100% - 20px) !important;
            box-shadow: none;
            border-radius: 0;
        }
    }
}