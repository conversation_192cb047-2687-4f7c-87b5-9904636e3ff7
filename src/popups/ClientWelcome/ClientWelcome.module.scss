@import 'src/styles/variables';

.ClientWelcomehWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .title {
    font-size: 34px;
    color: $blue;
    font-weight: 200;
  }

  .logo {
    margin-bottom: 20px;
  }

  .description {
    text-align: center;
    font-size: 16px;
  }

  .inputWithLabel {
    display: flex;
    flex-direction: column;
    margin: 20px 0 10px 0;

    label {
      color: black;
      font-weight: 600;
    }

    input {
      padding: 10px 10px;
      border-radius: 5px;
      border: 1px solid $grey;
      appearance: none;
      font-size: 16px;
    }

    small {
      padding-top: 5px;
      padding-bottom: 10px;
    }
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      &:first-child {
        margin-right: 10px;
      }

      margin-top: 25px;
      padding: 5px 35px;
    }
  }
}