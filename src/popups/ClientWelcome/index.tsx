import cn from "classnames"
import { <PERSON>, FormikProvider, Form, useFormik } from "formik";
import * as Yup from "yup";
import { ToastContainer, toast } from 'react-toastify';

import { UserService } from 'services';

import Modal from "shared/Modal";
import Button from "shared/Button"

import logo from 'assets/logo/Logo-Full.png';

import FormClasses from 'shared/Forms/Forms.module.scss';
import classes from "./ClientWelcome.module.scss"

export default function ClientWelcome(props: { handleClose: Function, userService?: UserService }) {
  const { handleClose, userService } = props

  const passwordForm = useFormik({
    initialValues: { password: '', confirmation: '' },
    validationSchema: Yup.object().shape({
      password: Yup.string().required('Please enter a password'),
      confirmation: Yup.string().required('Please confirm your password')
    }),
    onSubmit: (values: any, actions: any) => {
      var invalid: boolean = false
      var error: string | null = null

      if (
        ![values.confirmation].includes(values.password) ||
        (values.password === '' && values.confirmation !== '') ||
        (values.password !== '' && values.confirmation === '')
      ) invalid = !invalid

      error = invalid ? 'Please enter both passwords so they match.' : null

      if (!invalid && values.password && values.confirmation) {
        userService?.update({
          password: values.password,
          passwordConfirmation: values.confirmation
        }, () => setTimeout(() => handleClose(), 500))
      }

      if (error) toast.error(error)
    }
  });

  const passFocus = passwordForm.touched,
    passErrors = passwordForm.errors;

  const errors = {
    password: passErrors.password && passFocus.password && FormClasses.hasError,
    confirmation: passErrors.confirmation && passFocus.confirmation && FormClasses.hasError
  }

  return (
    <Modal opaque={true}>
      <div className={classes.ClientWelcomehWrapper}>
        <img className={classes.logo} src={logo} alt="REPD logo" />

        <div className={classes.description}>
          <p>
            Confirmation was a success! Thanks again.<br />
            Please include a password.<br /><br />
          </p>
        </div>

        <div className={classes.passwordWrapper}>
          <FormikProvider key="passFormProvider" value={passwordForm}>
            <Form key="passForm" className={cn(FormClasses.Form, classes.campaignDetails)}>
              <div className={classes.inputWithLabel}>
                <label>Password</label>
                <Field type="password" name="password" className={errors.password || ''} />
                {errors.password && <div className={FormClasses.errorText}>Please enter a password</div>}
              </div>

              <div className={classes.inputWithLabel}>
                <label>Confirm Password</label>
                <Field type="password" name="confirmation" className={errors.confirmation || ''} />
                {errors.confirmation && <div className={FormClasses.errorText}>Please confirm your password</div>}
              </div>

              <div className={classes.buttonWrapper}>
                <Button text="Save" customClass="primary" callback={() => passwordForm.submitForm()} />
              </div>
            </Form>
          </FormikProvider>
        </div>
      </div>

      <ToastContainer position="top-center" />
    </Modal>
  )
}