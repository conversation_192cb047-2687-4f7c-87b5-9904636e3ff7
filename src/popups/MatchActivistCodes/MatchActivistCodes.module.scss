@import 'styles/variables';

.title {
  text-align: center;
  font-weight: 400;
  margin: 0px auto 20px auto;
}

.subTitle {
  color: $dark-blue;
  font-size: 18px;
  font-weight: 400;
  text-align: center;
  margin-bottom: 10px;
}

.matchActivistCodes {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  font-size: 16px;

  .categoriesList {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 10px;
    max-height: 800px;
    width: 300px;
    overflow-y: auto;
    background: white;
    border: 1px solid $offwhite;
    border-radius: 10px;

    .category {
      margin-bottom: 10px;
      padding: 5px 15px 5px 15px;
      border: 1px solid $pale-blue;
      border-radius: 30px;
      overflow-wrap: anywhere;
      width: fit-content;
      color: $blue;
      margin-right: 20px;
      cursor: pointer;

      svg {
        fill: none !important;
        stroke: $blue;
        stroke-width: 50px;
      }
    }

    .selected {
      background: $pale-blue;

      svg {
        fill: $blue !important;
        stroke: none;
        stroke-width: 0;
      }
    }

  }

  .matchedTo {
    align-self: center;
    margin: 20px;
  }

  .activistCodesList {
    .activistCode {
      margin-bottom: 10px;
      padding: 5px 10px 5px 10px;
      border: 1px solid $pale-blue;
      border-radius: 30px;
      overflow-wrap: anywhere;
      width: fit-content;
      color: $blue;
      display: flex;
      align-items: center;
      cursor: pointer;

      svg {
        height: 20px;
        width: 20px;
        margin-right: 5px;
        color: transparent;
        fill: none !important;
        stroke: $blue;
        stroke-width: 50px;
      }
    }

    .matched {
      background: $pale-blue;

      svg {
        color: $blue;
        fill: $blue !important;
        stroke: none;
        stroke-width: 0;
      }
    }
  }
}

.buttonWrapper {
  display: flex;
  justify-content: center;
  margin: 25px auto 0px auto;

  button {
    padding: 7px 35px;
    color: $blue !important;

    &:first-child {
      margin-right: 10px;
    }

    &:hover {
      background-color: $pale-blue;
    }
  }
}

@media (max-width: 640px) {
  .matchActivistCodes {
    flex-direction: column;

    .categoriesList {
      max-height: 400px;
      width: 100%;
    }

    .activistCodesList {
      max-height: 400px;
      width: 100%;
    }
  }
}