import {  useState } from 'react';
import { useNotification } from "hooks";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import cn from 'classnames';

import { ActivistCodeInterface, SyncActivistCodeInterface, ActivistCodeMatchInterface } from 'interfaces/ngpvan.interface'
import { NgpVanService } from 'services/index'

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import classes from './MatchActivistCodes.module.scss';

interface MatchActivistCodesProps {
  handleClose: () => void;
  clientId: string;
  categories: string[];
  activistCodes: ActivistCodeInterface[];
  ngpVanService: NgpVanService;
}

export default function MatchActivistCodes(props: MatchActivistCodesProps) {
  const { handleClose, clientId, categories, activistCodes, ngpVanService } = props;
  const { showAlert } = useNotification();

  const [selectedCategory, setSelectedCategory] = useState(categories[0]);
  const [updatedActivistCodes, setUpdatedActivistCodes] = useState<ActivistCodeInterface[]>(activistCodes.map(c => JSON.parse(JSON.stringify(c))));

  const handleMatchActivistCode = (code: ActivistCodeInterface) => {
    const codes: ActivistCodeInterface[] = [];

    updatedActivistCodes.forEach(c => {
      if (c === code) {
        c.categories.includes(selectedCategory) ?
          c.categories = c.categories.filter(c => c !== selectedCategory) :
          c.categories.push(selectedCategory);
      }
      codes.push(c);
    })

    setUpdatedActivistCodes(codes);
  }

  const handleSave = () => {
    const payload: SyncActivistCodeInterface[] = [];

    categories.forEach((category) => {
      let matchedCodes: ActivistCodeMatchInterface[] = [];

      updatedActivistCodes.forEach(code => {
        if (code.categories.includes(category))
          matchedCodes.push({ codeId: code.activistCodeId, code: code.name });
      });
      payload.push({ category: category, activistCodes: matchedCodes })
    });

    ngpVanService.syncActivistCodes({ data: payload, clientId: clientId }).then(
      showAlert("Matching updated")
    );

    handleClose();
  }

  return (
    <Modal contentWrapperStyle={{ maxWidth: "860px" }}>
      <h5 className={classes.title}>Match Activist Codes</h5>
      <div className={classes.matchActivistCodes}>
        <div>
          <h6 className={classes.subTitle}>Categories</h6>
          <div className={classes.categoriesList}>

            {categories.map((category) => {
              return <div
                key={category}
                className={cn(classes.category, category === selectedCategory && classes.selected)}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </div>
            })}
          </div>
        </div>

        <div className={classes.matchedTo}>
          Matched to
        </div>

        <div>
          <h6 className={classes.subTitle}>Activist Codes</h6>
          <div className={classes.activistCodesList}>
            {updatedActivistCodes.map((activistCode) => {
              return <div
                key={activistCode.activistCodeId}
                className={cn(classes.activistCode, activistCode.categories.includes(selectedCategory) && classes.matched)}
                onClick={() => handleMatchActivistCode(activistCode)}
              >
                <FontAwesomeIcon icon={activistCode.categories.includes(selectedCategory) ? "check-circle" : "circle"} />
                {activistCode.name}
              </div>
            })}
          </div>
        </div>
      </div>

      <div className={classes.buttonWrapper}>
        <Button text="Cancel" callback={handleClose} />
        <Button text="Save" callback={handleSave} />
      </div>
    </Modal>
  )
}