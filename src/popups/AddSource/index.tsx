import React, { useState } from 'react';
import Modal from 'shared/Modal';
import Button from 'shared/Button';
import { useServiceContext } from 'services/ServiceProvider';
import cn from 'classnames';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faTimes } from '@fortawesome/free-solid-svg-icons';

import classes from './AddSource.module.scss';

interface AddSourceProps {
  isOpen: boolean;
  handleClose: () => void;
}

type SourceType = 'Upload Document' | 'Add Single Page' | 'Add New Website' | 'Add Overriding Source' | null;

export default function AddSource({
  isOpen,
  handleClose,
}: AddSourceProps) {
  const { adminStatsService } = useServiceContext();
  const [selectedSource, setSelectedSource] = useState<SourceType>(null);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleButtonClick = (sourceType: SourceType) => {
    setSelectedSource(sourceType);
    if (sourceType) {
      adminStatsService?.trackEvent('AIPanel', `select_${sourceType.toLowerCase().replace(/ /g, '_')}`);
    }
  };

  const handleContinue = () => {
    if (selectedSource) {
      adminStatsService?.trackEvent('AIPanel', 'continue_add_source');

      if (currentStep === 1) {
        setCurrentStep(2);
      } else {
        // Here you would handle the specific action based on the selected source type
        console.log(`Continuing with selected source type: ${selectedSource}`);
        if (selectedFile) {
          console.log(`Selected file: ${selectedFile.name}`);
          // Here you would handle the file upload
        }
        handleClose();
      }
    }
  };

  const handleBack = () => {
    setCurrentStep(1);
    setSelectedFile(null);
    adminStatsService?.trackEvent('AIPanel', 'back_to_source_selection');
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
      adminStatsService?.trackEvent('AIPanel', 'file_selected');
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
      adminStatsService?.trackEvent('AIPanel', 'file_dropped');
    }
  };

  const handleDragAreaClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const getDescriptionText = () => {
    switch (selectedSource) {
      case 'Upload Document':
        return "This document will be used to supply information when formatting answers back to user question in the guide.";
      case 'Add Single Page':
        return "Adds a new single page on top of the main website to be scraped.";
      case 'Add New Website':
        return "Adds a new website on top of the main website to be scraped.";
      case 'Add Overriding Source':
        return "Adds text to override existing sources.";
      default:
        return "Please select a source type to continue.";
    }
  };

  const renderStep1 = () => (
    <>
      <div className={classes.titleWithClose}>
        <h2 className={classes.title}>Add Source</h2>
        <button className={classes.closeButton} onClick={handleCloseClick}>
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>

      <div className={classes.buttonGrid}>
        <button
          className={cn(
            classes.sourceButton,
            selectedSource === 'Upload Document' && classes.selected
          )}
          onClick={() => handleButtonClick('Upload Document')}
        >
          Upload Document
        </button>

        <button
          className={cn(
            classes.sourceButton,
            selectedSource === 'Add Single Page' && classes.selected
          )}
          onClick={() => handleButtonClick('Add Single Page')}
        >
          Add Single Page
        </button>

        <button
          className={cn(
            classes.sourceButton,
            selectedSource === 'Add New Website' && classes.selected
          )}
          onClick={() => handleButtonClick('Add New Website')}
        >
          Add New Website
        </button>

        <button
          className={cn(
            classes.sourceButton,
            selectedSource === 'Add Overriding Source' && classes.selected
          )}
          onClick={() => handleButtonClick('Add Overriding Source')}
        >
          Add Overriding Source
        </button>
      </div>

      <p className={classes.description}>
        {getDescriptionText()}
      </p>

      <div className={classes.buttonWrapper}>
        <Button
          text="Continue"
          callback={handleContinue}
          customClass={!selectedSource ? "disabled" : ""}
        />
      </div>
    </>
  );

  const renderStep2 = () => (
    <>
      <div className={classes.titleWithBackAndClose}>
        <button className={classes.backButton} onClick={handleBack}>
          <FontAwesomeIcon icon={faArrowLeft} />
        </button>
        <h2 className={classes.title}>{selectedSource}</h2>
        <button className={classes.closeButton} onClick={handleCloseClick}>
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>

      <div className={classes.uploadArea}>
        <div
          className={cn(
            classes.dragDropArea,
            selectedFile && classes.hasFile
          )}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleDragAreaClick}
        >
          {selectedFile ? (
            <p>Selected file: {selectedFile.name}</p>
          ) : (
            <p>Drag or select a document to upload</p>
          )}
          <input
            type="file"
            className={classes.fileInput}
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf,.docx,.xlsx,.csv,.txt"
          />
        </div>
        <p className={classes.fileTypes}>PDF, DOCX, Excel, CSV, & TXT accepted.</p>
      </div>

      <p className={classes.description}>
        {getDescriptionText()}
      </p>

      <div className={classes.buttonWrapper}>
        <Button
          text="Continue"
          callback={handleContinue}
          customClass={!selectedFile ? "disabled" : ""}
        />
      </div>
    </>
  );

  const handleCloseClick = () => {
    adminStatsService?.trackEvent('AIPanel', 'close_add_source');
    handleClose();
  };

  return isOpen ? (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.addSource}>
        {currentStep === 1 ? renderStep1() : renderStep2()}
      </div>
    </Modal>
  ) : null;
}
