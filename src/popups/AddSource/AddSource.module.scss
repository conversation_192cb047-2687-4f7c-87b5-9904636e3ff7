@import 'styles/variables';

.addSource {
  width: 100%;
  text-align: center;
  padding: 20px;
  position: relative;

  .closeButton,
  .backButton {
    background: none;
    border: none;
    color: $dark-blue;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      color: $blue;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    color: $dark-blue;
  }

  .titleWithClose,
  .titleWithBackAndClose {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 30px;
  }

  .titleWithClose {
    justify-content: center;

    .title {
      margin-bottom: 0;
    }

    .closeButton {
      position: absolute;
      right: 0;
    }
  }

  .titleWithBackAndClose {
    justify-content: space-between;

    .title {
      margin-bottom: 0;
      flex-grow: 1;
      text-align: center;
    }

    .backButton {
      margin-right: auto;
    }

    .closeButton {
      margin-left: auto;
    }
  }

  .buttonGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;

    .sourceButton {
      height: 120px;
      background-color: white;
      border: 1px solid $grey;
      border-radius: 10px;
      font-size: 18px;
      font-weight: 500;
      color: $dark-blue;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15px;
      position: relative;
      overflow: hidden;

      &:hover {
        background-color: $pale-blue;
        border-color: $blue;
      }

      &:active {
        transform: scale(0.98);
      }

      &.selected {
        background-color: $dark-blue;
        color: white;
        border-color: $dark-blue;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .uploadArea {
    margin-bottom: 30px;

    .dragDropArea {
      border: 2px dashed $grey;
      border-radius: 10px;
      padding: 60px 30px;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &:hover {
        border-color: $blue;
        background-color: $pale-blue;
      }

      &.hasFile {
        border-color: $mint-green;
        background-color: $pale-mint-green;

        p {
          color: $dark-mint-green;
          font-weight: 500;
        }
      }

      p {
        margin: 0;
        font-size: 18px;
        color: $dark-blue;
      }

      .fileInput {
        width: 0.1px;
        height: 0.1px;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        z-index: -1;
      }
    }

    .fileTypes {
      font-size: 14px;
      color: $dark-grey;
      margin: 10px 0 0 0;
    }
  }

  .description {
    margin: 0 auto;
    max-width: 500px;
    color: $dark-grey;
    line-height: 1.5;
    font-size: 16px;
    min-height: 60px; // Ensure consistent height regardless of text length
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .addSource {
    .buttonGrid {
      grid-template-columns: 1fr;

      .sourceButton {
        height: 80px;
      }
    }

    .description {
      min-height: 80px; // Adjust for mobile
    }

    .titleWithClose,
    .titleWithBackAndClose {
      padding: 0 10px;
    }

    .uploadArea {
      .dragDropArea {
        padding: 20px;
      }
    }
  }
}