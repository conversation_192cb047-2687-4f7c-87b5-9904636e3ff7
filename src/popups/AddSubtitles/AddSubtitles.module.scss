@import 'styles/variables';

.addSubtitles {
  width: 100%;
  text-align: center;
  font-size: 16px;

  .title {
    padding-bottom: 20px;
    margin-bottom: 10px;
    font-weight: 500;
  }

  .subTitle {
    font-size: 18px;
    color: $dark-blue;
    font-weight: 400;
  }

  .bodySection {
    padding: 20px;

    textarea {
      width: 100%;
      height: 322px;
      max-height: 500px;
      border: 1px solid $offwhite;
      padding: 10px;
      font-size: 16px;
      border-radius: 5px;
      white-space: pre-line;
      font-weight: 500;
      color: black;
      font-family: monospace;
      outline: $grey 2px dashed;

      &:focus {
        outline: $blue 2px dashed !important;
      }

      &::placeholder {
        color: $grey;
        font-weight: 500;
      }
    }

  }

  .buttonWrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      color: $blue;
      padding: 7px 35px;

      &:first-child {
        margin-right: 20px;
      }

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}

@media screen and (max-width: 640px) {
  .addSubtitles {
    .bodySection {
      textarea {
        padding: 10px;
      }
    }
  }
}