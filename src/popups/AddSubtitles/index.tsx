import { useState } from 'react';

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import { AnswersService } from 'services';

import classes from './AddSubtitles.module.scss';

interface AddSubtitlesProps {
  handleClose: () => void;
  subtitles: string;
  answerId: string;
  clientId: number;
  answersService: AnswersService;
  setAnswers: any;
}

export default function AddSubtitles({
  handleClose,
  subtitles,
  answerId,
  clientId,
  answersService,
  setAnswers,
}: AddSubtitlesProps) {
  const [subtitle, setSubtitle] = useState(subtitles)

  const handleSubtitleSave = () => {
    const payload = {
      id: answerId,
      clientId,
      subtitles: subtitle,
    }

    answersService.updateAnswer(payload, () => answersService.getAnswers(setAnswers)).then(
      handleClose
    )
  };

  return (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.addSubtitles}>
        <div className={classes.title}>Add Subtitles</div>
        <div className={classes.bodySection}>
          <textarea value={subtitle} placeholder="Enter subtitles..."
            onChange={(e) => setSubtitle(e.target.value)}
          ></textarea>

          <div className={classes.buttonWrapper}>
            <Button text="Cancel" customClass={classes.button} callback={handleClose} />
            <Button text="Save" customClass={classes.button} callback={handleSubtitleSave} />
          </div>
        </div>
      </div>
    </Modal>
  )
}