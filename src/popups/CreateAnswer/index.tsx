import React, { useState } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import cn from "classnames";

import { FileService, QuestionService } from "services";
import { useNotification } from "hooks";
import * as interfaces from "interfaces";
import { useServiceContext } from "services/ServiceProvider";

import Modal from "shared/Modal";
import Button from "shared/Button";
import Icons, { getIconByType } from "shared/Icons";

import classes from "./CreateAnswer.module.scss";

interface PopUpProps {
  isOpen: boolean;
  handleClose: () => void;
  refetchQuestionList: () => void;
  service?: QuestionService;
  client?: interfaces.ClientInterface;
  user?: interfaces.UserInterface;
  fileService?: FileService;
}

export default function CreateAnswer({
  isOpen,
  handleClose,
  client,
  refetchQuestionList,
  service,
  user,
  fileService,
}: PopUpProps) {
  const [questionCharacterCount, setQuestionCharacter] = useState(0);
  const { adminStatsService } = useServiceContext();

  const { showAlert } = useNotification();
  const categories = client?.categories
    ?.split(",")
    .map((c: string) => c.trim());

  const handleApproval = (id: string, resetForm: () => void) => {
    const payload = {
      id,
      isApproved: true,
      isDenied: false,
    };

    service
      ?.approveQuestion(payload, () => { })
      .then((question: any) => {
        if (question.enabled) {
          refetchQuestionList();
          if (question.id) {
            handleClose();
            resetForm();
          }
        }
      });
  };

  const createAnswerSuccess = (item: any, resetForm: () => void) => {
    showAlert("Question created successfully");
    handleApproval(item.id, resetForm);
    adminStatsService?.trackEvent('QuestionsArchive', 'create_question_success');
    // service?.getQuestions(setQuestions);
  };

  const formik = useFormik({
    initialValues: {
      question: "",
      category: "",
      overridingName: "",
    },

    validationSchema: Yup.object().shape({
      question: Yup.string()
        .required("Question is required")
        .min(2, "Question length must be at least 2 characters long"),
      category: Yup.string().required("Category is Required"),
      overridingName: Yup.string().max(100, "Name must be 100 characters or less"),
    }),

    onSubmit: (values, { resetForm }) => {
      const payload = {
        clientId: client?.id,
        userId: user?.id,
        text: values.question,
        category: values.category,
        categoryIcon: getIconByType(values.category),
        generated: false,
        votes: 1,
        asked: true,
        ...(values.overridingName && { overridingName: values.overridingName }),
      };

      adminStatsService?.trackEvent('QuestionsArchive', 'submit_create_question_form');

      service
        ?.createQuestion(payload, () => { })
        .then((response: any) => {
          if (response.enabled) {
            createAnswerSuccess(response, resetForm);
          }
        }).catch((error: any) => {
          const errorMessage = error.message || "Your question could not be saved at this time";
          showAlert(errorMessage);
        });
    },
  });

  const {
    values,
    setFieldValue,
    handleSubmit,
    errors,
    touched,
    handleBlur,
    resetForm,
  } = formik;

  return (
    <div>
      {isOpen ? (
        <Modal>
          <div className={classes.createAnswer}>
            <h5>What question are you answering?</h5>
            <FormikProvider value={formik}>
              <Form>
                <div className={classes.inputWithLabel}>
                  <label htmlFor="question">
                    Question{" "}
                    <span className={classes.counter}>
                      {255 - questionCharacterCount} characters left
                    </span>
                  </label>

                  <textarea
                    rows={4}
                    id="question"
                    name="question"
                    maxLength={255}
                    className={cn(
                      touched.question && errors.question && classes.hasError
                    )}
                    value={values.question}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      setFieldValue("question", e.target.value);
                      setQuestionCharacter(e.target.value.length);
                    }}
                  ></textarea>

                  {touched.question && errors.question && (
                    <div className={cn(classes.errorText)}>
                      {errors.question}
                    </div>
                  )}
                </div>

                <div className={classes.inputWithLabel}>
                  <label htmlFor="overridingName">
                    Name (optional)
                  </label>

                  <input
                    type="text"
                    id="overridingName"
                    name="overridingName"
                    maxLength={100}
                    className={cn(
                      touched.overridingName && errors.overridingName && classes.hasError
                    )}
                    value={values.overridingName}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      setFieldValue("overridingName", e.target.value);
                    }}
                    placeholder="Enter a custom name for this question"
                    style={{ width: '100%' }}
                  />

                  {touched.overridingName && errors.overridingName && (
                    <div className={cn(classes.errorText)}>
                      {errors.overridingName}
                    </div>
                  )}
                </div>

                <div className={classes.inputWithLabel}>
                  <div className={classes.selectArrow}>
                    <Icons
                      iconType="downChevron"
                      iconClass={classes.downChevron}
                    />
                  </div>

                  <label htmlFor="category">Category</label>
                  <div className={classes.categorySelect}>
                    <label htmlFor="category" className={classes.inset}>
                      {/* TODO: Replace with FontAwesome */}
                      <Icons iconType={values.category} />
                    </label>

                    <select
                      name="category"
                      id="category"
                      className={cn(classes.outset)}
                      onChange={(e) => {
                        setFieldValue("category", e.target.value);
                      }}
                    >
                      <option value=""></option>

                      {categories?.map((category: any) => (
                        <option value={category} key={category}>
                          {" "}
                          {category}{" "}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {touched.category && errors.category && (
                  <div className={cn(classes.errorText)}>{errors.category}</div>
                )}
              </Form>
            </FormikProvider>

            <div className={classes.buttonGroup}>
              <Button
                text="Cancel"
                callback={() => {
                  handleClose();
                  resetForm();
                  adminStatsService?.trackEvent('QuestionsArchive', 'cancel_create_question');
                }}
              />
              <Button text="Ok" callback={() => {
                adminStatsService?.trackEvent('QuestionsArchive', 'ok_create_question');
                handleSubmit();
              }} />
            </div>
          </div>
        </Modal>
      ) : null}
    </div>
  );
}
