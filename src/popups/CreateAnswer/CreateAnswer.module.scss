@import 'src/styles/variables';

.createAnswer {
  width: 100%;

  h5 {
    width: 100%;
    font-size: 18px;
    margin: 0 0 15px;
    color: $blue;
    text-align: left;
    font-weight: 400;
  }

  form {
    display: flex;
    flex-direction: column;
    font-size: 16px;

    .inputWithLabel {
      margin-bottom: 5px;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .hasError {
        border-color: #f00;
      }

      input {
        height: 40px;
      }

      label {
        display: block;
        color: $dark-grey;

        .counter {
          float: right;
        }
      }
    }

    textarea {
      appearance: none;
      width: 100%;
      min-height: 80px;
      max-height: 300px;
      color: #000;
      border-radius: 5px;
      border: 1px solid $grey;
      padding: 10px;
      font-size: 16px;
      line-height: 20px;

      &.invalid {
        border-color: red;
      }

      &:focus {
        outline: $blue 2px dashed !important;
      }
    }

    input {
      padding: 10px 10px;
      // min-width: 100%;
      color: #000;
      border-radius: 5px;
      border: 1px solid $grey;
      appearance: none;
      font-size: 16px;

      &.invalid {
        border-color: red;
      }

      &:focus {
        outline: $blue 2px dashed !important;
      }

      &[type='checkbox'] {
        margin-right: 10px;
        min-width: auto;
        appearance: checkbox;
      }
    }

    .error {
      color: red;
    }

    select {
      // position: relative;
      border-radius: 5px;
      border: 1px solid $grey;
      padding: 10px 10px;
      font-size: 16px;
      color: #000;
      // color: #dddddd;
      // min-width: 380px;
      appearance: none;
      width: 100%;
      // height: 39px;
      box-sizing: content-box;
      cursor: pointer;

      &:focus {
        outline: $blue 2px dashed !important;
      }
    }

    .errorText {
      color: #f00;
    }

    .selectArrow {
      display: block;
      right: 0;
      bottom: 0;
      right: 15px;
      bottom: 10px;
      position: absolute;
      z-index: 1;
      pointer-events: none;
      cursor: pointer;
      color: $blue;

      .downChevron {
        height: 16px;
        width: 14px;
      }
    }

    label.inset {
      height: 39px;
      min-width: 42px;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      margin-right: -3px;
      border: 1px solid #eee;
      background-color: #eee;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;

      .scroll {
        line-height: 1;
      }
    }

    .outset {
      // min-width: 320px;
      width: 100%;
      // height: 37px;
      padding: 0 10px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;

      &::before {
        content: '\f078';
      }
    }
  }

  form.visitorName {
    margin-top: 20px;

    label {
      small {
        font-size: smaller;
      }
    }

    input {
      width: 100%;
    }
  }

  .buttonGroup {
    display: flex;
    width: 100%;
    margin-top: 25px;

    button {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;

      &:first-child {
        background-color: #fff;
        border: 1px solid $light-grey;
        color: $blue;
        max-width: 121px;

        &:hover {
          background-color: $pale-blue;
        }
      }

      &:nth-child(2) {
        margin-left: 10px;
        display: flex;
        background-color: $blue;
        color: #fff;

        &:hover {
          background-color: $dark-blue;
        }
      }
    }
  }

  .categorySelect {
    display: flex;
  }
}

.bookOpen {
  background-color: #eeee9b;
  color: rgba(0, 0, 0, 0.533);
}

.tree {
  background-color: #008013;
}

.child {
  background-color: #87ceeb;
  color: #00000088;
}

.heartbeat {
  background-color: #f47174;
}

.moneyBill {
  background-color: #00008b;
  color: #fff;
}

.bus {
  background-color: #ffb8bf;
  color: rgba(0, 0, 0, 0.533);
}

.gavel {
  background-color: #bfb9fa;
  color: rgba(0, 0, 0, 0.533);
}

.home {
  background-color: #ffb347;
}

.handshake {
  background-color: #3eb489;
  color: rgba(0, 0, 0, 0.533);
}

.passport {
  background-color: #4169e1;
}

.mobile {
  background-color: #a9a9a9;
}

.shield {
  background-color: #a0522d;
}

.flag {
  background-color: #d3d3d3;
  color: rgba(0, 0, 0, 0.533);
}

.microphone {
  background-color: #30d5c8;
  color: rgba(0, 0, 0, 0.533);
}

.comment {
  background-color: #f0f0f0;
  color: rgba(0, 0, 0, 0.533);
}