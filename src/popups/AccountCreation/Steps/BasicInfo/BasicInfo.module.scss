@import 'styles/variables';

.BasicInfo {
  width: 100%;

  .title {
    position: relative;
    display: flex;
    color: $blue;
    font-size: 18px;
    font-weight: 300;
    margin: 40px 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid $blue;
  }

  .ngpInformation {
    margin-top: 20px;

    a {
      color: $blue;
    }
  }

  .inputWithLabel {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin: 20px 0 10px 0;

    label {
      color: black;
      font-weight: 600;
    }

    input {
      width: 100%;
      padding: 10px 10px;
      border-radius: 5px;
      border: 1px solid $grey;
      appearance: none;
      font-size: 16px;
    }

    small {
      padding-top: 5px;
      padding-bottom: 10px;
    }
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      &:first-child {
        margin-right: 10px;
      }

      margin-top: 25px;
      padding: 5px 35px;
    }
  }

}