import { Field, FormikProvider, Form, useFormik } from 'formik';
import * as Yup from 'yup';
import { toast } from 'react-toastify';

import { ClientService, UserService } from 'services';
import { ClientInterface } from 'interfaces/client.interfaces'

import Button from 'shared/Button'

import FormClasses from 'shared/Forms/Forms.module.scss';
import classes from './BasicInfo.module.scss'
import React from "react";

const updateClientLink = function (c: ClientInterface): string {
  const pathname: string = c.name.toString().trim().replace(/[^a-z0-9]+/ig, '-')

  const subdomain = window.location.host.match('localhost') ? `staging` : (
    window.location.host.match('staging') ? `staging` : (
      `app`
    )
  );

  c.link = `https://${subdomain}.repd.us/${pathname}`

  return c.link
}

export default function BasicInfo(props: { clientService: ClientService, userService: UserService, handleNext: Function }) {
  const { clientService, userService, handleNext } = props;

  let client: ClientInterface | any = clientService.client

  function onChangeField(event: any) {
    if (['email', 'name'].includes(event.target.name) && !event.target.value) {
      event.target.value = client[event.target.name]
      return;
    }

    updateClient({
      name: event.target.name,
      value: event.target.value
    })
  }

  function updateClient({ name, value }: { name: string, value: string }) {
    client[name] = value;
    client.link = updateClientLink(client);
  }

  const basicInfoForm = useFormik({
    initialValues: { password: '', confirmation: '' },
    validationSchema: Yup.object().shape({
      password: Yup.string().required('Please enter a password'),
      confirmation: Yup.string().required('Please confirm your password')
    }),
    onSubmit: (values: any, actions: any) => {
      let invalid: boolean = false;
      let error: string | null = null;

      if (
        ![values.confirmation].includes(values.password) ||
        (values.password === '' && values.confirmation !== '') ||
        (values.password !== '' && values.confirmation === '')
      ) invalid = !invalid;

      error = invalid ? 'Please enter both passwords so they match.' : null;

      clientService.update(client);

      if (!invalid && values.password && values.confirmation) {
        userService?.update({
          password: values.password,
          passwordConfirmation: values.confirmation
        }, handleNext());
      }

      if (error) toast.error(error);
    }
  });

  const passFocus = basicInfoForm.touched,
    passErrors = basicInfoForm.errors;

  const errors = {
    password: passErrors.password && passFocus.password && FormClasses.hasError,
    confirmation: passErrors.confirmation && passFocus.confirmation && FormClasses.hasError
  }

  return (
    <div className={classes.BasicInfo}>
      <div className={classes.title}>
        1. Basic Info
      </div>

      <FormikProvider key="basicInfoFormProvider" value={basicInfoForm}>
        <Form key="basicInfoForm" className={FormClasses.Form}>
          <div className={FormClasses.SideBySide}>
            <div className={classes.inputWithLabel}>
              <label>{client.clientType === 'Government' ? 'Full City Name*' : 'Official Campaign Name*'}</label>
              <input type="text" name="name" defaultValue={client.name} onChange={onChangeField} />
            </div>

            <div className={classes.inputWithLabel}>
              <label>{client.clientType === 'Government' ? 'Admin Email Address*' : 'Campaign Email Address*'}</label>
              <input type="email" name="email" defaultValue={client.email} onChange={onChangeField} />
            </div>
          </div>

          <div className={FormClasses.SideBySide}>
            <div className={classes.inputWithLabel}>
              <label>Password</label>
              <Field type="password" name="password" className={errors.password || ''} />
              {errors.password && <div className={FormClasses.errorText}>Please enter a password</div>}
            </div>

            <div className={classes.inputWithLabel}>
              <label>Confirm Password</label>
              <Field type="password" name="confirmation" className={errors.confirmation || ''} />
              {errors.confirmation && <div className={FormClasses.errorText}>Please confirm your password</div>}
            </div>
          </div>
          {client.type === 'Campaign' && <div className={FormClasses.SideBySide}>
            <div className={classes.inputWithLabel}>
              <label>Votebuilder Username</label>
              <input type="text" name="ngpVanUsername" placeholder="••••" defaultValue={client.ngpVanUsername} onChange={onChangeField} />
            </div>
            <div className={classes.inputWithLabel}>
              <label>Votebuilder API Key</label>
              <input type="text" name="ngpVanApiKey" placeholder="••••-••••-••••" defaultValue={client.ngpVanApiKey} onChange={onChangeField} />
            </div>
          </div>}
        </Form>

        {client.type === 'Campaign' && <div className={classes.ngpInformation}>
          To request an API key with the correct access enabled check out our instructions
          <a href="https://help.ngpvan.com/van/s/article/2969508-requesting-and-approving-api-keys" target="_blank" rel="noreferrer"> here</a>
          .
        </div>}

        <div className={classes.buttonWrapper}>
          <Button text="Next" customClass="primary" callback={() => basicInfoForm.submitForm()} />
        </div>
      </FormikProvider>

    </div>
  )
}