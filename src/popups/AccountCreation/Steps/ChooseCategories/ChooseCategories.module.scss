@import 'styles/variables';

.ChooseCategories {
  width: 100%;
  padding: 30px;

  .title {
    position: relative;
    display: flex;
    color: $blue;
    padding-bottom: 10px;
    border-bottom: 1px solid $blue;
    font-size: 18px;
    font-weight: 300;

    button {
      position: absolute;
      border: none;
      background: none;
      left: -45px;
      bottom: 5px;

      &:hover {
        color: $blue;
      }

      svg {
        height: 20px;
        width: 20px;
      }
    }
  }

  .subTitle {
    display: flex;
    margin: 30px 0 15px 0;
    color: $blue;
    padding-bottom: 5px;
    border-bottom: 1px solid $blue;
    font-size: 16px;
    font-weight: 300;

    p {
      width: 60px;
      margin-right: 20px;
    }
  }

  .categories {
    max-height: 600px;
    overflow-y: auto;
    margin-bottom: 10px;

    .category {
      margin-bottom: 10px;
      padding-bottom: 5px;
      border-bottom: 1px solid $pale-blue;
      display: flex;
      align-items: center;
      cursor: pointer;

      span {
        width: 80px;
        display: flex;
        align-items: center;
        color: $blue;

        svg {
          height: 20px;
          width: 20px;
          margin-right: 5px;
        }
      }

      .disabledIcon {
        height: 20px;
        width: 20px;
        margin-right: 5px;
        color: white;
        border: solid 1px $blue;
        border-radius: 10px;
      }

    }
  }

  .matchActivistCodes {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    padding: 10px 10px 20px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border: 1px solid $offwhite;
    border-radius: 10px;
    font-size: 16px;

    .match {
      display: flex;
      flex-direction: row;
      align-items: center;

      .categoryWrapper {
        min-width: 200px;
        width: 200px;
        border-right: 1px solid $offwhite;
        padding: 5px;
        align-self: stretch;

        .category {
          margin: 5px 10px 5px 0px;
          padding: 5px 15px 5px 15px;
          overflow-wrap: anywhere;
          background: $pale-blue;
          border-radius: 30px;
          color: $blue;
          width: fit-content;
          text-align: center;

        }
      }

      .activistCodesWrapper {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 5px;

        .activistCode {
          margin: 5px 0px 5px 10px;
          padding: 5px 15px 5px 15px;
          border: 1px solid $blue;
          border-radius: 30px;
          color: $blue;
        }
      }
    }
  }

  .activistCodes {
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    padding: 10px 10px 20px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border: 1px solid $offwhite;
    border-radius: 10px;
    font-size: 16px;
    flex-direction: row;
    flex-wrap: wrap;

    .activistCode {
      margin: 5px 10px 5px 0px;
      padding: 5px 15px 5px 15px;
      background: $pale-blue;
      border-radius: 40px;
      color: $blue;
    }
  }

  p {
    text-align: center;
    margin: 5px;
  }

  .updateButton {
    display: flex;
    justify-content: flex-end;
    margin-top: 5px;
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
    margin-top: 15px;
  }


}

@media (max-width: 640px) {
  .ChooseCategories {
    .matchActivistCodes {
      .match {
        flex-direction: column;
        align-items: flex-start;
        border-bottom: 1px solid $offwhite;

        &:last-child {
          border-bottom: none;
        }

        .categoryWrapper {
          border-right: none;
        }
      }
    }
  }
}