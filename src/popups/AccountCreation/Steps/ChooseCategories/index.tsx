import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useNotification } from 'hooks';

import { ClientService, NgpVanService } from 'services';
import { ClientInterface } from 'interfaces/client.interfaces'

import Button from 'shared/Button'

import classes from './ChooseCategories.module.scss'
import { ActivistCodesInterface, ActivistCodeInterface } from 'interfaces/ngpvan.interface';
import MatchActivistCodes from 'popups/MatchActivistCodes';

const DEFAULT_ACTIVIST_CODES = ["Repd User", "Repd Click Volunteer", "Repd Click Donate"];

export default function ChooseCategories(props: { setClient: Function, clientService: ClientService, ngpVanService: NgpVanService, handleBack: Function }) {
  const { setClient, clientService, ngpVanService, handleBack } = props;

  const navigate = useNavigate();

  let client: ClientInterface | any = clientService.client;
  const { showAlert } = useNotification();
  const categories: string[] = (client.categories || '').replace(/\s+/g, ' ')
    .replace(/, /g, ',')
    .replace(/ ,/g, ',')
    .replace(/^ /, '')
    .replace(/ $/, '').split(',');

  const [updatedCategories, setUpdatedCategories] = useState(categories);
  const [activistCodes, setActivistCodes] = useState<ActivistCodeInterface[]>([]);
  const [matchActivistCodesOpen, setMatchActivistCodesOpen] = useState(false);
  const matchedCategories = updatedCategories.filter(c => activistCodes.find(a => a.categories.includes(c)));

  const handleSelectCategory = (category: string) => {
    if (updatedCategories.includes(category))
      setUpdatedCategories(updatedCategories.filter((c) => c !== category));
    else
      setUpdatedCategories(updatedCategories => [...updatedCategories, category]);
  }

  const handleCreateAccount = () => {
    client.categories = updatedCategories.join(',');
    clientService.update(client).then(showAlert("Account created successfully"));
    setClient(client);
    navigate("/edit-client", { replace: true });
  }

  useEffect(() => {
    if (!matchActivistCodesOpen && client.type === 'Campaign')
      ngpVanService.getActivistCodes((response: ActivistCodesInterface) => {
        setActivistCodes(response.voterActivistCodes.filter(code => !DEFAULT_ACTIVIST_CODES.includes(code.name)));
      });
  }, [ngpVanService, matchActivistCodesOpen]);

  return (
    <div className={classes.ChooseCategories}>
      <div className={classes.title}>
        <Button text="" iconText="arrow-left" callback={handleBack} />
        2. Choose Categories
      </div>

      <div className={classes.subTitle}>
        <p>Enabled</p>
        <p>Name</p>
      </div>

      <div className={classes.categories}>
        {categories.map((category) => {
          return <div
            key={category}
            className={classes.category}
            onClick={() => handleSelectCategory(category)}
          >
            {updatedCategories.includes(category) ?
              <span>
                <FontAwesomeIcon icon="check-circle" />
                Yes
              </span> :
              <span>
                <div className={classes.disabledIcon}/>
                No
              </span>}
            {category}
          </div>
        })}
      </div>

      {client.type === 'Campaign' && <>
        <div className={classes.title}>
          Activist codes
        </div>

        {activistCodes.length ?
          <>
            {matchedCategories.length ?
              <>
                <div className={classes.matchActivistCodes}>
                  {matchedCategories.map((category) => {
                    return <div key={category} className={classes.match}>
                      <div className={classes.categoryWrapper}>
                        <div className={classes.category}>
                          {category}
                        </div>
                      </div>
                      <div className={classes.activistCodesWrapper}>
                        {activistCodes.filter(c => c.categories.includes(category)).map((activistCode) => {
                          return <div key={activistCode.activistCodeId} className={classes.activistCode}>
                            {activistCode.name}
                          </div>
                        })}
                      </div>
                    </div>
                  })}
                </div>

                <div className={classes.updateButton}>
                  <Button text="Update" callback={() => setMatchActivistCodesOpen(true)} />
                </div>

              </> :
              <>
                <div className={classes.activistCodes}>
                  {activistCodes.map((activistCode) => {
                    return <div key={activistCode.activistCodeId} className={classes.activistCode}>
                      {activistCode.name}
                    </div>
                  })}
                </div>

                <p>
                  Match your activist codes to your categories to segment and target users by interest.
                </p>

                <div className={classes.buttonWrapper}>
                  <Button text="Match Activist Codes" customClass="primary" callback={() => setMatchActivistCodesOpen(true)} />
                </div>
              </>
            }
          </> :
          <div className={classes.matchActivistCodes}>
            <p>No Activist Codes</p>
            <p>Add your Votebuilder API key to pull codes.</p>
          </div>
        }

        {matchActivistCodesOpen &&
          <MatchActivistCodes
            handleClose={() => setMatchActivistCodesOpen(false)}
            clientId={client.id}
            categories={updatedCategories}
            activistCodes={activistCodes}
            ngpVanService={ngpVanService}
          />}
      </>}

      {!(client.type === 'Campaign' && activistCodes.length !== 0 && matchedCategories.length === 0) &&
        <div className={classes.buttonWrapper}>
          <Button text="Create Account" customClass="primary" callback={handleCreateAccount} />
        </div>
      }

    </div>
  )
}