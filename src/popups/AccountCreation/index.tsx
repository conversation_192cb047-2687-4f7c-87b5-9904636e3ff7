import { useState } from 'react';
import { ToastContainer } from 'react-toastify';

import { ClientService, UserService, NgpVanService } from 'services';

import Modal from 'shared/Modal';
import BasicInfo from './Steps/BasicInfo';
import ChooseCategories from './Steps/ChooseCategories';


export default function AccountCreation(props: { setClient: Function, clientService: ClientService, userService: UserService, ngpVanService: NgpVanService }) {
  const { setClient, clientService, userService, ngpVanService } = props;
  const [Step, setStep] = useState('BasicInfo');

  const AccountCreationSteps: any = {
    BasicInfo: (
      <BasicInfo
        clientService={clientService}
        userService={userService}
        handleNext={() => setStep('ChooseCategories')}
      />
    ),
    ChooseCategories: (
      <ChooseCategories
        setClient={setClient}
        clientService={clientService}
        ngpVanService={ngpVanService}
        handleBack={() => setStep('BasicInfo')}
      />
    ),
  };

  return (
    <Modal contentWrapperStyle={{ maxWidth: "860px" }} opaque={true}>
      {AccountCreationSteps[Step]}
      <ToastContainer position="top-center" />
    </Modal>
  )
}