import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faFly } from "@fortawesome/free-brands-svg-icons"

import Modal from "shared/Modal";
import Button from "shared/Button"

import classes from "./Published.module.scss"

export default function SavedList(props: { isOpen: Boolean, handleClose: Function, link: string }) {
  const { isOpen, handleClose, link } = props

  return (
    isOpen ?
      <Modal>
        <div className={classes.PublishWrapper}>
          <h1 className={classes.title}>Your Site is Live!</h1>

          <FontAwesomeIcon icon={faFly} className={classes.centerIcon} />

          <div className={classes.description}>
            <p>
              Congratulations!<br />
              You have successfully launched your Rep'd page.<br />
              Here's your Rep'd link:<br /><br />
              <a href={link}>{link}</a>
            </p>
          </div>

          <div className={classes.buttonWrapper}>
            <Button text="Close" callback={() => handleClose(false)} />
          </div>
        </div>
      </Modal>
      : null
  )
}