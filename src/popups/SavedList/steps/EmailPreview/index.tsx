import * as Yup from "yup"
import cn from "classnames"

import * as interfaces from 'interfaces';
import Button from "shared/Button"

import classes from "./EmailPreview.module.scss"
import FormClasses from 'shared/Forms/Forms.module.scss';
import { Field, Form, FormikProvider, useFormik } from "formik";

import emailPreview from 'assets/platform/email-image.png';

interface EmailPreviewProps {
  handleClose: () => void;
  handleNext: () => void;
  handleTestEmail: () => void;
  clientInfo: interfaces.ClientEmailInfoInterface;
  setClientInfo: Function;
  user?: interfaces.UserInterface;
  clientType?: string
}

export default function EmailPreview({ handleClose, handleNext, handleTestEmail, setClientInfo, clientInfo, user, clientType }: EmailPreviewProps) {
  const validationSchema = Yup.object().shape({
    fromEmail: Yup.string().email('Please Enter valid Email'),
    campaignName: Yup.string().required('Campaign Name is required'),
    campaignAddress: Yup.string().required('Campaign Address is required'),
  });

  const formik = useFormik({
    initialValues: clientInfo,
    validationSchema,
    onSubmit: (values) => { setClientInfo(values) }
  });

  const { setFieldValue, values, errors, touched, handleSubmit, handleBlur, isValid } = formik

  const handleSendTestEmail = () => {
    handleSubmit();
    isValid && handleTestEmail();
  }

  const handleChooseRecipientLists = () => {
    handleSubmit();
    isValid && handleNext();
  }

  return (
    <div className={classes.emailPreview}>
      <h5 className={classes.title}>Email Preview</h5>

      <div className={classes.previewWrapper}>
        <FormikProvider value={formik}>
          <Form className={`${FormClasses.Form} ${classes.previewInputs}`}>
            <p className={classes.subtitle}>Customize your email with the information below before bulk-sending your email.</p>

            <div className={FormClasses.InputWithLabel}>
              <label htmlFor="fromEmailName">Sent-From Name <small>(optional)</small></label>
              <Field type="text" name="fromEmailName" />
            </div>

            <div className={FormClasses.InputWithLabel}>
              <label htmlFor="fromEmail">Sent-From Email <small>(optional)</small></label>
              <Field type="email" name="fromEmail" className={cn(touched.fromEmail && errors.fromEmail && classes.hasError)} />
              {touched.fromEmail && errors.fromEmail && <div className={cn(FormClasses.errorText, classes.errorText)}>{errors.fromEmail}</div>}
            </div>

            <div className={FormClasses.InputWithLabel}>
              <label htmlFor="subject">Subject Line <small>(optional)</small></label>
              <Field type="text" name="subject" />
            </div>

            <div className={FormClasses.InputWithLabel}>
              <label htmlFor="topMessage">Top Message <small>(optional)</small></label>
              <textarea name="topMessage" rows={4} maxLength={140} value={values.topMessage}
                className={cn(touched.topMessage && errors.topMessage && classes.hasError)}
                onBlur={handleBlur}
                onChange={e => setFieldValue('topMessage', e.target.value)}
              ></textarea>
              {touched.topMessage && errors.topMessage && <div className={cn(FormClasses.errorText, classes.errorText)}>{errors.topMessage}</div>}
            </div>

            <div className={FormClasses.InputWithLabel}>
              <label htmlFor="campaignName">{clientType === 'Government' ? 'City Name' : 'Campaign Name'}<small>*</small></label>
              <Field type="text" name="campaignName" className={cn(touched.campaignName && errors.campaignName && classes.hasError)} />
              {touched.campaignName && errors.campaignName && <div className={cn(FormClasses.errorText, classes.errorText)}>{errors.campaignName}</div>}
            </div>

            <div className={FormClasses.InputWithLabel}>
              <label htmlFor="campaignAddress">{clientType === 'Government' ? 'City Hall Address' : 'Campaign Address'}<small>*</small></label>
              <textarea name="campaignAddress" rows={4} maxLength={140} value={values.campaignAddress}
                className={cn(touched.campaignAddress && errors.campaignAddress && classes.hasError)}
                onBlur={handleBlur}
                onChange={e => setFieldValue('campaignAddress', e.target.value)}
              ></textarea>
              {touched.campaignAddress && errors.campaignAddress && <div className={cn(FormClasses.errorText, classes.errorText)}>{errors.campaignAddress}</div>}
            </div>
          </Form>
        </FormikProvider>

        <div className={classes.previewInputs}>
          <h6 className={classes.title}>Sample</h6>
          <img src={emailPreview} alt="" width="100%" />
        </div>
      </div>

      <div className={classes.buttonWrapper}>
        <Button text="Cancel" callback={handleClose} />
        <Button text="Choose Recipient Lists" callback={handleChooseRecipientLists} customClass="primary" />
        <Button text="Send Test Email" callback={handleSendTestEmail} />
      </div>
    </div>
  )
}