@import 'src/styles/variables';

.emailPreview {
  .title {
    color: $blue;
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 700;
  }

  .previewWrapper {
    display: flex;

    .subtitle {
      margin-bottom: 20px;
    }

    .errorText {
      margin-bottom: 20px;
      margin-top: -20px;
    }

    .previewInputs {
      width: 50%;
      margin-left: 30px;

      h6 {
        color: $dark-grey;
        font-size: 16px;
        margin-bottom: 25px;
      }

      img {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
      }

      .hasError {
        border-color: #f00;
      }
    }
  }

  .buttonWrapper {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap-reverse;
    align-items: center;

    button {
      &:first-child {
        margin-right: 10px;
      }

      margin-top: 25px;
      padding: 7px 35px;
    }
  }
}

@media (max-width: 640px) {
  .emailPreview {

    .previewWrapper {
      display: block;

      form,
      .previewInputs {
        width: 100%;
      }
    }
  }
}