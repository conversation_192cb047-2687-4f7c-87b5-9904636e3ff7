@import 'src/styles/variables';

.sendTestEmail {
  width: 100%;

  .title {
    color: $blue;
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 700;
  }

  .bodySection {
    padding: 20px;

    .emailsForm {
      position: relative;
      display: flex;
      justify-content: start;
      align-items: center;
      flex-wrap: wrap;
      padding: 10px 10px 20px;
      min-height: 90px;
      background: white;
      border: 1px solid $offwhite;
      border-radius: 10px;
      font-size: 16px;

      .email {
        display: flex;
        align-items: center;
        margin-top: 10px;
        margin-right: 10px;
        padding: 5px 5px 5px 15px;
        background: $pale-blue;
        border-radius: 40px;
        color: $blue;

        .textInput {
          display: inline-block;
          vertical-align: middle;
          min-width: auto;
          background: transparent;
          border: none;
          border-radius: 40px;
          line-height: 1;
        }

        button {
          position: relative;
          top: -1px;
          margin-left: 10px;
          padding: 3px 8px 5px;
          border: 1px solid $blue;
          border-radius: 40px;
          font-size: 16px;
          color: $blue;
          line-height: 1;
          background: none;

          &:hover {
            color: $dark-blue;
            border-color: $dark-blue
          }
        }
      }
    }
  }

  .emailInputWrapper {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;

    input {
      padding: 5px 10px;
      border-radius: 5px;
      border: 1px solid $grey;
      appearance: none;
      font-size: 16px;
      width: 100%;
    }

    .hasError {
      border-color: red;
    }

    button {
      color: $blue;
      padding: 7px 15px;
      margin-left: 10px;

      &:hover {
        background-color: $pale-blue;
      }
    }

  }

  .errorText {
    color: red;
    margin-top: 5px;
    margin-left: 20px;
  }

  .buttonWrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      color: $blue;
      padding: 7px 35px;

      &:first-child {
        margin-right: 20px;
      }

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}