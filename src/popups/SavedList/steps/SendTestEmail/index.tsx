import { useState } from "react";
import { Field, Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";

import { NgpVanService } from "services";

import { useNotification } from "hooks";

import Button from "shared/Button";

import classes from "./SendTestEmail.module.scss";

interface EmailPreviewProps {
  handleBack: () => void;
  ngpVanService?: NgpVanService;
  answerId: string;
  clientInfo: any;
}

export default function SendTestEmail({ handleBack, ngpVanService, answerId, clientInfo }: EmailPreviewProps) {

  const [emails, setEmails] = useState<string[]>([]);
  const { showAlert } = useNotification();

  const removeEmail = (event: any, index: number) => {
    event.preventDefault();
    setEmails(emails.filter((_, i) => i !== index));
  }

  const emailValidationSchema = Yup.object().shape({
    email: Yup.string().email('Please Enter valid Email')
  });

  const validateEmail = (email: string) => emails.includes(email) ? 'Email already added' : null;

  const emailsFormik = useFormik({
    initialValues: { email: '' },
    validationSchema: emailValidationSchema,
    validateOnChange: false,
    onSubmit: (values, { resetForm }) => {
      if (values.email) {
        setEmails(emails => [...emails, values.email]);
        resetForm();
      }
    }
  });

  const handleSend = () => {
    ngpVanService?.postNgpVanTestEmail({ emails: emails, ...clientInfo }, answerId, () => { showAlert("Test email(s) sent") });
    handleBack();
  }

  return (
    <div className={classes.sendTestEmail}>
      <h5 className={classes.title}>Send Test Email</h5>

      <div className={classes.bodySection}>
        <div className={classes.emailsForm}>
          {emails.map((email, index: number) => {
            return <div key={index} className={classes.email}>
              <div className={classes.textInput}>
                {email}
              </div>
              <button onClick={(event) => removeEmail(event, index)}>x</button>
            </div>
          })}
          <FormikProvider value={emailsFormik}>
            <Form className={classes.emailInputWrapper}>
              <Field
                  type="email"
                  name="email"
                  placeholder="Enter email..."
                  validate={validateEmail}
                  className={emailsFormik.errors.email && emailsFormik.touched.email && classes.hasError}
              />
              <Button text="Add" callback={() => emailsFormik.handleSubmit()} />
            </Form>
          </FormikProvider>
        </div>
        {emailsFormik.touched.email && emailsFormik.errors.email && <div className={classes.errorText}>{emailsFormik.errors.email}</div>}

        <div className={classes.buttonWrapper}>
          <Button text="Back" callback={handleBack} />
          <Button text="Send" customClass={emails.length > 0 ? "" : "disabled"} callback={handleSend} />
        </div>
      </div>


    </div>
  )
}