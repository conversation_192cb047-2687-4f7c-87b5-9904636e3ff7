import Button from "shared/Button"
import classes from "./NgpVideoGuide.module.scss"

export default function NgpVideoGuide({ handleClose }: { handleClose: () => void }) {
  return (
    <div className={classes.ngpVideoGuide}>
      <h5 className={classes.title}>NGP VAN Complete Video Guide</h5>
      <video width="100%" height="auto" controls> <source src="https://admin.repd.us/assets/ngp-van-video-guide.mp4" /></video>
      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </div>
  )
}