import { faFly } from "@fortawesome/free-brands-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"

import Button from "shared/Button"

import classes from "./Completion.module.scss"

export default function Completion({ handleClose }: { handleClose: () => void }) {
  return (
    <div className={classes.savedListCompleted}>
      <h1 className={classes.title}>Saved List</h1>

      <FontAwesomeIcon icon={faFly} className={classes.flyIcon} />

      <div className={classes.description}>
        <p>Thanks for adding <br /> Users in these lists will be <br /> notified of your answer shortly.</p>
      </div>

      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </div>
  )
}