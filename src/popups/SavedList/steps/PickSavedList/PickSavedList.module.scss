@import 'src/styles/variables';

.savedList {
  width: 100%;

  .subtitle {
    font-size: 16px;
  }

  .title {
    color: $blue;
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 700;
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      &:first-child {
        margin-right: 10px;
      }

      margin-top: 25px;
      padding: 7px 35px;
    }
  }

  .pickSavedList {
    position: relative;
    display: block;
    margin: 20px 0 0 0;
    max-height: 312px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid $pale-blue;
    border-radius: 10px;
    border-color: $offwhite;
    background: white;

    .select {
      ul.options {
        margin: 0;
        padding: 0;

        li {
          cursor: pointer;
          user-select: none;
          list-style: none;

          &.option {
            border: 1px solid $pale-blue;
            border-bottom: 0;
            width: 100%;
            background-color: white;
            z-index: 1;
            padding: 15px;
            font-size: 16px;

            .usersCount {
              margin-left: 4px;
              color: $blue;
            }

            input {
              margin-left: 4px;
              margin-right: 6px;
            }

            // display: none;

            &:hover {
              background-color: $pale-blue;
            }

            &.active {
              display: block;
            }

            &.default {
              margin: 0;
              border: 2px dashed $pale-blue;
              border-radius: 5px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-weight: bold;
              cursor: pointer;
            }
          }
        }
      }
    }
  }
}