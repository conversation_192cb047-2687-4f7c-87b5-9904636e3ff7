import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown, faChevronUp } from "@fortawesome/free-solid-svg-icons";
import { Field, Form, FormikProvider, useFormik } from "formik";
import { ToastContainer, toast } from 'react-toastify';

import { NgpVanService } from "services";
import { NgpVanInterface } from "interfaces";

import Button from "shared/Button"
import Chip from "components/Chip";

import classes from "./PickSavedList.module.scss"

interface PickListProps {
  handleBack: () => void;
  handleNext: () => void;
  ngpVanService?: NgpVanService;
  answerId: string;
  setSelectedSavedListName: any;
  clientType?: string;
}

export default function PickSavedList(
  { handleBack, handleNext, ngpVanService, answerId, setSelectedSavedListName, clientType }: PickListProps
) {
  const [selectOpen, setSelectOpen] = useState(false)
  const [savedList, setSavedList] = useState([] as NgpVanInterface[])

  useEffect(() => {
    ngpVanService?.getNgpVan(() => { }).then((value?: NgpVanInterface[]) => {
      if (value && value.length > 0) setSavedList(value)
      else toast.error("No saved lists are found")
    })
  }, [])

  const formik = useFormik<any>({
    initialValues: savedList?.reduce((acc, cur: any) => ({ ...acc, [cur.savedListId]: "" }), {}),
    // initialValues: {test: ""},
    onSubmit: (values, initialValues) => {
      handleNext();
      setSelectedSavedListName(savedList?.filter((item: any) => values[item?.savedListId]))
    }
  });

  const { setFieldValue, values, handleSubmit } = formik

  return (
    <div className={classes.savedList}>
      <h5 className={classes.title}>Saved List</h5>

      <p className={classes.subtitle}>
        {clientType === 'Government' ?
          'Select one or more saved lists of contacts to be notified of your answer.':
          'Add the saved lists of users from your NGP VAN account to be notified of your answer.'
        }
      </p>

      <FormikProvider value={formik}>
        <Form className={classes.pickSavedList}>
          <div className={classes.select}>
            <ul className={classes.options}>
              <li className={`${classes.option} ${classes.active} ${classes.default}`} onClick={() => setSelectOpen(!selectOpen)}>
                <p>Pick saved list(s)</p>
                <FontAwesomeIcon icon={selectOpen ? faChevronUp : faChevronDown} />
              </li>

              {selectOpen && savedList.map(
                (option: any, index: any) =>
                  <label htmlFor={option.savedListId} key={index}>
                    <li className={classes.option}>
                      <Field name={option.savedListId} id={option.savedListId} type="checkbox" />
                      {option.savedListName}
                      {option.usersCount &&
                        <span className={classes.usersCount}>
                          ({option.usersCount > 999 ? (option.usersCount/1000).toFixed(0) + 'k' : option.usersCount})
                        </span>
                      }
                    </li>
                  </label>
              )}

              {savedList?.filter((item: any) => values[item?.savedListId]).map((option: any) =>
                <Chip
                  title={option.savedListName}
                  key={option.savedListId}
                  handleRemove={() => setFieldValue(option.savedListId, false)}
                />
              )}

            </ul>
          </div>
        </Form>
      </FormikProvider>

      <div className={classes.buttonWrapper}>
        <Button text="Back" callback={handleBack} />
        <Button text="Send" callback={handleSubmit} />
      </div>

      <ToastContainer position="top-center" />
    </div>
  )
}