import { useState } from 'react';

import PickSavedList from "./steps/PickSavedList";
import Completion from "./steps/Completion";
import EmailPreview from "./steps/EmailPreview";
import NgpVideoGuide from "./steps/NgpVideoGuide";
import SendTestEmail from "./steps/SendTestEmail";

import Modal from "shared/Modal";

import { NgpVanService } from 'services';
import * as interfaces from 'interfaces';


interface SavedListProps {
  handleClose: () => void;
  user?: interfaces.UserInterface;
  isOpen: boolean;
  ngpVanService?: NgpVanService;
  answerId: string;
  setSavedListInfo: any
}

export default function SavedList(
  { isOpen, handleClose, ngpVanService, answerId, user, setSavedListInfo }: SavedListProps
) {
  const [currentStep, setCurrentStep] = useState<string>("emailPreview")
  const [selectedSavedList, setSelectedSavedList] = useState([])
  const [clientInfo, setClientInfo] = useState<interfaces.ClientEmailInfoInterface>({
    fromEmailName: user?.client?.name || `${user?.firstName} ${user?.lastName}` || "",
    fromEmail: user?.client?.email || user?.email || "",
    subject: user?.client?.subject || "",
    topMessage: user?.client?.topMessage || "",
    campaignName: user?.client?.campaignName || "",
    campaignAddress: user?.client?.campaignAddress || ""
  })
  const clientType = user?.client?.clientType;

  const handleChangeStep = (step: string) => {
    setCurrentStep(step)
  }
  const handleClosePopup = () => {
    handleClose();
    handleChangeStep("emailPreview")
  }
  const checkEntryCount = () => {
    ngpVanService?.getAnswerExports(answerId, () => { }).then((item: any) => setSavedListInfo(item.data))
  }
  const handlePostNgpVan = () => {
    selectedSavedList.map((item: any) => {
      ngpVanService?.postNgpVan(
        { savedListName: item.savedListName, ...clientInfo },
        answerId, item.savedListId, () => checkEntryCount() );
    });
    handleClosePopup();
  }

  const savedListSteps: any = {
    "pickSavedList": {
      component: <PickSavedList
        handleBack={() => handleChangeStep("emailPreview")}
        handleNext={() => handleChangeStep("completed")}
        ngpVanService={ngpVanService}
        setSelectedSavedListName={setSelectedSavedList}
        answerId={answerId}
        clientType={clientType}
      />,
      width: "460px"
    },
    "emailPreview": {
      component: <EmailPreview
        handleClose={handleClosePopup}
        handleNext={() => handleChangeStep("pickSavedList")}
        handleTestEmail={() => handleChangeStep("sendTestEmail")}
        setClientInfo={setClientInfo}
        user={user}
        clientInfo={clientInfo}
        clientType={clientType}
      />,
      width: "860px"
    },
    "ngpVanGuide": {
      component: <NgpVideoGuide
        handleClose={handleClosePopup}
      />,
      width: "920px"
    },
    "sendTestEmail": {
      component: <SendTestEmail
          handleBack={() => handleChangeStep("emailPreview")}
          ngpVanService={ngpVanService}
          answerId={answerId}
          clientInfo={clientInfo}
      />,
      width: "640px"
    },
    "completed": {
      component: <Completion
        handleClose={handlePostNgpVan}
      />
    },
    width: "460px"
  }

  return (
    isOpen ?
      <Modal contentWrapperStyle={{ maxWidth: savedListSteps[currentStep].width }}>
        {savedListSteps[currentStep].component}
      </Modal>
      : null
  )
}