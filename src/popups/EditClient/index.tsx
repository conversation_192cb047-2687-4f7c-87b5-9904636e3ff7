import { useState } from 'react';
import { toast } from 'react-toastify';
import cn from 'classnames';

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import { ClientService } from 'services';
import { ClientInterface } from 'interfaces';

import FormClasses from 'shared/Forms/Forms.module.scss';
import classes from './EditClient.module.scss';

interface EditClientProps {
  client: ClientInterface;
  clientService?: ClientService;
  records: ClientInterface[];
  setRecords: any;
  handleClose: () => void;
}

// Your list includes U.S. states, territories, and the District of Columbia.
// If you’re looking for only the 50 U.S. states, remove the following:
//
// •	DC (District of Columbia)
// •	PR (Puerto Rico)
// •	VI (U.S. Virgin Islands)
// •	GU (Guam)
// •	AS (American Samoa)
// •	MP (Northern Mariana Islands)
//
const states = [
  'CA', 'NY', 'TX', 'FL', 'IL', 'PA', 'OH', 'GA', 'NC', 'MI', 'NJ', 'VA', 'WA', 'AZ',
  'MA', 'TN', 'IN', 'MO', 'MD', 'WI', 'CO', 'MN', 'SC', 'AL', 'LA', 'KY', 'OR', 'CT',
  'OK', 'IA', 'UT', 'NV', 'AR', 'MS', 'KS', 'NM', 'NE', 'WV', 'ID', 'HI', 'NH', 'ME',
  'MT', 'RI', 'DE', 'SD', 'ND', 'AK', 'VT', 'WY'
];

export default function EditClient({
  client,
  clientService,
  records,
  setRecords,
  handleClose,
}: EditClientProps) {
  const [updatedClient, setClient] = useState(client)
  const [isUpdating, setUpdated] = useState(false);

  const updateField = (client: ClientInterface) => {
    setClient(client);
    setRecords(records.map((c: ClientInterface) => c.id === client.id ? client : c));
  }

  const handleSave = (client: ClientInterface) => {
    if (isUpdating) return;
    setUpdated(true);

    clientService?.update(updatedClient, (item: ClientInterface) => {
      setUpdated(false);

      if (!item?.id) {
        toast.error(
          "Saving the client failed. Please try again later.",
        );
      } else {
        toast.success("Client saved successfully.");
      }

      !!handleClose && handleClose();
    });
  }

  return (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.editClient}>
        <div className={classes.title}>Edit <b>{client.name}</b></div>
        <div className={classes.bodySection}>
          <div className={classes.form}>
            <div className={classes.fieldGroup}>
              <div>
                <label>Name</label>
                <input type="text" defaultValue={client.name} onChange={(e) => updateField({ ...client, name: e.target.value })} />
              </div>

              <div>
                <label>Email</label>
                <input type="email" defaultValue={client.email} onChange={(e) => updateField({ ...client, email: e.target.value })} />
              </div>
            </div>

            <div className={classes.fieldGroup}>
              {/* <div>
                <label>Is Published?</label>
                <select onChange={(e) => updateField({ ...client, isPublished: e.target.value === 'Yes' })}>
                  <option value="Yes" selected={!!client.isPublished}>Yes</option>
                  <option value="No" selected={!client.isPublished}>No</option>
                </select>
              </div> */}

              <div>
                <label>Is Locked?</label>
                <select onChange={(e) => updateField({ ...client, isLocked: e.target.value === 'Yes', isPublished: e.target.value === 'No' })}>
                  <option value="Yes" selected={!!client.isLocked}>Yes</option>
                  <option value="No" selected={!client.isLocked}>No</option>
                </select>
              </div>
            </div>

            <div className={classes.fieldGroup}>
              <div>
                <label>AI Is Enabled?</label>
                <select onChange={(e) => updateField({ ...client, aiQuestionsEnabled: e.target.value === 'Yes' })}>
                  <option value="Yes" selected={!!client.aiQuestionsEnabled}>Yes</option>
                  <option value="No" selected={!client.aiQuestionsEnabled}>No</option>
                </select>
              </div>
            </div>


            <div className={classes.fieldGroup}>
              <div>
                <label>Question UI Always On?</label>
                <select onChange={(e) => updateField({ ...client, aiAskQuestionAlwaysOn: e.target.value === 'Yes' })}>
                  <option value="Yes" selected={!!client.aiAskQuestionAlwaysOn}>Yes</option>
                  <option value="No" selected={!client.aiAskQuestionAlwaysOn}>No</option>
                </select>
              </div>
            </div>

            {/* Add two code state selection, tied to 'state' field on client */}
            <div className={classes.fieldGroup}>
              <div>
                <label>State</label>
                <select onChange={(e) => updateField({ ...client, state: e.target.value })}>
                  {/* dynamic options based on variable */}
                  {states.map((state) => (
                    <option key={state} value={state} selected={client.state === state}>{state}</option>
                  ))}
                </select>
              </div>

              <div>
                <label>Population Size</label>
                <input type="number" defaultValue={client.populationSize} onChange={(e) => updateField({ ...client, populationSize: Number(e.target.value) })} />
              </div>
            </div>
          </div>

          <div className={classes.buttonWrapper}>
            <Button text="Cancel" customClass={classes.button} callback={handleClose} />
            <Button text={isUpdating ? 'Saving...' : 'Save'} customClass={classes.button + ', ' + classes.primary} callback={handleSave} />
          </div>
        </div>
      </div>
    </Modal>
  )
}