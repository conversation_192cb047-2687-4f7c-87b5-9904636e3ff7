@import 'styles/variables';

.editClient {
  width: 100%;
  text-align: center;
  font-size: 16px;

  .title {
    padding-bottom: 20px;
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 1.4em;
  }

  .subTitle {
    font-size: 18px;
    color: $dark-blue;
    font-weight: 400;
  }

  .bodySection {
    padding: 20px;

    textarea {
      width: 100%;
      height: 322px;
      max-height: 500px;
      border: 1px solid $offwhite;
      padding: 10px;
      font-size: 16px;
      border-radius: 5px;
      white-space: pre-line;
      font-weight: 500;
      color: black;
      font-family: monospace;
      outline: $grey 2px dashed;

      &:focus {
        outline: $blue 2px dashed !important;
      }

      &::placeholder {
        color: $grey;
        font-weight: 500;
      }
    }
  }

  .buttonWrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      color: $blue;
      padding: 7px 35px;

      &.primary {
        color: white;
      }

      &:first-child {
        margin-right: 20px;
      }

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}

.form {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  overflow: auto;

  .fieldGroup {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 20px;
    width: 100%;

    > * {
      width: 100%;
      text-align: left;
    }
  }

  p {
    width: 100%;
  }

  label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
  }

  input,
  select {
    min-width: 100%;
    padding: 10px 15px;
    border: 1px solid lightgray;
    border-radius: 5px;
  }
}
