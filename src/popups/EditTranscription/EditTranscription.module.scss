@import 'styles/variables';

.editTranscription {
  width: 100%;
  text-align: center;
  font-size: 16px;

  .title {
    padding-bottom: 20px;
    margin-bottom: 10px;
    font-weight: 500;
  }

  // .transcription {
  //   font-size: 18px;
  //   color: $dark-blue;
  //   font-weight: 400;
  // }

  .bodySection {
    padding: 20px;

    strong {
      min-width: 80px;
    }

    .transcription {
      display: flex;
      // width: 80px;
      // padding-right: 20px;
    }

    .input {
      width: 100%;
      // height: 135px;
      max-height: 500px;
      margin-bottom: 20px;
      padding: 10px;
      border: 1px solid $offwhite;
      font-size: 16px;
      border-radius: 5px;
      white-space: pre-line;
      font-weight: 500;
      color: black;
      font-family: monospace;
      outline: $grey 2px dashed;
      overflow: scroll;
      text-align: left;

      &:focus {
        outline: $blue 2px dashed !important;
      }

      &::placeholder {
        color: $grey;
        font-weight: 500;
      }
    }

  }

  .buttonWrapper {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      color: $blue;
      padding: 7px 35px;

      &:first-child {
        margin-right: 20px;
      }

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}