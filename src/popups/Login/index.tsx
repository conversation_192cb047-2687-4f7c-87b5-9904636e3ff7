import React from 'react';
import { Field, Form, FormikProvider, useFormik } from "formik";
import { ToastContainer, toast } from 'react-toastify';
import * as Yup from "yup";

import Modal from 'shared/Modal';
import FormClasses from 'shared/Forms/Forms.module.scss';
import ButtonClasses from 'shared/Button/Button.module.scss';
import classes from './Login.module.scss';

import * as interfaces from 'interfaces';
import { AuthService } from 'services/auth.service';


export default function Login(props: { callback: Function, authService: AuthService }) {
    const { callback, authService } = props;

    const emailErrorMessage = "Please Enter valid Email",
        passwordErrorMessage = "Please Enter valid Password"

    const formik = useFormik({
        initialValues: {
            email: '',
            password: ''
        },
        validationSchema: Yup.object().shape({
            email: Yup.string().required(emailErrorMessage).email(emailErrorMessage),
            password: Yup.string().required(passwordErrorMessage),
        }),
        onSubmit: (values, actions) => {
            authService.login(values.email, values.password, (session: interfaces.SessionInterface | null) => {
                if (session?.loginLinkSent) {
                    toast.success('Login link was sent to your email');
                    return callback()
                }
                if (session?.id) {
                    toast.success('Logged in successfully');
                    return callback()
                } else
                    toast.error('Please enter valid login credentials');
            })
        }
    });

    const { touched, errors } = formik;

    const emailErrorClass = errors.email && touched.email && FormClasses.hasError,
        passwordErrorClass = errors.password && touched.password && FormClasses.hasError

    return (
        <Modal opaque={true}>
            <div className={classes.LoginWrapper}>
                <h1 className={classes.title}>Please Log In</h1>
                <FormikProvider value={formik}>
                    <Form className={FormClasses.Form}>
                        <div className={FormClasses.InputWithLabel}>
                            <label>Email</label>
                            <Field type="email" name="email" className={emailErrorClass} placeholder="Enter your email" />
                            {errors.email && touched.email && <div className={FormClasses.errorText}>{errors.email}</div>}
                        </div>

                        <div className={FormClasses.InputWithLabel}>
                            <label>Password</label>
                            <Field type="password" name="password" className={passwordErrorClass} />
                            {errors.password && touched.password && <div className={FormClasses.errorText}>{errors.password}</div>}
                        </div>

                        <div className={classes.buttonBar}>
                            <button type="submit" className={ButtonClasses.Button}>
                                <span className={ButtonClasses.text}> Enter</span>
                            </button>
                        </div>
                    </Form>
                </FormikProvider>
            </div>
            <ToastContainer position="top-center" />
        </Modal>
    )
}