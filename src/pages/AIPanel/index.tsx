// import { ToastContainer } from 'react-toastify';

import { useState } from 'react';
import { UserInterface, ClientInterface } from 'interfaces';
import Button from 'shared/Button';

import { UserService, ClientService, AuthService } from 'services';
import { useServiceContext } from 'services/ServiceProvider';

import pageClasses from 'styles/PageWrapper.module.scss';
import classes from './AIPanel.module.scss';
import PageLayout from 'shared/PageLayout';
import AISource from 'components/AIPanel/AISource/AISource';
import EmbedCode from 'components/AIPanel/EmbedCode/EmbedCode';
import AddSource from 'popups/AddSource';

interface ProfileProps {
  user?: UserInterface;
  setUser: Function;
  userService?: UserService;
  clientService: ClientService;
  authService: AuthService;
}

export default function AIPanel(props: ProfileProps) {
  const { user, clientService, authService } = props;
  const { adminStatsService } = useServiceContext();
  const client: ClientInterface | any = clientService.client;
  const [isAddSourceModalOpen, setIsAddSourceModalOpen] = useState(false);

  // Sample data for AISource - in a real implementation, this would likely come from an API
  const aiSourcesData = [
    {
      id: 1,
      progress: 75,
      sourceName: "Emporia Main",
      sourceUrl: "https://www.emporiaks.gov",
      lastScraped: new Date().toLocaleDateString('en-US', {
        weekday: 'short',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      })
    }
  ];

  const handleEditSource = (sourceId: number) => {
    // Handle edit source action
    console.log("Edit source clicked for ID:", sourceId);
    // This could open a modal or navigate to an edit page
  };

  const handleAddSource = () => {
    adminStatsService?.trackEvent('AIPanel', 'add_source');
    setIsAddSourceModalOpen(true);
  };

  return <PageLayout user={user} client={client} authService={authService}>
    <div className={classes.AIPanel}>
      <div className={pageClasses.Wrapper}>
        <h1 className={pageClasses.title}>
          AI Progress

          <Button text="Add Source" callback={handleAddSource} />
        </h1>

        {aiSourcesData.map(source => (
          <AISource
            key={source.id}
            id={source.id}
            progress={source.progress}
            sourceName={source.sourceName}
            sourceUrl={source.sourceUrl}
            lastScraped={source.lastScraped}
            onEditClick={handleEditSource}
          />
        ))}

        <p className={classes.noOverridingSources}>No overriding sources added, please add a source above.</p>

        <EmbedCode />
      </div>
    </div>

    <AddSource
      isOpen={isAddSourceModalOpen}
      handleClose={() => setIsAddSourceModalOpen(false)}
    />
  </PageLayout>
}
