import React, { useState, useRef, useEffect } from 'react';
import cn from 'classnames';
import ReCA<PERSON>TCHA from 'react-google-recaptcha';

import logoSrc from 'assets/logo/Logo-Full.png';
import { ApiService } from 'services/api.service';

import classes from './Unsubscribe.module.scss';

const captchaSiteKey = process.env.NODE_ENV !== "development" && process.env.REACT_APP_CAPTCHA_SITE_KEY ? process.env.REACT_APP_CAPTCHA_SITE_KEY : "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI";

export default function Unsubscribe() {
  const [isVerified, setIsVerified] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const recaptchaRef = useRef<ReCAPTCHA>(null);

  useEffect(() => {
    // Get email from URL query parameter
    const params = new URLSearchParams(window.location.search);
    const emailParam = params.get('email');
    setEmail(emailParam);
  }, []);

  const handleCaptchaChange = (token: string | null) => {
    setIsVerified(!!token);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isVerified && email) {
      try {
        const apiService = new ApiService();
        await apiService.post(
          { endpoint: 'unsubscribe' },
          { email }
        );
        setIsSubmitted(true);
        setError(null);
      } catch (err) {
        setError('Failed to unsubscribe. Please try again later.');
        console.error('Unsubscribe error:', err);
      }
    }
  };

  return (
    <div className={cn(classes.Unsubscribe, 'Unsubscribe')}>
      <div className={classes.header}>
        <img className={classes.logo} src={logoSrc} alt="Logo" />
      </div>

      <div className={classes.content}>
        {!isSubmitted ? (
          <form onSubmit={handleSubmit} className={classes.unsubscribeForm}>
            <h2>Unsubscribe from emails</h2>
            <p>We're sorry to see you go. Please confirm your unsubscription below.</p>

            {error && <p className={classes.errorMessage}>{error}</p>}

            <div className={classes.captchaContainer}>
              <ReCAPTCHA
                ref={recaptchaRef}
                sitekey={captchaSiteKey}
                onChange={handleCaptchaChange}
              />
            </div>

            <button
              type="submit"
              disabled={!isVerified || !email}
              className={classes.submitButton}
            >
              Unsubscribe
            </button>
          </form>
        ) : (
          <div className={classes.successMessage}>
            <h2>Successfully Unsubscribed</h2>
            <p>You have been removed from our mailing list.</p>
          </div>
        )}
      </div>
    </div>
  );
}
