@import 'styles/variables';

.Unsubscribe {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background-color: white;

  .header {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 20px 0;

    .logo {
      max-width: 300px;
      height: auto;
    }
  }
}

.content {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.unsubscribeForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.captchaContainer {
  margin: 1rem 0;
  display: flex;
  justify-content: center;
}

.submitButton {
  padding: 10px 20px;
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #3367d6;
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
}

.successMessage {
  text-align: center;

  h2 {
    color: #4caf50;
    margin-bottom: 1rem;
  }
}

.emailInfo {
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.errorMessage {
  color: #d32f2f;
  margin: 1rem 0;
}