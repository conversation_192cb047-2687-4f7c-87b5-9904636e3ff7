import React, { useEffect, useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import _ from 'lodash';

import logger from 'utils/logger';
import SiteSettingsSteps from 'components/SiteSettingsSteps';
import Button from 'shared/Button';

import { ClientInterface, TextOptionsInterface, UserInterface } from 'interfaces';
import { AuthService, ClientService } from 'services/index'
import { useNotification } from "hooks";

import pageClasses from 'styles/PageWrapper.module.scss';
import classes from './SiteSettings.module.scss';
import PageLayout from 'shared/PageLayout';

export default function SiteSettings(props: {
  setClient: Function,
  service: ClientService,
  user?: UserInterface,
  authService: AuthService,
}) {
  const { user, setClient, service, authService } = props;
  const { showAlert } = useNotification();

  const [step, setStep] = useState(1);
  const [savedIndex, setSavedIndex] = useState(-1);

  const [originalClient, setOriginalClient] = useState<Partial<ClientInterface>>({});

  var client: ClientInterface | any = service.client;

  useEffect(() => {
    setOriginalClient(service?.client || {});
  }, []);

  function validateClient(c: ClientInterface | any) {
    var isValid: boolean = true;

    for (var field in c) {
      if (['websiteURL', 'email', 'name'].includes(field) && !c[field]) {
        // toast.error('Please make sure the required fields are present.');
        isValid = false
        break;
      }

      if (['email'].includes(field) && !c[field].match(/[^@]+@[^.]+\.[a-z]+/)) {
        // toast.error('Please enter a valid email');
        isValid = false
        break;
      }
    }

    return isValid
  }

  function updateClient(updatedClient: ClientInterface) {
    client = updatedClient

    if (!validateClient(updatedClient)) return;

    setClient(updatedClient)

    service.client = client
  }

  function renderButtonText() {
    if (step !== 6) return "Save & Next"
    else return "Save & Complete"
  }

  function save() {
    showAlert("Updating your settings...")
    setSavedIndex(step - 1)

    if (!validateClient(client)) {
      toast.error('Please make sure the required fields are present & valid.');

      client = service.client

      return;
    }

    const changedFields = _.pickBy(client, (value, key) => {
      return !_.isEqual(value, originalClient[key as keyof ClientInterface]);
    });

    changedFields.textOptions = client.textOptions;

    // Always include AI embed design fields to ensure they're saved
    changedFields.whiteLogoURL = client.whiteLogoURL;
    changedFields.embedMainColour = client.embedMainColour;
    changedFields.expandedWhiteLogoURL = client.expandedWhiteLogoURL;
    changedFields.embedPrimaryColour = client.embedPrimaryColour;
    changedFields.embedAccentColour = client.embedAccentColour;
    changedFields.embedProgressBarColour = client.embedProgressBarColour;
    changedFields.embedButtonColour = client.embedButtonColour;

    logger.info(`Updated client values: ${client.id}`, changedFields);
    // console.log(`Updated client values: ${client.id}`, changedFields);

    service.update({ id: client.id, ...changedFields }, () => {
      showAlert("Your site settings are updated.")
      setTimeout(() => setSavedIndex(-1), 500)
    })
  }

  return (
    <div className={classes.SiteSettings}>
      <PageLayout user={user} client={client} authService={authService} className={classes.page}>
        <div className={classes.content}>
          <h1 className={pageClasses.title}>
            Site Settings
            <Button text={renderButtonText()} callback={save} />
          </h1>

          {client && <SiteSettingsSteps client={client} setClient={updateClient} savedIndex={savedIndex} setStep={setStep} />}
        </div>
      </PageLayout>

      <ToastContainer position="top-right" />
    </div>
  )
}
