.ErrorPage {
  padding: 20px;

  .content {
    flex: 1;
    width: 100%;
  }
}

.title {
  font-size: 2rem;
  margin-bottom: 20px;
  color: #e53e3e;
}

.subtitle {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #333;
}

.description {
  font-size: 1.2rem;
  margin-bottom: 30px;
  line-height: 1.5;
}

.errorBox {
  background-color: #fff5f5;
  border: 1px solid #fc8181;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 30px;
  margin-top: 30px;
  width: 100%;
}

.errorCode {
  font-family: monospace;
  font-size: 1.1rem;
  background-color: #f7fafc;
  padding: 15px;
  border-radius: 5px;
  border-left: 4px solid #e53e3e;
  margin-bottom: 20px;
  overflow-x: auto;
}

.button {
  padding: 10px 20px;
  font-size: 1rem;
  color: #fff;
  background-color: #e53e3e;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-right: 10px;
  margin-top: 10px;
}

.button:hover {
  background-color: #c53030;
}

.loading {
  font-size: 1.2rem;
  color: #666;
  margin: 20px 0;
  text-align: center;
}

.errorMessage {
  font-size: 1.2rem;
  color: #e53e3e;
  margin: 20px 0;
  text-align: center;
  padding: 20px;
  background-color: #fff5f5;
  border: 1px solid #fc8181;
  border-radius: 5px;
  width: 100%;

  p {
    margin-bottom: 15px;
  }
}

.tableContainer {
  width: 100%;
  margin-bottom: 30px;
}

.noErrors {
  font-size: 1.1rem;
  color: #666;
  margin: 20px 0;
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

.errorMessageCell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.healthIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.healthyIndicator {
  background-color: #48bb78;
  /* Green */
}

.unhealthyIndicator {
  background-color: #e53e3e;
  /* Red */
}

.countCell {
  font-weight: bold;
  padding-left: 10px;
}

.errorRow {
  cursor: pointer;
  transition: background-color 0.2s;
}

.errorRow:hover {
  background-color: #f0f4f8;
}

.statRow {
  transition: background-color 0.2s;
}

.statRow:hover {
  background-color: #f0f4f8;
}

.errorSummary {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;

  p {
    margin: 0;
    font-size: 1.1rem;
  }
}

.errorInstances {
  margin-bottom: 20px;
}

.errorInstance {
  margin-bottom: 25px;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 5px;
  background-color: #f8fafc;

  &:last-child {
    margin-bottom: 0;
  }
}

.instanceTitle {
  font-size: 1.2rem;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.errorDetails {
  margin-bottom: 10px;
}

.errorDetail {
  margin-bottom: 15px;

  strong {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: 600;
  }
}