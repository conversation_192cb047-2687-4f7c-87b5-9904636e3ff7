import React, { useState, useEffect, useMemo } from "react";
import { AuthService, ErrorService, AdminStatsService } from "services";
import { ClientInterface, UserInterface, EndpointErrorInterface, AdminStatEventInterface } from "interfaces";
import { API_URL_ROUTES } from "services/api.service";
import PageLayout from "shared/PageLayout";
import classes from "./ErrorPage.module.scss";
import pageClasses from "styles/PageWrapper.module.scss";
import tableClasses from "shared/Table/Table.module.scss";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "shared/ui/table";
import cn from "classnames";

// Interface for grouped errors
interface GroupedErrorInterface {
  route: string;
  method: string;
  count: number;
  errors: EndpointErrorInterface[];
  latestError: EndpointErrorInterface;
}

// Interface for grouped admin stat events
interface GroupedAdminStatInterface {
  eventAction: string;
  count: number;
  events: AdminStatEventInterface[];
  latestEvent: AdminStatEventInterface;
}

interface ErrorPageProps {
  user?: UserInterface;
  client?: ClientInterface | null;
  authService: AuthService;
}

const ErrorPage: React.FC<ErrorPageProps> = ({ user, client, authService }) => {
  const [endpointErrors, setEndpointErrors] = useState<EndpointErrorInterface[]>([]);
  const [adminStatEvents, setAdminStatEvents] = useState<AdminStatEventInterface[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingStats, setLoadingStats] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [selectedErrorRoute, setSelectedErrorRoute] = useState<string | null>(null);

  // Group errors by route
  const groupedErrors = useMemo(() => {
    const errorMap = new Map<string, GroupedErrorInterface>();

    endpointErrors.forEach(err => {
      if (!errorMap.has(err.route)) {
        errorMap.set(err.route, {
          route: err.route,
          method: err.method,
          count: 1,
          errors: [err],
          latestError: err
        });
      } else {
        const group = errorMap.get(err.route)!;
        group.count += 1;
        group.errors.push(err);

        // Update latest error if this one is more recent
        if (new Date(err.createdAt) > new Date(group.latestError.createdAt)) {
          group.latestError = err;
        }
      }
    });

    // Convert map to array and sort by count (descending)
    return Array.from(errorMap.values())
      .sort((a, b) => b.count - a.count);
  }, [endpointErrors]);

  // Get all API routes with their health status
  const apiRoutesHealth = useMemo(() => {
    // Create an array of all API routes
    const allRoutes = Object.values(API_URL_ROUTES);

    // Map each route to an object with route and health status
    return allRoutes.map(route => {
      // Remove the leading slash from the route
      const routeWithoutSlash = route.startsWith('/') ? route.substring(1) : route;

      // Check if there are any errors for this route
      const hasErrors = endpointErrors.some(err =>
        // Check if the error route includes this API route
        err.route.includes(route)
      );

      // Find the matching error group if any
      const matchingErrorGroup = groupedErrors.find(group =>
        group.route.includes(route)
      );

      return {
        route: routeWithoutSlash,
        originalRoute: route,
        isHealthy: !hasErrors,
        latestError: matchingErrorGroup ? matchingErrorGroup.latestError : null
      };
    }).sort((a, b) => {
      // Sort by health status (unhealthy first) and then alphabetically by route
      if (a.isHealthy !== b.isHealthy) {
        return a.isHealthy ? 1 : -1;
      }
      return a.route.localeCompare(b.route);
    });
  }, [endpointErrors, groupedErrors]);

  // Group admin stat events by eventAction
  const groupedAdminStats = useMemo(() => {
    const statsMap = new Map<string, GroupedAdminStatInterface>();

    adminStatEvents.forEach(event => {
      if (!statsMap.has(event.eventAction)) {
        statsMap.set(event.eventAction, {
          eventAction: event.eventAction,
          count: 1,
          events: [event],
          latestEvent: event
        });
      } else {
        const group = statsMap.get(event.eventAction)!;
        group.count += 1;
        group.events.push(event);

        // Update latest event if this one is more recent
        if (new Date(event.createdAt) > new Date(group.latestEvent.createdAt)) {
          group.latestEvent = event;
        }
      }
    });

    // Convert map to array and sort by count (descending)
    return Array.from(statsMap.values())
      .sort((a, b) => b.count - a.count);
  }, [adminStatEvents]);

  useEffect(() => {
    const fetchErrors = async () => {
      setLoading(true);
      setError(null);

      try {
        // Make sure we're using the most current token
        const currentToken = localStorage.getItem('token') || authService.token;
        const errorService = new ErrorService(currentToken);

        await errorService.getEndpointErrors((errors) => {
          setEndpointErrors(errors);
          if (errors.length === 0) {
            // This could be due to no errors or an auth issue
            console.log('No endpoint errors returned');
          }
        });
      } catch (err) {
        console.error('Error fetching endpoint errors:', err);
        setError('Failed to fetch endpoint errors. Please try refreshing the page.');
      } finally {
        setLoading(false);
      }
    };

    fetchErrors();
  }, [authService.token]);

  useEffect(() => {
    const fetchAdminStatEvents = async () => {
      setLoadingStats(true);
      setStatsError(null);

      try {
        // Make sure we're using the most current token
        const currentToken = localStorage.getItem('token') || authService.token;
        const adminStatsService = new AdminStatsService(currentToken);

        await adminStatsService.getAdminStatEvents((events) => {
          setAdminStatEvents(events);
          if (events.length === 0) {
            // This could be due to no events or an auth issue
            console.log('No admin stat events returned');
          }
        });
      } catch (err) {
        console.error('Error fetching admin stat events:', err);
        setStatsError('Failed to fetch admin stat events. Please try refreshing the page.');
      } finally {
        setLoadingStats(false);
      }
    };

    fetchAdminStatEvents();
  }, [authService.token]);

  const formatDate = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const viewErrorDetails = (route: string) => {
    setSelectedErrorRoute(route);
  };

  // Convert action string from underscores to human readable format
  const formatActionString = (action: string) => {
    // Replace underscores with spaces
    let formattedAction = action.replace(/_/g, ' ');

    // Capitalize first letter of each word
    formattedAction = formattedAction
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return formattedAction;
  };

  // Format route by removing API base path
  const formatRoute = (route: string) => {
    return route.replace('/api/v1.0.0/', '');
  };

  // Extract error message from error text JSON
  const extractErrorMessage = (errorText: string) => {
    try {
      const errorJson = JSON.parse(errorText);
      return errorJson.message || errorText;
    } catch (e) {
      // If parsing fails, return the original text
      return errorText;
    }
  };

  return (
    <PageLayout user={user} client={client} authService={authService} className={classes.ErrorPage}>
      <div className={classes.content}>
        <h1 className={pageClasses.title}>Error Page</h1>

        {/* Admin Stat Events Table */}
        {loadingStats ? (
          <div className={classes.loading}>Loading admin stat events...</div>
        ) : statsError ? (
          <div className={classes.errorMessage}>
            <p>{statsError}</p>
            <button className={classes.button} onClick={() => window.location.reload()}>
              Refresh Page
            </button>
          </div>
        ) : (
          <div className={classes.tableContainer}>
            <h2 className={classes.subtitle}>Admin Stat Events (Last 7 Days)</h2>

            {groupedAdminStats.length === 0 ? (
              <div className={classes.noErrors}>No admin stat events found in the last 7 days.</div>
            ) : (
              <div className={cn(tableClasses.TableScrollWrapper)}>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Count</TableHead>
                      <TableHead>Latest Time</TableHead>
                      <TableHead>Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {groupedAdminStats.map((group) => (
                      <TableRow
                        key={group.eventAction}
                        className={classes.statRow}
                      >
                        <TableCell className={classes.countCell}>{group.count}</TableCell>
                        <TableCell>{formatDate(group.latestEvent.createdAt)}</TableCell>
                        <TableCell>{formatActionString(group.eventAction)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        )}

        {/* Endpoint Errors Table */}
        {loading ? (
          <div className={classes.loading}>Loading endpoint errors...</div>
        ) : error ? (
          <div className={classes.errorMessage}>
            <p>{error}</p>
            <button className={classes.button} onClick={() => window.location.reload()}>
              Refresh Page
            </button>
          </div>
        ) : (
          <div className={classes.tableContainer}>
            <h2 className={classes.subtitle}>API Routes Health Status (Last 24 Hours)</h2>

            <div className={cn(tableClasses.TableScrollWrapper)}>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Health</TableHead>
                    <TableHead>Route</TableHead>
                    <TableHead>Latest Error</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {apiRoutesHealth.map((routeHealth) => (
                    <TableRow
                      key={routeHealth.route}
                      className={routeHealth.latestError ? classes.errorRow : ''}
                      onClick={routeHealth.latestError ? () => viewErrorDetails(routeHealth.latestError?.route || '') : undefined}
                    >
                      <TableCell>
                        <span
                          className={cn(
                            classes.healthIndicator,
                            routeHealth.isHealthy ? classes.healthyIndicator : classes.unhealthyIndicator
                          )}
                        />
                      </TableCell>
                      <TableCell>{routeHealth.route}</TableCell>
                      <TableCell>
                        {routeHealth.latestError
                          ? formatDate(routeHealth.latestError.createdAt)
                          : 'No errors'
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}

        {selectedErrorRoute && (
          <div className={classes.errorBox}>
            <h2 className={classes.title}>Error Details for Route: {formatRoute(selectedErrorRoute)}</h2>
            <div className={classes.errorSummary}>
              <p>
                <strong>Total Occurrences:</strong> {
                  groupedErrors.find(group => group.route === selectedErrorRoute)?.count || 0
                }
              </p>
            </div>

            <div className={classes.errorInstances}>
              {groupedErrors
                .find(group => group.route === selectedErrorRoute)
                ?.errors
                .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                .map((error, index) => (
                  <div key={error.id} className={classes.errorInstance}>
                    <h3 className={classes.instanceTitle}>Instance {index + 1}</h3>
                    <div className={classes.errorDetails}>
                      <div className={classes.errorDetail}>
                        <strong>Time:</strong> {formatDate(error.createdAt)}
                      </div>
                      {error.user?.email && (
                        <div className={classes.errorDetail}>
                          <strong>User Email:</strong> {error.user.email}
                        </div>
                      )}
                      <div className={classes.errorDetail}>
                        <strong>Route:</strong> {formatRoute(error.route)}
                      </div>
                      <div className={classes.errorDetail}>
                        <strong>Error Text:</strong>
                        <div className={classes.errorCode}>
                          <pre>{extractErrorMessage(error.errorText)}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              }
            </div>

            <button className={classes.button} onClick={() => setSelectedErrorRoute(null)}>
              Close Details
            </button>
          </div>
        )}
      </div>
    </PageLayout>
  );
};

export default ErrorPage;
