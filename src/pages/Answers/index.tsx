import { useRef, useState } from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { CSVLink } from "react-csv";
import cn from 'classnames';

import * as interfaces from 'interfaces';
import { AnswersService, AuthService, FileService, NgpVanService } from 'services';
import { useServiceContext } from 'services/ServiceProvider';

import AnswersList from 'components/AnswersList';
import Button from "shared/Button";
import ImportingVisitors from "popups/ImportingVisitors";

import pageClasses from 'styles/PageWrapper.module.scss';
import ButtonClasses from "shared/Button/Button.module.scss";
import classes from './Answers.module.scss';
import PageLayout from 'shared/PageLayout';

export default function Answers(
  props: {
    answersService: AnswersService,
    ngpVanService: NgpVanService,
    fileService: FileService,
    user?: interfaces.UserInterface,
    authService: AuthService,
  }) {
  const { ngpVanService, answersService, fileService, user, authService } = props
  const { adminStatsService } = useServiceContext();

  const activeTab = "Answers"
  const [isOpen, setIsOpen] = useState(false)

  const [importFileName, setImportFileName] = useState('');
  const [importError, setImportError] = useState("")
  const [importStatus, setImportStatus] = useState<"processing" | "completed" | "failed">("processing")
  const [importedUsers, setImportedUsers] = useState<interfaces.NgpVanInterface[]>([])

  const fileReader = new FileReader();
  const inputFile = useRef<any>(null)

  const csvData = [
    ["First Name", "Last Name", "Email", "ZIP", "City", "State"],
    ["Sacha", "Baron", "<EMAIL>", "10001", "Manhattan", "NY"]
  ];

  const csvFileToArray = (str: string): [] => {
    const csvHeader = str.slice(0, str.indexOf("\n")).split(",");
    const csvRows = str.slice(str.indexOf("\n") + 1).split("\n").filter((item) => item !== "");

    const array: any = csvRows.map(i => {
      const values = i.split(",");
      const obj = csvHeader.reduce((object: any, header: any, index) => {
        object[header] = values[index];
        return object;
      }, {});
      return obj;
    });


    return array
  };

  const handleFileChange = (e: any) => {
    const file = (e.target?.files || [])[0]

    if (file) {
      setImportFileName(file.name);

      fileReader.onload = (event: any) => {
        const text = event.target.result;

        handleNgpVanImport(file.name, csvFileToArray(text))
      };

      setIsOpen(true);
      setImportStatus("processing")
      fileReader.readAsText(file);
    }

    e.target.value = null
  }

  const handleNgpVanImport = (fileName: string, userList: any[]) => {
    const values: any = {
      savedListName: fileName,
      users: []
    }

    const fixCsvString = function (s: string) {
      return s.replace(/"|\/"| /g, '').replace(/\r|\r\n|\n|\\r|\\r\\n|\\n/g, '').replace(/mailto:/ig, '')
    }

    function mapHeaderToKey(header: string) {
      const lowerHeader = header.toLowerCase();

      if (lowerHeader.includes("last")) return "lastname";
      if (lowerHeader.includes("name")) return "firstname";
      if (lowerHeader.includes("email")) return "email";
      if (lowerHeader.includes("zip")) return "zip";
      if (lowerHeader.includes("city")) return "city";
      if (lowerHeader.includes("state")) return "state";

      return header;
    }

    for (var csvUser of userList) {
      const fixedKeys = fixCsvString(Object.keys(csvUser).join('&&&')).toLowerCase().split('&&&')
      const fixedValues = fixCsvString(Object.values(csvUser).join('&&&')).split('&&&')

      var user: any = {}

      for (const index in fixedKeys) {
        const key = fixedKeys[index].toString().trim();
        const mappedKey = mapHeaderToKey(key);
        user[mappedKey] = fixedValues[index];
      }

      values.users.push({
        firstName: user["firstname"],
        lastName: user["lastname"],
        email: (user["email"] || '').toString().toLowerCase(),
        zip: user["zip"],
        location: !user["state"] && !user["city"] ? null : `${user["state"]} / ${user["city"]}`
      });
    }

    setImportedUsers(values.users);

    let isInvalid = false;
    for (var i in values.users) {
      if (!importError && !isInvalid) {
        const validEmail = (values.users[i]["email"] || "").toLowerCase().match(/[^@]+@[^.]+\.[a-z]{2,}(.*)$/i) !== null;

        if (!values.users[i]["firstName"]) {
          console.error('import - error - firstName', values.users[i].firstName);
          setImportError(`Line #${parseInt(i) + 1} - Please specify a first name.`);
          isInvalid = true;
        }
        else if (!values.users[i]["lastName"]) {
          console.error('import - error - lastName', values.users[i].lastName);
          setImportError(`Line #${parseInt(i) + 1} - Please specify a last name.`);
          isInvalid = true;
        }
        else if (!values.users[i]["email"] || !validEmail) {
          console.error('import - error - email', values.users[i].email);
          setImportError(`Line #${parseInt(i) + 1} - Please specify a valid email.`);
          isInvalid = true;
        }
      }

      if (parseInt(i) === values.users.length - 1) {
        // console.log( 'import - values', values )

        if (!isInvalid)
          ngpVanService?.importNgpVan(values, () => { }).then(
            (response?: interfaces.NgpVanInterface[]) => {
              setImportStatus("completed")
            }
          )
        else {
          // console.error('import - error', importError, values.users[i]);
          setImportStatus("failed");
        }
      }
    }
  }

  return (
    <PageLayout user={user} client={user?.client} authService={authService} className={classes.Answers}>
      <div className={classes.Answers}>
        <div className={pageClasses.Wrapper}>
          <div className={`${pageClasses.Page} ${pageClasses.wide}`}>
            <div className={pageClasses.TitleWithTabs}>
              <div className={pageClasses.Tabs}>
                <div
                  className={cn(pageClasses.Tab, pageClasses.active)}
                >
                  Uploaded
                </div>
              </div>
              <div className={pageClasses.Buttons}>
                <CSVLink
                  data={csvData}
                  filename={"REPD-Visitor-List.csv"}
                  className={cn(ButtonClasses.Button, pageClasses.Button)}
                  onClick={() => adminStatsService?.trackEvent('Answers', 'view_sample_csv')}
                >
                  <FontAwesomeIcon icon="download" />
                  View Sample CSV
                </CSVLink>

                <input type='file' id='file' ref={inputFile} accept=".csv" style={{ display: 'none' }} onChange={handleFileChange} />

                <Button
                  iconText="upload"
                  text={user?.client?.clientType === 'Government' ? 'Import Contact list' : 'Import Voter list'}
                  callback={() => {
                    inputFile.current.click();
                    adminStatsService?.trackEvent('Answers', user?.client?.clientType === 'Government' ? 'import_contact_list' : 'import_voter_list');
                  }}
                />
              </div>
            </div>

            {isOpen && (
              <ImportingVisitors
                handleClose={() => { setIsOpen(false); setImportError(""); }}
                status={importStatus}
                fileName={importStatus === "failed" ? importError : importFileName}
                importedUsers={importedUsers}
              />
            )}

            <div className={pageClasses.limitHeight}>
              <AnswersList answersService={answersService} fileService={fileService} ngpVanService={ngpVanService} user={user} isDrafts={false} />
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
