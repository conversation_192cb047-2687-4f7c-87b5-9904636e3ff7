// import { ToastContainer } from 'react-toastify';

import { UserInterface, ClientInterface, SessionInterface } from 'interfaces';
import Button from 'shared/Button';

import { UserService, ClientService, AuthService } from 'services';
import { useServiceContext } from 'services/ServiceProvider';

import pageClasses from 'styles/PageWrapper.module.scss';
import classes from './Team.module.scss';
import UserTable from 'shared/UserTable';
import { useEffect, useState } from 'react';
import PageLayout from 'shared/PageLayout';

interface ProfileProps {
  user?: UserInterface;
  setUser: Function;
  userService?: UserService;
  clientService: ClientService;
  authService: AuthService;
}

export default function Team(props: ProfileProps) {
  const { user, setUser, userService, clientService, authService } = props;
  const { adminStatsService } = useServiceContext();
  const client: ClientInterface | any = clientService.client;

  const [records, setRecords] = useState<SessionInterface[]>([]);

  const fetchClients = () => {
    clientService?.list((s: SessionInterface[]) => {
      setRecords(s);
    })
  }

  useEffect(fetchClients, [clientService]);

  return <PageLayout user={user} client={client} authService={authService}>
    <div className={classes.Team}>
      <div className={pageClasses.Wrapper}>
        <h1 className={pageClasses.title}>
          Team

          <Button text="Sign Out" callback={() => {
            adminStatsService?.trackEvent('Team', 'sign_out');
            authService.logOut();
          }} />
        </h1>
      </div>

      {/* <ToastContainer position="top-center" /> */}

      <UserTable fetchClients={fetchClients} userService={userService} clientSessionRecords={records} selectedClient={client} authUser={user} />

    </div>
  </PageLayout>
}
