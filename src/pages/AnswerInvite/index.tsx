import { useState, useEffect, useMemo } from "react";
import * as interfaces from "interfaces";
import VideoUploadModal from "../../components/VideoHelpers/VideoUpload/VideoUploadModal";
// import VideoRecordModal from "../../components/VideoHelpers/VideoRecord/VideoRecordModal";
import { useServiceContext } from "../../services/ServiceProvider";

interface AnswerInviteProps {
  questionId: string;
}

export default function AnswerInvite({ questionId }: AnswerInviteProps) {
  const { questionService, clientService } = useServiceContext();
  const [notes, setNotes] = useState("");
  const [question, setQuestion]: interfaces.QuestionInterface | any =
    useState();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (questionId)
      questionService
        ?.getQuestion(questionId, setQuestion)
        .then(() => setLoading(false));
  }, [questionId]);

  useEffect(() => {
    if (question) setNotes(question.suggestedNotes);
  }, [question]);

  const [isVideoUploadModalOpen, setIsVideoUploadModalOpen] = useState(false);
  // const [isVideoRecordModalOpen, setIsVideoRecordModalOpen] = useState(false);

  // const expired = useMemo(() => {
  //   if (question?.shares?.length) {
  //     const lastShare = question && question?.shares?.[question?.shares?.length - 1];
  //     return lastShare && lastShare.dueDate && new Date(lastShare.dueDate) < new Date();
  //   } else return false;
  // }, [question]);

  if (!question?.text && !loading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] bg-red-100 p-4">
        <div className="bg-white shadow-lg rounded-lg p-6 max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">
            Invite Expired
          </h1>
          <p className="text-gray-700 mb-6">
            Sorry, this invite is invalid or has expired. Please contact the sender for a new
            invite.

            If the issue persists, please contact Rep'd support at{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600">
              <EMAIL>
            </a>
            .
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-80px)] bg-gray-100 p-4">
        <div className="bg-white shadow-lg rounded-lg p-6 max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Loading...</h1>
          <p className="text-gray-700 mb-6">
            Please wait while the question is being loaded.
          </p>
          <div className="flex justify-center">
            <svg
              className="animate-spin h-8 w-8 text-gray-900"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8v8z"
              ></path>
            </svg>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col items-center justify-center  min-h-[calc(100vh-80px)] bg-gray-100 p-4">
        <div className="bg-white shadow-lg rounded-lg p-6 max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Welcome!</h1>
          <p className="text-gray-700 mb-6">
            You have been invited to answer the following question:
          </p>

          <div className="bg-gray-200 p-4 rounded-lg mb-6 text-justify">
            <p className="text-gray-900">{question.text}</p>
          </div>

          <div className="flex space-x-4 justify-center">
            <button
              onClick={() => setIsVideoUploadModalOpen(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Capture video
            </button>
          </div>
        </div>
      </div>
      {isVideoUploadModalOpen && (
        <VideoUploadModal
          notes={notes}
          onClose={() => {
            setIsVideoUploadModalOpen(false);
          }}
          question={question}
          clientService={clientService}
        />
      )}
      {/* {isVideoRecordModalOpen && (
        <VideoRecordModal
          notes={notes}
          onClose={() => {
            setIsVideoRecordModalOpen(false);
          }}
          question={question}
          clientService={clientService}
        />
      )} */}
    </>
  );
}
