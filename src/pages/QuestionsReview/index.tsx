import * as interfaces from 'interfaces';
import QuestionsReviewList from 'components/QuestionsReviewList';
import PageLayout from 'shared/PageLayout';

import { QuestionService } from 'services/questions.service';
import { AnswersService, AuthService, ClientService, FileService, UserService } from 'services';

export default function QuestionsReview(props: {
  service?: QuestionService,
  answersService?: AnswersService,
  fileService?: FileService,
  userService?: UserService,
  clientService?: ClientService,
  client: interfaces.ClientInterface,
  user?: interfaces.UserInterface,
  authService: AuthService,
}) {
  const { service, answersService, client, user, fileService, userService, clientService, authService } = props;

  return (
    <PageLayout user={user} client={client} authService={authService}>
      <QuestionsReviewList 
        service={service} answersService={answersService} fileService={fileService} userService={userService} clientService={clientService}
        client={client} user={user} />
    </PageLayout>
  );
}
