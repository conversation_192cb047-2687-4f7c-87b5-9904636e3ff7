.wrapper {
  padding: 0px 40px;
  display: flex;
  justify-content: center;
}

.mainContainer {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-xl);
  max-width: 100%;
}

.contentWrapper {
  height: 944px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-11xl) 0px 0px;
  box-sizing: border-box;
  max-width: 100%;
  text-align: left;
  font-size: var(--font-size-xl);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

.contentPanel {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 20px;
  min-height: 1103px;
  max-width: 100%;
}

.pageTitle {
  margin: 0;
  flex: 1;
  position: relative;
  font-size: inherit;
  font-weight: 500;
  font-family: inherit;
  display: inline-block;
  min-width: 80px;
  max-width: 100%;
}

.filterContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-xl);
  max-width: 100%;
  font-size: var(--font-size-15xl);
}

.filterBarContainer {
  width: 100%;
}

// Removed shared metric styling (now in Shared.module.scss)

.metricsContainer {
  width: 774px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0px var(--padding-xl) 0px 0px;
  box-sizing: border-box;
  gap: var(--gap-xl);
  max-width: 100%;
  text-align: center;
}

.chartTitle {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 500;
  font-family: inherit;
  display: inline-block;
  padding-bottom: 20px;
}

.chartTitleContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xs-5) 0px 0px;
}

.moreInfoIcon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
  cursor: pointer;
}

.chartHeaderContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-xl);
}

.chartContent {
  align-self: stretch;
  height: 317px;
  position: relative;
  max-width: 100%;
  text-align: center;
  font-size: var(--font-size-base);
}

.chartContainer {
  align-self: stretch;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xl) var(--padding-6xl);
  gap: var(--gap-10xs);
  max-width: 100%;
}

.tableContainer {
  align-self: stretch;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xl) var(--padding-6xl) var(--padding-15xl);
  gap: var(--gap-xl);
  max-width: 100%;
}

/* Transposed Table Styles */
:global {
  .transposed-table-container {
    width: 100%;
  }

  .transposed-table {
    width: 100%;
    border-collapse: collapse;
  }

  .transposed-row {
    display: flex;
    width: 100%;
    border-bottom: 1px solid var(--color-gainsboro-100);
  }

  .transposed-header {
    width: 150px;
    min-width: 150px;
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    background-color: #f9fafb;
    border-right: 1px solid var(--color-gainsboro-100);
  }

  .transposed-cells-container {
    flex: 1;
    padding: 0;
  }

  .transposed-cells-scroll {
    display: flex;
    overflow-x: auto;
    width: 100%;
  }

  .transposed-cell {
    min-width: 180px;
    padding: 12px 16px;
    cursor: pointer;
    border-right: 1px solid var(--color-gainsboro-100);
    text-align: right;
  }

  .transposed-cell:hover {
    background-color: #f9fafb;
  }

  /* Mobile Email Styles */
  .mobile-email-container {
    width: 100%;
  }

  .mobile-email-card {
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 20px 0px 10px 0px;
    background-color: white;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px #D3D9E3 solid;
  }

  .mobile-email-card:hover {
    background-color: #f9fafb;
  }

  .mobile-email-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .mobile-email-row:last-child {
    border-bottom: none;
  }

  .mobile-email-header {
    font-weight: 700;
    color: #000;
    text-align: left;
    padding-right: 12px;
    flex: 1;
  }

  .mobile-email-value {
    text-align: right;
    flex: 1;
  }

  .mobile-emails-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    width: 100%;
  }

  .mobile-email-table {
    width: 100%;
    border-collapse: collapse;
  }

  .mobile-email-row {
    border-bottom: 1px solid #f0f0f0;
  }

  .mobile-email-row:last-child {
    border-bottom: none;
  }

  .mobile-email-header {
    font-weight: 700;
    color: #000;
    text-align: left;
    padding: 8px 0;
  }

  .mobile-email-value {
    text-align: right;
    padding: 8px 0;
  }

  .mobile-header {
    font-weight: 500;
    font-size: 20px;
    color: #445472;
    margin-bottom: 0px;
  }
}

@media screen and (max-width: 975px) {
  .pageTitle {
    font-size: var(--font-size-8xl);
  }

  .filterContainer {
    flex-wrap: wrap;
  }

  // Removed shared media query styles

  .contentWrapper {
    height: auto;
    max-width: 100%;
  }

  .mainContainer {
    padding-left: var(--padding-xl);
    padding-right: var(--padding-xl);
    box-sizing: border-box;
  }

  :global {
    .transposed-header {
      width: 100px;
      min-width: 100px;
    }

    .transposed-cell {
      min-width: 150px;
    }
  }
}

@media screen and (max-width: 725px) {
  .metricsContainer {
    flex-wrap: wrap;
  }

  .hideOnMobile {
    display: none;
  }

  :global {
    .transposed-table {
      border: none;
      background: transparent;
    }

    .transposed-row {
      flex-direction: row;
      border-bottom: 1px solid #eee;
      margin-bottom: 8px;
      padding: 8px 0;
      background: transparent;
    }

    .transposed-header {
      width: 50%;
      min-width: unset;
      border: none;
      background: transparent;
      font-weight: 700;
      text-align: left;
      padding: 8px 0;
    }

    .transposed-cells-container {
      width: 50%;
      border: none;
    }

    .transposed-cells-scroll {
      display: block;
      overflow: visible;
    }

    .transposed-cell {
      width: 100%;
      min-width: unset;
      border: none;
      text-align: right;
      padding: 8px 0;
    }

    .mobile-email-card {
      margin-bottom: 16px;
    }
  }
}

@media screen and (max-width: 640px) {
  .pageTitle {
    font-size: var(--font-size-xl);
  }

  // Removed shared media query styles

  .chartTitle {
    font-size: var(--font-size-base);
  }

  .chartHeaderContainer {
    flex-wrap: wrap;
  }

  .chartContainer {
    padding: 20px 20px 0px 20px;
    box-sizing: border-box;
  }

  .chartTitle {
    font-size: 20px;
  }

  .tableContainer {
    height: auto;
    padding-top: var(--padding-xl);
    padding-bottom: var(--padding-3xl);
    box-sizing: border-box;
    padding-left: var(--padding-lg);
    padding-right: var(--padding-lg);
  }

  .wrapper {
    padding: 0px 20px;
  }

  .mainContainer {
    padding: 0;
  }

  :global {
    .mobile-email-row {
      flex-direction: row;
      padding: 0;
    }

    .mobile-email-row {
      border: none;
    }

    .mobile-email-card {
      margin: 0;
    }

    .mobile-emails-grid {}

    .mobile-email-header {
      font-size: 14px;
      font-weight: 700;
      color: #445472;
      padding: 0;
    }

    .mobile-email-value {
      text-align: right;
      width: 100%;
      font-size: 14px;
      color: #445472;
      padding: 0;
    }
  }
}