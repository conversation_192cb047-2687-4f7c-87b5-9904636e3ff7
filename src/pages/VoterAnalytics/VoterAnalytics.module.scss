@import 'src/styles/variables';

.VoterAnalytics {
  .Wrapper {
    display: flex;
  }

  .Row {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;

    &.bottom .StatTotal {
      margin-bottom: 0;
    }
  }

  .StatTotal,
  .Table,
  .Chart {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: space-between;
    margin: 0 20px 20px 0;
    padding: 20px;
    border: 1px solid $grey;
    border-radius: 10px;
    overflow: auto;
  }

  .title {
    width: 100%;
    font-weight: 600;
    margin-bottom: 20px;
    line-height: 20px;
  }

  .StatTotal {
    min-width: calc(25% - 21px);
    text-align: center;

    .centerNumber {
      color: $blue;
      font-size: 40px;
      margin-bottom: 10px;
    }

    .qualifier {
      color: $blue;
      margin-bottom: 10px;
    }
  }

  .Table {
    min-width: calc(50% - 21px);
    border-radius: 10px;

    &.edge {
      min-width: calc(50%);
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 0;
      width: 100%;
      border-bottom: 1px solid $grey;
      color: $blue;

      .amount {
        color: $dark-blue;
      }

      .text {}
    }

    .row:last-child {
      border: none;
      padding-bottom: 0;
    }
  }

  .Chart {
    width: 100%;
    margin-right: 0;
    flex-direction: row;

    canvas {
      background: #f9F9F999;
      padding: 0 10px 10px 0;
      max-height: 300px;
    }
  }

  .edge {
    margin-right: 0;
  }

  // Custom
  .videoThumbnail {
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 0 0px 0 #00000055;

    &:hover {
      opacity: .9;
      box-shadow: 0 0 10px 0 #00000055;
    }
  }
}

@media screen and (max-width: 640px) {
  .VoterAnalytics {

    .StatTotal,
    .Table {
      width: 100%;
      margin-right: 0;
    }
  }
}

.engagementsTitle,
.filterContainer {
  display: flex;
  align-items: center;
  max-width: 100%;
}

.filterBarContainer {
  width: 100%;
}

.engagementsTitle {
  margin: 0;
  height: 54px;
  flex: 1;
  position: relative;
  font-size: inherit;
  font-weight: 500;
  font-family: inherit;
  min-width: 179px;
}

.filterContainer {
  align-self: stretch;
  flex-direction: row;
  justify-content: flex-start;
  gap: var(--gap-xl);
  font-size: var(--font-size-15xl);
}

.tabItem {
  position: relative;
  font-weight: 500;
  display: inline-block;
  font-size: 20px;
  padding-bottom: 5px;
  margin-bottom: 15px;
  cursor: pointer;
}

.tabItem:hover {
  transition-duration: 0ms;
  color: var(--color-crimson);
  border-bottom: 1px solid var(--color-crimson);
}

.selectedTab {
  color: var(--color-crimson);
  border-bottom: 1px solid var(--color-crimson);
}

.tabsContainer {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xs-5) 0 0;
}

.moreInfoIcon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
}

.chartHeaderContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-xl);
}

.deviceUsageChart {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0 var(--padding-sm) 0 var(--padding-mini);
}

.deviceIcon {
  width: 14px;
  height: 14px;
  position: relative;
  border-radius: 50%;
  background-color: #5cc776;
}

.deviceIconWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-10xs) 0 0;
}

.deviceName {
  flex: 1;
  position: relative;
}

.deviceContent,
.deviceLabels {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.deviceLabels {
  align-self: stretch;
  flex-direction: row;
  gap: var(--gap-7xs);
}

.deviceContent {
  flex: 1;
  flex-direction: column;
  padding: var(--padding-10xs-5) 0 0;
}

.devicePercentage {
  position: relative;
  font-size: var(--font-size-xl);
  color: var(--color-black);
  text-align: right;
}

.deviceItemContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-lgi);
}

.deviceContainer,
.deviceUsageContentWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.deviceContainer {
  align-items: flex-start;
  gap: var(--gap-3xs);
  flex-shrink: 0;
}

.deviceUsageContentWrapper {
  align-items: flex-end;
  gap: 19.9px;
}

.deviceUsageContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  color: var(--color-slategray);
}

.sentimentChartContentParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: flex-end;
  gap: 37px;
  max-width: 100%;
}

.sourcesTitle {
  align-self: stretch;
  position: relative;
  font-weight: 500;
}

.trafficSourceIcon {
  height: 34px;
  //width: 34px;
  position: relative;
}

.clientLogo {
  width: fit-content;
}

.sourceLink {
  font-size: 14px;
  padding-top: 4px;
}

.sourceDivider {
  height: 1px;
  width: 20px;
  position: relative;
}

.sourcePercentage {
  flex: 1;
  position: relative;
}

.trafficSourceItem {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.trafficSourceContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-3xs);
}

.trafficSourcesContainer,
.sentimentChartContainer,
.sourcesContainer {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.trafficSourcesContainer {
  flex-direction: row;
  gap: 40px;
  text-align: right;
  font-size: var(--font-size-5xl);
  color: var(--color-black);
}

.sentimentChartContainer,
.sourcesContainer {
  flex-direction: column;
  max-width: 100%;
}

.sourcesContainer {
  gap: var(--gap-3xs);
  font-size: var(--font-size-xl);
  color: var(--color-slategray);
}

.sentimentChartContainer {
  width: 1037px;
  gap: 26px;
  font-size: var(--font-size-base);
  color: var(--color-black);
}

.chartContainer {
  align-self: stretch;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  padding: var(--padding-5xl) var(--padding-6xl) 43px;
  gap: 3px;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.dataContainer {
  align-self: stretch;
  gap: 30px;
  max-width: 100%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.dataContainerWrapper {
  height: 944px;
  flex: 1;
  padding: var(--padding-11xl) 0 0;
  box-sizing: border-box;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.sentimentChartContent {
  flex: 1;
  min-width: 539px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  max-width: 100%;
}

@media screen and (max-width: 975px) {
  .engagementsTitle {
    font-size: var(--font-size-8xl);
    min-width: auto;
  }

  .filterContainer {
    flex-wrap: wrap;
  }

  .trafficSourcesContainer {
    flex-wrap: wrap;
  }

  .dataContainerWrapper {
    padding: 30px 0px;
    height: auto;
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .metricContainer {
    gap: 25px;
    width: 100%;
    flex-direction: column;
  }

  .sentimentChartContent {
    min-width: 100%;
  }

  .sentimentChartContentParent {
    gap: var(--gap-lg);
  }

  .chartContainer {
    padding-top: var(--padding-xl);
    padding-bottom: 28px;
    box-sizing: border-box;
  }

  .engagementsTitle {
    font-size: var(--font-size-xl);
  }

  .totalTraffic {
    font-size: var(--font-size-base);
  }

  .engagementRate {
    font-size: var(--font-size-base);
  }

  .chartHeaderContainer {
    flex-wrap: wrap;
  }

  .devicePercentage,
  .sourcesTitle {
    font-size: var(--font-size-base);
  }

  .sourcePercentage {
    font-size: var(--font-size-lgi);
  }

  .trafficSourcesContainer {
    gap: var(--gap-xl);
  }

  .hideOnMobile {
    display: none;
  }

  .filterContainer {
    gap: 10px;
  }

  .dataContainer {
    gap: 10px;
  }
}