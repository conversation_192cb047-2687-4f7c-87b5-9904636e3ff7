import React, { useState, useMemo, useRef } from 'react';
import { motion } from "framer-motion";
import * as interfaces from 'interfaces';
import { AuthService, ClientService, StatService } from 'services';
import { useServiceContext } from 'services/ServiceProvider';
import {
  useStats,
  useAnalytics,
  useQuestionAnalytics,
  useTrendingAnalytics
} from 'hooks/reactQuery/useAnalyticsQueries';

import PageLayout from 'shared/PageLayout';
import defaultLogo from 'assets/logo/Logo-Full.png';

import classes from './VoterAnalytics.module.scss';
import sharedClasses from './Shared.module.scss';

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip
} from 'chart.js';
import Filter from 'components/VoterAnalytics/Filter';
import AnalyticsButton from 'components/VoterAnalytics/Buttons';
import Queries from 'components/VoterAnalytics/Queries';
import QueryTrends from 'components/VoterAnalytics/QueryTrends';
import FilterBar from 'components/VoterAnalytics/FilterBar';
import { Chart } from 'components/VoterAnalytics/Chart';
import ExportButton from 'components/VoterAnalytics/ExportElementBtn';
// import { getPercent, mockData } from 'utils/mockData';
import { useFilterStore } from 'hooks/zustand/filterStore';
import { Popover, PopoverContent, PopoverTrigger } from 'shared/ui/popover';
import { PieChartComponent, PieChartColors } from 'shared/ui/pie';
import { cn, numberWithCommas } from 'utils/utils';
import moment from 'moment';
import VideoUploadModal from 'components/VideoHelpers/VideoUpload/VideoUploadModal';
import EditQuestionModal from 'components/EditQuestionModal';
import { CSVLink } from 'react-csv';
import Loader from 'components/Loader';

import logo from 'assets/logo/Logo.png';
import { processChartData } from 'utils/stats';
import xlsx from 'json-as-xlsx';
import { useUIStore } from 'hooks/zustand/uiStore';
import useExportPdf from 'hooks/useExportPdf.hook';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip
);

ChartJS.defaults.borderColor = '#F0F0F0'
ChartJS.defaults.layout.padding = 0
ChartJS.defaults.elements.line.capBezierPoints = true;
ChartJS.defaults.elements.line.tension = 0.5;
ChartJS.defaults.elements.line.borderWidth = 3
ChartJS.defaults.elements.point.radius = 5

export default function VoterAnalytics(props: {
  client: interfaces.ClientInterface;
  user?: interfaces.UserInterface;
  service: StatService;
  authService: AuthService;
  clientService?: ClientService;
}) {
  const { client, clientService, service, user, authService } = props;
  const { adminStatsService, questionService } = useServiceContext();
  const [stats, setStats] = useState<any>([]);
  const [analytics, setAnalytics] = useState<any>([]);
  const [questionAnalytics, setQuestionAnalytics] = useState<any>([]);
  const [trendingAnalytics, setTrendingAnalytics] = useState<any>([]);
  const [isVideoUploadModalOpen, setIsVideoUploadModalOpen] = useState(false);
  const [isEditQuestionModalOpen, setIsEditQuestionModalOpen] = useState(false);
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);
  const [selectedQuestion, setSelectedQuestion] = useState<interfaces.QuestionInterface | null>(null);
  const [sources, setSources] = useState<any>([]);
  const [deviceUsageData, setDeviceUsageData] = useState<any>([]);
  const [selectedTab, setSelectedTab] = useState<'traffic' | 'queries'>('traffic');
  const [engagementsChartData, setEngagementsChartData] = useState<any>([]);
  const filterStore = useFilterStore();
  const uiStore = useUIStore();
  const { uiType, setUiType } = uiStore;
  const { exportPDF } = useExportPdf(setUiType);

  // Update handler to forward parameters
  const exportPDFHandler = (exportEngagements: boolean, exportVideos: boolean, exportEmails: boolean) => {
    exportPDF(exportEngagements, exportVideos, exportEmails, loading);
    adminStatsService?.trackEvent('VoterAnalytics', 'export_pdf');
  };

  const normalizeDomain = (url: string) => {
    return url.replace(/http(s|):\/\/([^.]*.|)/, '').replace(/(\/|\?).*$/, '');
  };

  const squashedSources = useMemo(() => {
    const sourceMap = sources.reduce((acc: any, source: any) => {
      const normalizedDomain = normalizeDomain(source.source);
      if (!acc[normalizedDomain]) {
        acc[normalizedDomain] = { ...source, percentage: 0 };
      }
      acc[normalizedDomain].percentage += Number(source.percentage);
      return acc;
    }, {});

    return Object.values(sourceMap);
  }, [sources]);

  const chartRef = useRef(null);
  const queriesChartRef = useRef(null);
  const pieChartRef = useRef(null);

  const pageViews = useMemo(() =>
    analytics.filter((item: any) => item.event_category === 'PageView'),
    [analytics]
  );

  const sentimentData = useMemo(() => {
    return service.computedValues.sentimentData;
  }, [service.computedValues.sentimentData]);

  const queriesSentimentData = useMemo(() => {
    return service.computedValues.aiQueriesSentimentData;
  }, [service.computedValues.aiQueriesSentimentData]);

  const aiQueriesChartData = useMemo(() => {
    return service.aiQueriesChart;
  }, [service.aiQueriesChart]);

  const chartData = useMemo(() => {
    if (pageViews.length === 0) return [];

    const pageViewsSortedByDate = pageViews.sort((a: any, b: any) => {
      return moment(a.created_at).diff(moment(b.created_at));
    });
    const firstEventDate = moment(pageViewsSortedByDate[pageViewsSortedByDate.length - 1].created_at);
    const lastEventDate = moment(pageViewsSortedByDate[0].created_at);
    const isYearOrMore = lastEventDate.diff(firstEventDate, 'months') <= -6;
    const format = isYearOrMore ? 'YYYY-MM' : 'YYYY-MM-DD';

    // console.log(firstEventDate, lastEventDate, lastEventDate.diff(firstEventDate, 'months'));

    const formattedSentimentData = sentimentData.map((item: any) => {
      return {
        ...item,
        date: moment(item.date).format(format),
      }
    });

    const groupedData = pageViews.reduce((acc: any, event: any) => {
      const date = moment(event.created_at).format(format);

      // console.log(date);

      if (!acc[date]) acc[date] = 0;
      acc[date]++;

      return acc;
    }, {});

    const mergedData = Object.keys(groupedData).map(date => {
      const sentiment = formattedSentimentData.find((item: any) => item.date === date) || {
        happyPercentage: 0,
        neutralPercentage: 0,
        unhappyPercentage: 0
      };

      return {
        date,
        data: groupedData[date],
        happyPercentage: sentiment.happyPercentage,
        neutralPercentage: sentiment.neutralPercentage,
        unhappyPercentage: sentiment.unhappyPercentage
      };
    });

    return mergedData;
  }, [pageViews]);

  const mergedQueriesChartData = useMemo(() => {
    const firstEventDate = moment(aiQueriesChartData[aiQueriesChartData.length - 1]?.day);
    const lastEventDate = moment(aiQueriesChartData[0]?.day);
    const isYearOrMore = lastEventDate.diff(firstEventDate, 'months') <= -6;
    const format = isYearOrMore ? 'YYYY-MM' : 'YYYY-MM-DD';

    const mergedData = aiQueriesChartData.map(day => {
      const sentiment = queriesSentimentData.find((item) => moment(item.date).format(format) === moment(day?.day).format(format)) || {
        happyPercentage: 0,
        neutralPercentage: 0,
        unhappyPercentage: 0
      };

      return {
        date: moment(day?.day).format(format),
        data: day.amount,
        happyPercentage: sentiment.happyPercentage,
        neutralPercentage: sentiment.neutralPercentage,
        unhappyPercentage: sentiment.unhappyPercentage
      };
    });

    return mergedData;
  }, [aiQueriesChartData, queriesSentimentData]);

  const onAnswer = (questionId: string) => {
    // Find the question data from service.computedValues.topCategories (same as Queries component)
    let foundQuestionData: any = null;

    if (service.computedValues.topCategories) {
      for (const category of service.computedValues.topCategories) {
        if (category.questions) {
          foundQuestionData = category.questions.find((q: any) => q.id === questionId) || null;
          if (foundQuestionData) break;
        }
      }
    }

    if (foundQuestionData) {
      // Convert the found question data to a QuestionInterface-like object
      const questionForModal: interfaces.QuestionInterface = {
        id: foundQuestionData.id,
        text: foundQuestionData.text,
        // Add default values for required QuestionInterface fields
        enabled: true,
        createdAt: foundQuestionData.created_at,
        updatedAt: foundQuestionData.created_at,
        clientId: client?.id || '',
        suggestedNotes: '',
        category: '',
        categoryIcon: '',
        votes: 0,
        user: {} as any,
        isApproved: true,
        isDenied: false,
        isAnswered: false,
        isShared: false,
        userVotes: [],
        asked: false,
        blockedForClient: false,
        shares: [],
      };

      setSelectedQuestionId(questionId);
      setSelectedQuestion(questionForModal);
      // Temporarily disabled: setIsEditQuestionModalOpen(true);
      // adminStatsService?.trackEvent('VoterAnalytics', 'open_edit_question_modal');
      // Skip edit modal and go directly to video upload
      setIsVideoUploadModalOpen(true);
      adminStatsService?.trackEvent('VoterAnalytics', 'open_video_upload');
    } else {
      // Fallback: proceed directly to video upload if question not found
      setSelectedQuestionId(questionId);
      setIsVideoUploadModalOpen(true);
      adminStatsService?.trackEvent('VoterAnalytics', 'open_video_upload');
    }
  }

  const handleEditQuestionSave = async (updatedQuestion: interfaces.QuestionInterface) => {
    setSelectedQuestion(updatedQuestion);
    // Refetch the question analytics to update the UI with the new question text
    await questionAnalyticsQuery.refetch();
  };

  const handleProceedWithAnswer = () => {
    setIsEditQuestionModalOpen(false);
    setIsVideoUploadModalOpen(true);
    adminStatsService?.trackEvent('VoterAnalytics', 'proceed_to_video_upload');
  };

  const handleEditQuestionClose = () => {
    setIsEditQuestionModalOpen(false);
    setSelectedQuestionId(null);
    setSelectedQuestion(null);
    adminStatsService?.trackEvent('VoterAnalytics', 'close_edit_question_modal');
  };

  const engagementsCsv = useMemo(() => {
    return service.getEngagementsCsv();
  }, [service, analytics]);

  const trendingData = useMemo(() => {
    return service.computedValues.trendingData
  }, [service.computedValues.trendingData]);

  // Fetch stats
  const statsQuery = useStats(service);

  // Fetch analytics data
  const analyticsQuery = useAnalytics(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin"
  );

  // Fetch question analytics
  const questionAnalyticsQuery = useQuestionAnalytics(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin"
  );

  // Fetch trending analytics
  const trendingAnalyticsQuery = useTrendingAnalytics(
    service,
    filterStore.timeFilter,
    filterStore.locationFilters,
    user?.accessLevel === "super admin"
  );

  // Set loading state based on all queries
  const loading =
    statsQuery.isLoading ||
    analyticsQuery.isLoading ||
    questionAnalyticsQuery.isLoading ||
    trendingAnalyticsQuery.isLoading;

  // Update state when data is available
  useMemo(() => {
    if (statsQuery.data) {
      setStats(statsQuery.data);
    }
  }, [statsQuery.data]);

  useMemo(() => {
    if (analyticsQuery.data) {
      setAnalytics(analyticsQuery.data.analytics);
      setSources(analyticsQuery.data.referralPercentage);
      setDeviceUsageData(analyticsQuery.data.mobileDesktop);
      setEngagementsChartData(analyticsQuery.data.engagementsChart);
    }
  }, [analyticsQuery.data]);

  useMemo(() => {
    if (questionAnalyticsQuery.data) {
      setQuestionAnalytics(questionAnalyticsQuery.data);
    }
  }, [questionAnalyticsQuery.data]);

  useMemo(() => {
    if (trendingAnalyticsQuery.data) {
      setTrendingAnalytics(trendingAnalyticsQuery.data);
    }
  }, [trendingAnalyticsQuery.data]);

  const xlsxSettings = {
    fileName: 'engagements'
  }

  const downloadExcelHandler = () => {
    const data = service.getEngagementsXlsx();
    xlsx(data, xlsxSettings);
    adminStatsService?.trackEvent('VoterAnalytics', 'export_excel');
  }

  return (
    <PageLayout user={user} client={client} authService={authService} className={classes.VoterAnalytics}>
      {loading ? (
        <Loader customId='loader' />
      ) : (
        <div className={classes.dataContainerWrapper}>
          <div className={classes.dataContainer}>
            <div className={classes.filterContainer}>
              <div className={uiType !== "pdf" ? classes.engagementsTitle : ""}>
                {uiType === "pdf" && <h2 className={"w-full"}>{client.name} Report</h2>}
                {uiType === "pdf" && <h3 className={"text-2xl mb-5"}>Date: {filterStore.timeFilter?.name || 'Default - All Time'}</h3>}
                <h2 className={classes.engagementsTitle}>Engagements</h2>
              </div>
              {
                uiType !== "pdf" && <>
                  <Filter isSuperAdmin={user?.accessLevel === "super admin"} type="Default" />
                  <AnalyticsButton onClickPDF={exportPDFHandler} onClickExcel={downloadExcelHandler} csvData={engagementsCsv || ""} csvFilename="engagements.csv" />
                </>
              }
            </div>
            {uiType !== "pdf" && <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className={cn(classes.filterBarContainer, classes.hideOnMobile)}>
              <FilterBar />
            </motion.div>}
            <div className={sharedClasses.metricContainer}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.15 }}
                className={sharedClasses.statItemContainer}>
                <img
                  className={sharedClasses.statIcon}
                  loading="lazy"
                  alt=""
                  src="/analytics/svg-icons/hand.svg"
                />
                <div className={sharedClasses.metricLabelsContainer}>
                  <div className={sharedClasses.totalTraffic}>Total Engagements</div>
                  <b className={sharedClasses.metricDurations}>
                    {service.computedValues.totalEngagements != null
                      ? numberWithCommas(service.computedValues.totalEngagements)
                      : <motion.span animate={{ opacity: [0.3, 1, 0.3] }} transition={{ duration: 1.5, repeat: Infinity }}>-</motion.span>}
                  </b>
                </div>
              </motion.div>
              <img
                className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                alt=""
                src="/analytics/chart/vertical-divider.svg"
              />
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.15 }}
                className={sharedClasses.statItemContainer}>
                <img
                  className={sharedClasses.statIcon}
                  loading="lazy"
                  alt=""
                  src="/analytics/svg-icons/stats.svg"
                />
                <div className={sharedClasses.metricLabelsContainer}>
                  <div className={sharedClasses.totalTraffic}>Total Traffic</div>
                  <b className={sharedClasses.metricDurations}>
                    {service.computedValues.totalTraffic != null
                      ? numberWithCommas(service.computedValues.totalTraffic)
                      : <motion.span animate={{ opacity: [0.3, 1, 0.3] }} transition={{ duration: 1.5, repeat: Infinity }}>-</motion.span>}
                  </b>
                </div>
              </motion.div>
              <img
                className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                alt=""
                src="/analytics/chart/vertical-divider.svg"
              />
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className={sharedClasses.statItemContainer}>
                <img
                  className={sharedClasses.statIcon}
                  loading="lazy"
                  alt=""
                  src="/analytics/svg-icons/handshake.svg"
                />
                <div className={sharedClasses.metricLabelsContainer}>
                  <div className={sharedClasses.engagementRate}>Engagement Rate</div>
                  <b className={sharedClasses.metricValue}>
                    <span>
                      {service.computedValues.engagementRate != null
                        ? service.computedValues.engagementRate
                        : <motion.span animate={{ opacity: [0.3, 1, 0.3] }} transition={{ duration: 1.5, repeat: Infinity }}>-</motion.span>}
                    </span>
                    <span className={sharedClasses.metricUnit}>%</span>
                  </b>
                </div>
              </motion.div>
              <img
                className={cn(sharedClasses.metricDivider, sharedClasses.hideOnMobile)}
                alt=""
                src="/analytics/chart/vertical-divider.svg"
              />
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.45 }}
                className={sharedClasses.statItemContainer}>
                <img
                  className={sharedClasses.statIcon}
                  loading="lazy"
                  alt=""
                  src="/analytics/svg-icons/timer.svg"
                />
                <div className={sharedClasses.timeSavedContainer}>
                  <div className={sharedClasses.timeSaved}>Time Saved</div>
                  <b className={sharedClasses.metricValue}>
                    <span>
                      {service.computedValues.timeSaved != null
                        ? service.computedValues.timeSaved
                        : <motion.span animate={{ opacity: [0.3, 1, 0.3] }} transition={{ duration: 1.5, repeat: Infinity }}>-</motion.span>}
                    </span>
                    <span className={sharedClasses.metricUnit}>hrs</span>
                  </b>
                </div>
              </motion.div>
            </div>
            <Popover>
              <motion.div
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: 0.4 }}
                className={cn(classes.chartContainer, 'chartContainer')}>
                <div className={classes.chartHeaderContainer}>
                  <div className={classes.tabsContainer}>
                    <div
                      onClick={() => {
                        setSelectedTab('traffic');
                        adminStatsService?.trackEvent('VoterAnalytics', 'switch_engagements_chart_tab');
                      }}
                      className={cn(classes.tabItem, selectedTab === "traffic" && classes.selectedTab)}
                    >
                      Traffic
                    </div>
                    <div
                      onClick={() => {
                        setSelectedTab('queries');
                        adminStatsService?.trackEvent('VoterAnalytics', 'switch_engagements_chart_tab');
                      }}
                      className={cn(classes.tabItem, selectedTab === "queries" && classes.selectedTab)}
                    >
                      Queries
                    </div>
                  </div>
                  <PopoverTrigger asChild>
                    <img
                      className={cn(classes.moreInfoIcon, "cursor-pointer")}
                      loading="lazy"
                      alt=""
                      src="/analytics/icons/more-info-icon.png"
                    />
                  </PopoverTrigger>
                  <PopoverContent className='w-fit'>
                    <ExportButton mainContent={selectedTab === "traffic" ? chartRef : queriesChartRef} />
                  </PopoverContent>
                </div>
                <div className={classes.sentimentChartContainer}>
                  <div className={classes.sentimentChartContentParent}>
                    <div className={classes.sentimentChartContent}>
                      {
                        selectedTab === 'traffic' ?
                          <Chart type="engagements" chartData={chartData} chartRef={chartRef} /> :
                          <Chart color="#5bc776" type="queries" chartData={mergedQueriesChartData} chartRef={queriesChartRef} />
                      }
                    </div>
                    <div className={classes.deviceUsageContainer}>
                      <div className={classes.deviceUsageContentWrapper}>
                        <div className={classes.deviceUsageChart}>
                          <PieChartComponent
                            data={deviceUsageData.map((device: any) => (
                              {
                                name: device.device.charAt(0).toUpperCase() + device.device.slice(1),
                                value: Math.round(
                                  (
                                    parseInt(device.count) /
                                    deviceUsageData.reduce((sum: number, x: any) => sum + parseInt(x.count), 0)
                                  ) * 100
                                )
                              }))}
                            ref={pieChartRef} />
                        </div>
                        <div className={classes.deviceContainer}>
                          {(deviceUsageData || []).map((device: any, index: number) => (
                            <div className={classes.deviceItemContainer} key={index}>
                              <div className={classes.deviceContent}>
                                <div className={classes.deviceLabels}>
                                  <div className={classes.deviceIconWrapper}>
                                    <div className={classes.deviceIcon} style={{ backgroundColor: PieChartColors[index] }} />
                                  </div>
                                  <div className={classes.deviceName}>
                                    {device.device.charAt(0).toUpperCase() + device.device.slice(1)}
                                  </div>
                                </div>
                              </div>
                              <b className={classes.devicePercentage}>
                                {(
                                  (
                                    parseInt(device.count) /
                                    deviceUsageData.reduce((sum: number, x: any) => sum + parseInt(x.count), 0)
                                  ) * 100
                                ).toFixed(0)}
                                %
                              </b>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={classes.sourcesContainer}>
                    <div className={classes.sourcesTitle}>Sources</div>
                    <div className={classes.trafficSourcesContainer}>
                      {(squashedSources || []).sort((a: any, b: any) => b.percentage - a.percentage).map((source: any, index: number) => {
                        const sourceName = (!source.source || source.source === 'null' || source.source?.includes('upwork.com')) ? 'No Referrer' : source.source
                        const sourceIcon =
                          !source.source || source.source === 'null' || source.source?.includes('upwork.com') ? `/analytics/sources/search.svg` :
                            source.source === client.websiteURL ? (client.logoURL || defaultLogo) :
                              source.source.includes('repd') ? logo :
                                source.source.includes('google') ? `/analytics/sources/google.png` :
                                  source.source.includes('instagram') ? `/analytics/sources/instagram.svg` :
                                    source.source.includes('facebook') ? `/analytics/sources/facebook.svg` :
                                      source.source.includes('twitter') ? `/analytics/sources/x.svg` :
                                        source.source.includes('nextdoor') ? `/analytics/sources/nextdoor.svg` :
                                          source.source.includes('x.com') ? `/analytics/sources/x.svg` :
                                            source.source.includes('kvoe') ? `/analytics/sources/kvoe.png` :
                                              source.source.includes('emporiagazette') ? `/analytics/sources/emporia-gazette.png` :
                                                source.source.includes('linkedin') ? `/analytics/sources/linkedin.svg` :
                                                  source.source.includes('tx-denton') ? `/analytics/sources/tx-denton.png` :
                                                    `/analytics/sources/search.svg`
                        const displaySubtitle = uiType === "pdf" || sourceName === 'No Referrer' || sourceIcon === `/analytics/sources/search.svg`;

                        if (index > 4)
                          return <></>;
                        else
                          return (
                            <div className={classes.trafficSourceItem} key={index}>
                              <div className={classes.trafficSourceContent}>
                                <img
                                  className={cn(classes.trafficSourceIcon, source.source === client.websiteURL && classes.clientLogo)}
                                  loading="lazy"
                                  alt={source.source}
                                  title={sourceName}
                                  src={sourceIcon}
                                />
                                <img
                                  className={classes.sourceDivider}
                                  loading="lazy"
                                  alt=""
                                  src="/analytics/chart/divider-dashed.svg"
                                />
                                <b className={classes.sourcePercentage}>{Math.round(Number(source.percentage))}%</b>
                              </div>
                              {displaySubtitle && <b className={classes.sourceLink}>{(source.source || '').replace(/http(s|):|\/|www./g, '')}</b>}
                            </div>
                          );
                      })}
                    </div>
                  </div>
                </div>
              </motion.div>
            </Popover>

            <Queries
              type="Default"
              onAnswer={onAnswer}
              totalQueries={service.computedValues.totalQueries}
              categoryData={service.computedValues.topCategories}
              user={user}
              trendingStats={trendingData}
              className={uiType === "pdf" ? "mb-10" : ""} />

            {uiType !== "pdf" && <QueryTrends trendingStats={trendingData} />}
          </div>
        </div>
      )}
      {isVideoUploadModalOpen && (
        <VideoUploadModal
          onClose={() => {
            setIsVideoUploadModalOpen(false);
            setSelectedQuestionId(null);
            setSelectedQuestion(null);
          }}
          question={selectedQuestion || { id: selectedQuestionId } as interfaces.QuestionInterface}
          clientService={clientService}
        />
      )}
      {/* Temporarily disabled EditQuestionModal
      {isEditQuestionModalOpen && selectedQuestion && (
        <EditQuestionModal
          isOpen={isEditQuestionModalOpen}
          question={selectedQuestion}
          questionService={questionService}
          onClose={handleEditQuestionClose}
          onSave={handleEditQuestionSave}
          onProceedWithAnswer={handleProceedWithAnswer}
        />
      )}
      */}
    </PageLayout>
  )
}
