.metricContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: var(--gap-31xl);
    max-width: 100%;
    text-align: center;
}

.statItemContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: var(--gap-xl);
    min-width: 200px;
}

.statIcon {
    height: 48px;
    width: 48px;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.metricLabelsContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: start;
}

.totalTraffic {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: start;
}

.metricDurations {
    position: relative;
    font-size: var(--font-size-35xl);
    color: var(--color-black);
}

.metricDivider {
    height: 90px;
    width: 1px;
    position: relative;
}

.engagementRate {
    align-self: stretch;
    position: relative;
}

.metricValue {
    position: relative;
    color: var(--color-black);
    font-size: var(--font-size-35xl);
}

.metricUnit {
    font-size: var(--font-size-15xl);
}

.timeSaved {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeSavedContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}

.hideOnMobile {
    display: block;
}

.watchRateLabelContainer {
    display: flex;
    align-items: center;
    gap: 5px;
}

.infoIcon {
    width: 16px;
    height: 16px;
    position: relative;
}

/* Media queries for responsive design */
@media screen and (max-width: 975px) {
    .metricDurations {
        font-size: var(--font-size-24xl);
    }

    .metricValue {
        font-size: var(--font-size-8xl);
    }

    .metricContainer {
        flex-wrap: wrap;
    }
}

@media screen and (max-width: 700px) {
    .metricContainer {
        gap: 25px;
    }
}

@media screen and (max-width: 640px) {
    .hideOnMobile {
        display: none;
    }

    .metricContainer {
        gap: 20px;
        padding-bottom: 20px;
        width: 100%;
        flex-direction: column;
    }

    .statItemContainer {
        flex: 1;
        width: 100%;
        gap: 10px;
    }

    .totalTraffic {
        font-size: 16px;
    }

    .engagementRate {
        display: flex;
        align-items: center;
        font-size: var(--font-size-base);
    }

    .metricLabelsContainer {
        display: flex;
        flex-direction: row;
        flex: 1;
        justify-content: space-between;
        align-items: center;
        font-size: 24px;

        span {
            font-size: 24px;
        }

        b {
            font-size: 24px;
        }
    }

    .metricUnit {
        font-size: 24px;
    }

    .metricValue {
        font-size: var(--font-size-xl);

        span {
            font-size: 24px;
        }
    }

    .timeSavedContainer {
        display: flex;
        flex-direction: row;
        flex: 1;
        justify-content: space-between;
        align-items: center;
    }
}

@media screen and (max-width: 450px) {
    .metricDurations {
        font-size: var(--font-size-13xl);
    }

    .metricValue {
        font-size: var(--font-size-xl);
    }
}