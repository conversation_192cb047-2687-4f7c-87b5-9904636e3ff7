@import 'src/styles/variables';

.content {
  flex: 1;
  padding-top: 60px;
  font-size: 40px;
}

.wrapper {
  padding: 0px 40px;
}

.mainContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-xl);
  max-width: 100%;
}

.contentWrapper {
  height: 944px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-11xl) 0px 0px;
  box-sizing: border-box;
  width: 100%;
  text-align: left;
  font-size: var(--font-size-xl);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

.pageTitle {
  margin: 0;
  flex: 1;
  position: relative;
  font-size: inherit;
  font-weight: 500;
  font-family: inherit;
  display: inline-block;
  min-width: 90px;
  max-width: 100%;
}

.filterHeader {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-xl);
  max-width: 100%;
  font-size: var(--font-size-15xl);
}

.filterBarContainer {
  width: 100%;
}

.filterContainer {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 20px;
  max-width: 100%;
  flex-shrink: 0;
}

.metricDivider {
  height: 90px;
  width: 1px;
  position: relative;
}

.trophyIcon {
  height: 48px;
  width: 48px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.completedWatchRate {
  flex: 1;
  position: relative;
  text-align: left;
}

.infoIcon {
  height: 20px;
  width: 20px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.watchRateLabelContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-9xs);
}

.percentageUnit {
  font-size: var(--font-size-15xl);
}

.percentageValue {
  position: relative;
  display: flex;
  align-items: center;
  color: var(--color-black);
  font-size: var(--font-size-35xl);
}

.watchRateContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.watchRateCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-xl);
}

.metricCards {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px var(--padding-xl) 0px 0px;
  box-sizing: border-box;
  gap: var(--gap-31xl);
  max-width: 100%;
  text-align: center;
}

.chartTitle {
  position: relative;
  font-weight: 500;
  display: inline-block;
  font-size: 20px;
  padding-bottom: 20px;
}

.chartTitleContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xs-5) 0px 0px;
}

.moreInfoIcon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
  cursor: pointer;
}

.chartHeaderContainer {
  flex: 1;
  width: 100%;
  margin: 0 !important;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-xl);
}

.chartContainer {
  align-self: stretch;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 25px;
  position: relative;
  max-width: 100%;
  text-align: center;
  font-size: var(--font-size-base);
}

.sectionTitle {
  position: relative;
  font-weight: 500;
  display: inline-block;
  min-width: 69px;
}

.videoListHeaderContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-xl);
}

.videoThumbnail {
  align-self: stretch;
  height: 180px;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  flex-shrink: 0;
  object-fit: cover;
  border-radius: 5px;
  border: 0px solid white;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
    border-width: 2px;
    cursor: pointer;
  }
}

.videoTitle {
  position: relative;
  font-weight: 500;
  display: -webkit-inline-box;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.viewsText {
  margin: 0;
  white-space: pre-wrap;
}

.videoMeta {
  align-self: stretch;
  position: relative;
  font-size: var(--font-size-sm);
}

.videoCard {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-3xs);
  min-width: 230px;
  max-width: 250px;
  cursor: pointer;
}

.videoGrid {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: var(--gap-base);
  font-size: var(--font-size-base);
}

.videoListContainer {
  align-self: stretch;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 20px;
  gap: 20px;
}

.categoryBadge {
  align-self: flex-end;
  padding: 0.25em 0.5em;
  font-size: 0.75em;
  font-weight: 600;
  color: var(--color-whitesmoke);
  background-color: var(--color-slategray-100);
  border-radius: 0.25rem;
  margin-right: 0.24rem;
}

.accentBadge {
  background-color: var(--accent-color, $blue);
}

@media screen and (max-width: 975px) {
  .pageTitle {
    font-size: var(--font-size-8xl);
  }

  .filterHeader {
    flex-wrap: wrap;
  }

  .percentageValue {
    font-size: var(--font-size-8xl);
  }

  .metricCards {
    flex-wrap: wrap;
  }

  .videoGrid {
    justify-content: center;
    grid-template-columns: repeat(2, minmax(187px, 325px));
  }

  .contentWrapper {
    height: auto;
    max-width: 100%;
  }

  .mainContainer {
    padding-left: var(--padding-xl);
    padding-right: var(--padding-xl);
    box-sizing: border-box;
  }
}

@media screen and (max-width: 640px) {
  .hideOnMobile {
    display: none;
  }

  .metricContainer {
    gap: 20px;
    padding-bottom: 20px;
  }

  .statItemContainer {
    flex: 1;
    gap: 10px;
  }

  .engagementRate {
    display: flex;
    align-items: center;
  }

  .totalTraffic {
    font-size: 16px;
  }

  .metricLabelsContainer {
    font-size: 24px;

    span {
      font-size: 24px;
    }

    b {
      font-size: 24px;
    }
  }

  .metricUnit {
    font-size: 24px;
  }

  .metricValue {
    span {
      font-size: 24px;
    }
  }

  .timeSavedContainer {
    display: flex;
    flex-direction: row;
    flex: 1;
    justify-content: space-between;
    align-items: center;
  }

  .metricLabelsContainer {
    display: flex;
    flex-direction: row;
    flex: 1;
    justify-content: space-between;
    align-items: center;
  }

  .metricCards {
    gap: var(--gap-6xl);
  }

  .wrapper {
    padding: 0px 20px;
  }

  .mainContainer {
    padding: 0;
  }

  .chartTitle {
    font-size: 20px;
    color: #445472;
  }

  .chartContainer {
    padding: 20px;
  }

  .videoCard {
    max-width: none;
  }
}

@media screen and (max-width: 450px) {
  .pageTitle {
    font-size: var(--font-size-xl);
  }

  .completedWatchRate {
    font-size: var(--font-size-base);
  }

  .percentageValue {
    font-size: var(--font-size-xl);
  }

  .sectionTitle {
    font-size: var(--font-size-base);
  }

  .videoListHeaderContainer {
    flex-wrap: wrap;
  }

  .videoGrid {
    grid-template-columns: minmax(187px, 1fr);
  }

  .videoListContainer {
    padding-top: var(--padding-xl);
    box-sizing: border-box;
  }
}