import { useState, useEffect } from 'react';
import { Field, Form, FormikProvider, useFormik, useFormikContext } from "formik";
import * as Yup from "yup";
import { ToastContainer, toast } from 'react-toastify';
import cn from 'classnames';

import { AuthService, ClientService, UserService } from 'services';
import { UserInterface, SessionInterface, ClientInterface } from 'interfaces';

import Button from 'shared/Button';
import logo from 'assets/logo/Logo-Full.png';

import FormClasses from 'shared/Forms/Forms.module.scss';
import pageClasses from 'styles/PageWrapper.module.scss';
import classes from './Super.module.scss';
import PageLayout from 'shared/PageLayout';

function CreateNewClient(props: {
  clientService?: ClientService,
  authService: AuthService,
  userService?: UserService,
  authUser?: UserInterface
 }) {
  const { clientService, authService, authUser } = props;

  const [client, setSelectedClient] = useState<ClientInterface | null>(null);
  const [records, setRecords] = useState<SessionInterface[]>([]);

  const fetchClients = () => {
    clientService?.list((s: SessionInterface[]) => {
      setRecords(s);
    })
  }

  useEffect(fetchClients, [clientService]);

  const [save, setSave] = useState(false);

  const errorMessages = {
    firstName: 'Please enter a first name',
    lastName: 'Please enter a last name',
    email: 'Please enter a valid email address',
    clientName: 'Please enter a Candidate Name'
  }

  const profileForm = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      clientName: '',
      clientType: 'Campaign',
    },
    validationSchema: Yup.object().shape({
      firstName: Yup.string().required(errorMessages.firstName),
      lastName: Yup.string().required(errorMessages.lastName),
      email: Yup.string().required(errorMessages.email).email(errorMessages.email),
      clientName: Yup.string().required(errorMessages.clientName)
    }),
    onSubmit: (values, actions) => { }
  });

  const profileFocus = profileForm.touched,
    profileErrors = profileForm.errors

  const errors = {
    email: profileErrors.email && profileFocus.email && FormClasses.hasError,
    firstName: profileErrors.firstName && profileFocus.firstName && FormClasses.hasError,
    lastName: profileErrors.lastName && profileFocus.lastName && FormClasses.hasError,
    clientName: profileErrors.clientName && profileFocus.clientName && FormClasses.hasError
  }

  function SubmitOutsideForm() {
    var values: any = useFormikContext().values

    useEffect(() => {
      if (!save) return

      var invalid: boolean = false
      var error: string | null = null

      invalid = (!values.email ||
        !values.firstName?.match(/^[a-z0-9 ]+/i) ||
        !values.lastName?.match(/^[a-z0-9 ]+/i) ||
        !values.clientName?.match(/^[a-z0-9 ]+/i) ||
        !values.email.match(/[^@]+@[^.]+\.[a-z]+/)
      )
      error = invalid ? 'Please make sure the required fields are present.' : null

      if (!values.email || !values.email.match(/[^@]+@[^.]+\.[a-z]+/)) error = errorMessages.email
      if (!values.firstName) error = errorMessages.firstName
      if (!values.lastName) error = errorMessages.lastName
      if (!values.clientName) error = errorMessages.clientName

      if (!invalid)
        clientService?.create(values, (u: UserInterface) => {
          if (!u.firstName) 
            return toast.error('We could not create your candidate or user - please make sure the entered information is unique.')

          toast.success(`The client ${u.firstName} and candidate ${values.clientName} are created`)
          profileForm.resetForm()
        })

      if (error) toast.error(error)

      setSave(false);
    }, [values, save]);

    return null;
  }

  return <PageLayout user={authUser} client={client} authService={authService} className={classes.Super}>
    <div>
      <div className={cn(pageClasses.Wrapper, classes.wrapper, classes.mb0)}>
        <img className={classes.logo} src={logo} alt="REPD logo" />
          <div className={FormClasses.Form}>
            <h2 className={FormClasses.title}>{profileForm.values.clientType === 'Campaign' ? 'Create a New Admin & Candidate' : 'Create a New Admin'}</h2>

            <FormikProvider key="updateFormProvider" value={profileForm}>
              <SubmitOutsideForm key="updateFormButtonHandler" />

              <Form key="updateForm" className={FormClasses.Form}>
                <div className={FormClasses.radioInputBlock}>
                  <label>
                    <Field type="radio" name="clientType" value="Campaign" />
                    Campaign
                  </label>

                  <label>
                    <Field type="radio" name="clientType" value="Government" />
                    Government
                  </label>
                </div>

                <div className={FormClasses.SideBySide}>
                  <div className={FormClasses.InputWithLabel}>
                    <label>Admin First Name</label>
                    <Field type="text" name="firstName" className={errors.firstName} />
                    {errors.firstName && <div className={FormClasses.errorText}>{errorMessages.firstName}</div>}
                  </div>

                  <div className={FormClasses.InputWithLabel}>
                    <label>Admin Last Name</label>
                    <Field type="text" name="lastName" className={errors.lastName} />
                    {errors.lastName && <div className={FormClasses.errorText}>{errorMessages.lastName}</div>}
                  </div>
                </div>

                <div className={FormClasses.InputWithLabel}>
                  <label>Admin Email</label>
                  <Field type="email" name="email" className={errors.email} placeholder="Enter your email" />
                  {errors.email && <div className={FormClasses.errorText}>{errorMessages.email}</div>}
                </div>

                <div className={FormClasses.InputWithLabel}>
                  <label>{profileForm.values.clientType === 'Campaign' ? 'Candidate Name' : 'City Name'}</label>
                  <Field type="text" name="clientName" className={errors.clientName} />
                  {errors.clientName && <div className={FormClasses.errorText}>{errorMessages.clientName}</div>}
                </div>
              </Form>
            </FormikProvider>

            <div className={classes.ButtonGoup}>
              <Button text="Create" customClass="primary" callback={() => setSave(!save)} />
            </div>
          </div>
      </div>
      <ToastContainer position="top-right" />
    </div>
  </PageLayout>;
}

export default CreateNewClient;
