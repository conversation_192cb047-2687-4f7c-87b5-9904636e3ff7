import React from "react";
import { useNavigate } from "react-router-dom";
import classes from "./NotFound.module.scss"; // Import the styles

const NotFound = () => {
  const navigate = useNavigate();

  const goHome = () => {
    navigate("/");
  };

  return (
    <div className={classes.container}>
      <h1 className={classes.title}>404 - Page Not Found</h1>
      <p className={classes.message}>The page you are looking for does not exist.</p>
      <button className={classes.button} onClick={goHome}>Go Home</button>
    </div>
  );
};

export default NotFound;
