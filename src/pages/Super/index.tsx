import { useState, useEffect } from 'react';
import { ToastContainer } from 'react-toastify';
import cn from 'classnames';

import { AuthService, ClientService, UserService } from 'services';
import { UserInterface, SessionInterface, ClientInterface } from 'interfaces';

import ClientTable from '../../shared/ClientTable';
import UserTable from '../../shared/UserTable';
import Button from 'shared/Button';
import logo from 'assets/logo/Logo-Full.png';

import pageClasses from 'styles/PageWrapper.module.scss';
import classes from './Super.module.scss';
import PageLayout from 'shared/PageLayout';

export default function CreateNewClient(props: {
  clientService?: ClientService,
  authService: AuthService,
  userService?: UserService,
  authUser?: UserInterface
 }) {
  const { clientService, authService, userService, authUser } = props;

  const [client, setSelectedClient] = useState<ClientInterface | null>(null);
  const [records, setRecords] = useState<SessionInterface[]>([]);

  const fetchClients = () => {
    clientService?.list((s: SessionInterface[]) => {
      setRecords(s);
    })
  }

  useEffect(fetchClients, [clientService]);

  return <PageLayout user={authUser} client={client} authService={authService} className={classes.Super}>
    <div>
      <div className={cn(pageClasses.Wrapper, classes.wrapper, classes.mb0)}>
        <img className={classes.logo} src={logo} alt="REPD logo" />
      </div>
      <ToastContainer position="top-right" />
    
      <ClientTable fetchClients={fetchClients} clientService={clientService} clientSessionRecords={records} selectRecord={setSelectedClient} selectedClient={client} />
      <UserTable fetchClients={fetchClients} userService={userService} clientSessionRecords={records} selectedClient={client} authUser={authUser} />

    </div>
  </PageLayout>;
}
