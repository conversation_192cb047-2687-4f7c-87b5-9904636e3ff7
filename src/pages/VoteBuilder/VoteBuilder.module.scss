@import 'src/styles/variables';

.VoteBuilder {

  .page {
    max-width: 840px;
  }

  .title {
    position: relative;
    display: flex;
    color: $blue;
    font-size: 18px;
    font-weight: 300;
    margin: 40px 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid $blue;

    .categoriesTitle {
      margin-left: 20px;
      width: 210px;
    }

    button {
      position: absolute;
      right: 10px;
      bottom: 10px;
    }
  }

 .subTitle {
    font-size: 16px;
    font-weight: 300;
    margin: 30px 0 15px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid $offwhite;
  }

  .ngpInformation {
    a {
      color: $blue;
    }
  }

  .warningMessage {
    background: $pale-blue;
    padding: 20px;
    margin-bottom: 10px;
    border: 1px solid blue;
    border-radius: 10px;

    a {
      color: $blue;
    }
  }

  .matchActivistCodes {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    padding: 10px 10px 20px;
    max-height: 600px;
    overflow-y: auto;
    background: white;
    border: 1px solid $offwhite;
    border-radius: 10px;
    font-size: 16px;

    .match {
      display: flex;
      flex-direction: row;
      align-items: center;

      .categoryWrapper {
        min-width: 200px;
        width: 200px;
        border-right: 1px solid $offwhite;
        padding: 5px;
        align-self: stretch;

        .category {
          margin: 5px 10px 5px 0px;
          padding: 5px 15px 5px 15px;
          overflow-wrap: anywhere;
          background: $pale-blue;
          border-radius: 30px;
          color: $blue;
          width: fit-content;
          text-align: center;

        }
      }

      .activistCodesWrapper {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 5px;

        .activistCode {
          margin: 5px 0px 5px 10px;
          padding: 5px 15px 5px 15px;
          border: 1px solid $blue;
          border-radius: 30px;
          color: $blue;
        }
      }
    }
  }

  .unmatched {
    flex-direction: row;
    flex-wrap: wrap;

    .category {
      margin: 5px 10px 5px 0px;
      padding: 5px 15px 5px 15px;
      background: $pale-blue;
      border-radius: 40px;
      color: $blue;
    }
  }
}

@media (max-width : 640px) {

  .VoteBuilder {

    .title {
      .categoriesTitle {
        display: none;
      }
    }

    .matchActivistCodes {
      .match {
        flex-direction: column;
        align-items: flex-start;
        border-bottom: 1px solid $offwhite;

        &:last-child {
          border-bottom: none;
        }

        .categoryWrapper {
          border-right: none;
        }
      }
    }
  }

}