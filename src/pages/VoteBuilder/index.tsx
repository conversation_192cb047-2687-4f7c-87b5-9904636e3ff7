import { useEffect, useState} from 'react';
import { ToastContainer } from 'react-toastify';

import { ClientInterface } from 'interfaces/client.interfaces';
import { ActivistCodesInterface, ActivistCodeInterface } from 'interfaces/ngpvan.interface';
import { ClientService, NgpVanService } from 'services/index';
import { useNotification } from "hooks";

import MatchActivistCodes from 'popups/MatchActivistCodes';
import Button from 'shared/Button';

import pageClasses from 'styles/PageWrapper.module.scss';
import FormClasses from 'shared/Forms/Forms.module.scss';
import classes from './VoteBuilder.module.scss';

const DEFAULT_ACTIVIST_CODES = ["Repd User", "Repd Click Volunteer", "Repd Click Donate"];
const WARNING_CODES = {
    missingDefaultCodes: "001-Missing-Default-Activist-Codes",
    codesDoNotMatch: "002-Activist-Codes-Do-Not-Match",
};

export default function VoteBuilder(props: { setClient: Function, clientService: ClientService, ngpVanService: NgpVanService }) {
  const { setClient, clientService, ngpVanService } = props;

  const [activistCodes, setActivistCodes] = useState<ActivistCodeInterface[]>([]);
  const [missingDefaultCodes, setMissingDefaultCodes] = useState<string[]>([]);
  const [matchActivistCodesOpen, setMatchActivistCodesOpen] = useState(false);
  const [warningCodes, setWarningCodes] = useState<string[]>([]);

  const { showAlert } = useNotification();

  let client: ClientInterface | any = clientService.client;

  const categories: string[] = (client.categories || '').replace(/\s+/g, ' ')
    .replace(/, /g, ',')
    .replace(/ ,/g, ',')
    .replace(/^ /, '')
    .replace(/ $/, '').split(',');


  function onChangeField(event: any) {
    client[event.target.name] = event.target.value;
    setClient(client);
  }

  function saveNgpInformation() {
    clientService.update(client).then(showAlert("Information updated."));
  }

  useEffect(() => {
    if (!matchActivistCodesOpen)
      ngpVanService.getActivistCodes((response: ActivistCodesInterface) => {
        setMissingDefaultCodes(DEFAULT_ACTIVIST_CODES.filter(code => !response.voterActivistCodes.find(c => c.name === code)));
        setActivistCodes(response.voterActivistCodes.filter(code => !DEFAULT_ACTIVIST_CODES.includes(code.name)));
        setWarningCodes(response.warningCodes);
      });
  }, [ngpVanService, matchActivistCodesOpen]);

  return (
    <div className={classes.VoteBuilder}>
      <div className={`${pageClasses.Page} ${classes.page}`}>
        <h1 className={pageClasses.title}>
          Votebuilder
          <Button text="Save" callback={saveNgpInformation} />
        </h1>

        {missingDefaultCodes.length > 0 && <div className={classes.warningMessage}>
          <p>
            Segmentation data will only be pushed to users in votebuilder as tags.
            To push segmentation data as activist codes on users please create activist codes with these names in your votebuilder:
            {missingDefaultCodes.map((code) =>
              <li key={code}>
                {code}
              </li>)}
            <a href="https://help.ngpvan.com/" target="_blank" rel="noreferrer">Click here </a>
            to add these activist codes in Votebuilder.
          </p>
        </div>}

        {warningCodes.includes(WARNING_CODES.codesDoNotMatch) && <div className={classes.warningMessage}>
          <p>
            Warning: Your activist codes are mismatched.
          </p>
          <p>
            To ensure data can be pushed as activist codes please make sure the list is the same in both My Campaign and My Voters.
          </p>
        </div>}

        <form className={FormClasses.Form} method="post" action="">

          <div className={FormClasses.SideBySide}>
            <div className={FormClasses.InputWithLabel}>
              <label>Username</label>
              <input type="text" name="ngpVanUsername" placeholder="••••" defaultValue={client.ngpVanUsername} onChange={onChangeField} />
            </div>
            <div className={FormClasses.InputWithLabel}>
              <label>API Key</label>
              <input type="text" name="ngpVanApiKey" placeholder="••••-••••-••••" defaultValue={client.ngpVanApiKey} onChange={onChangeField} />
            </div>
          </div>

          <div className={classes.ngpInformation}>
            To request an API key with the correct access enabled check out our instructions
            <a href="https://help.ngpvan.com/van/s/article/2969508-requesting-and-approving-api-keys" target="_blank" rel="noreferrer"> here</a>
            .
          </div>

        </form>

        <h5 className={classes.title}>
          <div className={classes.categoriesTitle}>
            Categories
          </div>
          Activist Codes
          <Button text="Update" callback={() => setMatchActivistCodesOpen(true)} />
        </h5>

        {activistCodes.length ?
          <div className={classes.matchActivistCodes}>
            {categories.filter(c => activistCodes.find(a => a.categories.includes(c))).map((category) => {
              return <div key={category} className={classes.match}>
                <div className={classes.categoryWrapper}>
                  <div className={classes.category}>
                    {category}
                  </div>
                </div>
                <div className={classes.activistCodesWrapper}>
                  {activistCodes.filter(c => c.categories.includes(category)).map((activistCode) => {
                    return <div key={activistCode.activistCodeId} className={classes.activistCode}>
                      {activistCode.name}
                    </div>
                  })}
                </div>
              </div>
            })}
          </div> :
          <div className={classes.matchActivistCodes}>
            <p>No Activist Codes</p>
            <p>Add your Votebuilder API key to pull codes.</p>
          </div>}

        <h6 className={classes.subTitle}>Un-Matched Categories</h6>

        <div className={classes.matchActivistCodes + ' ' + classes.unmatched}>
          {categories.filter(c => !activistCodes.find(a => a.categories.includes(c))).map((category) => {
            return <div key={category} className={classes.category}>
              {category}
            </div>
          })}
        </div>

      </div>

      {matchActivistCodesOpen &&
        <MatchActivistCodes
          handleClose={() => setMatchActivistCodesOpen(false)}
          clientId={client.id}
          categories={categories}
          activistCodes={activistCodes}
          ngpVanService={ngpVanService}
        />}

      <ToastContainer position="top-center" />
    </div>
  )
}
