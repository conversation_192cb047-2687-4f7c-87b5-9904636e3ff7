@import './variables';

.<PERSON> {
    margin: auto;
    max-width: 800px;
    padding: 0 20px 20px 20px;

    &.wide {
        max-width: 1040px;
    }

    .Wrapper {
        width: 100%;
    }

    .title {
        margin: 25px 0 20px 0;
        padding-bottom: 20px;
        border-bottom: 1px solid $grey;
        color: $dark-yellow;
        font-size: 26px;
        font-weight: 100;

        button {
            float: right;
            margin-left: 10px;

            &:last-child {
                margin-left: 0;
            }
        }
    }

    .subTitle {
        width: 100%;
        color: #567deb;
        font-size: 18px;
        font-weight: 300;
        margin: 30px 0 15px 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center
    }

    .TitleWithTabs {
        margin: 25px 0 20px 0;
        padding-bottom: 20px;
        border-bottom: 1px solid $grey;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .Tabs {
            .Tab {
                margin-right: 20px;
                color: $blue;
                font-size: 26px;
                font-weight: 100;

                &:hover,
                &.active {
                    color: $dark-yellow;
                    font-weight: 300;
                }
            }
        }

        .Buttons {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .<PERSON><PERSON>,
            button {
                margin-left: 10px;
                margin-bottom: 5px;
            }
        }


        .Wrapper {
            width: 100%;
        }

        .limitHeight {
            max-height: calc(100vh - 205px);
            overflow-y: scroll;
        }
    }
}

@media (max-width : 640px) {
    .Page {
        padding: 0 20px 20px;

        .TitleWithTabs {
            .Buttons {
                flex-direction: column;
                min-width: 195px;
            }
        }
    }
}