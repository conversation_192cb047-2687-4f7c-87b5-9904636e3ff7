.page-segment.press
  min-height 200px
  padding-bottom 0px
  background blue-gradient-3

  h1.title.text-center
    margin 40px 0 20px
    font-weight 600
    font-size 45px
    font-weight 200
    color white

  .press-carousel
    position relative
    width 100%
    max-width 1300px
    overflow hidden
    margin 0 auto 30px auto

  .inner-press-wrapper
    display flex
    transition transform 0.5s ease

  .press-article-carousel-item
    display flex
    min-width calc(100% - 40px)
    box-sizing border-box
    // margin-right -20px

  .carousel-control
    position absolute
    top 50%
    transform translateY(-50%)
    background-color rgba(0, 0, 0, 0.5)
    color white
    border none
    padding 10px
    cursor pointer
    z-index 1

    &.prev
      left 10px

    &.next
      right 10px

  .press-dots
    text-align center
    margin-top 10px

    .press-dot
      display inline-block
      width 10px
      height 10px
      background-color #bbb
      border-radius 50%
      margin 0 5px
      cursor pointer

      &.active
        background-color #717171

  .press-article
    box-sizing border-box
    position relative
    display flex
    flex-direction column
    align-items stretch
    width calc(33% - 40px)
    margin 20px
    padding 20px
    border 4px solid #ffffff4f
    border-radius 10px
    color white
    background #ffffff24

    img.press-article
      height 100%
      box-sizing border-box
      width calc(100% - 0px)
      margin 0
      border-radius 5px

    .press-article-logo
      position absolute
      right 5px
      top 5px
      width 100px
      height 100px
      object-fit scale-down
      background white
      border-radius 100px
      border 2px solid white
      box-shadow 0px 5px 5px 0 #00000021

      &.horizontal
        min-height 30px
        max-height 50px
        border-radius 10px
        width auto
        max-width 40%

      &.vertical
        width auto
        border-radius 10px

      &.square
        border-radius 10px
        object-fit cover

    .press-article-text
      height 100%
      display flex
      align-items center
      font-size 1.3em
      line-height 1.5em

    .press-article-link
      background #00a4ff52
      display inline-block
      width 90px
      text-align center
      padding 10px 20px
      border-radius 20px
      box-shadow 0 0 0 1px #ffffff78 inset, 0 2px 5px 0 #00000021
      color white
      font-weight 400
      transition all 200ms

      &:hover
        width 110px
        background #00a4ff78
        box-shadow 0 0 5px 1px #ffffff78 inset, 0 2px 5px 0 #00000021

  @media all and ( max-width 768px )
    .press-article-carousel-item
      flex-direction column

    .press-article
      width calc(100% - 40px)
