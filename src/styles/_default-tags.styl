body
  margin 85px 0 0 0
  min-width 650px
  font-family "Plus Jakarta Sans", sans-serif
  font-size 16px
  font-weight 200
  color text-blue
  background-color dark-blue
  // transform scale(1)

  @media all and ( max-width 768px )
    margin 185px 0 0 0
    min-width auto

  // @media all and ( max-width 500px )
  //   // Scale down the whole website
  //   // zoom 0.8
  //   transform-origin top left /* Sets the origin of transformation */
  //   transform scale(0.8) /* Initial scale is 1, meaning no zoom */
  //   transition transform 0.5s ease /* Optional: Smooth transition for zoom effect */

*
  animation-duration .25s
  animation-delay .5s
  animation-direction normal
  animation-iteration-count 1
  animation-fill-mode forwards
  transition height .5s, margin .5s, padding .5s, border-width .5s, filter .5s, opacity .5s,
             color .25s, background .25s, background-color .25s, border-color .25s

// Placeholder color grey
::-webkit-input-placeholder
  color grey

a
  color dark-blue
  text-decoration none
  transition color .25s
  cursor pointer

  &:hover,
  &.active
    color light-blue
ul
  margin 0
  padding 0

  li
    list-style-type none
    display flex
    align-items center
    margin 20px 0

    .text
       margin-left 40px

button
  appearance none
  border none
  background-color pink
  color white
  padding 15px 20px
  border-radius 5px
  font-size 18px
  cursor pointer

  &:hover
    background-color blue
    cursor pointer

form
  display flex
  flex-direction column
  font-size 18px

  .input-with-label
    display flex
    flex-direction column
    margin-top 10px

    &:first-child
      margin-bottom 0

  label
    display block
    color dark-grey
    font-weight 300

    .counter
      float right

  textarea
    appearance none
    margin-top 10px
    padding 10px
    min-height 80px
    border-radius 5px
    border none
    line-height 20px
    color dark-grey

    &.invalid
      border-color error-red

    &:focus
      outline light-blue 2px solid !important

  input
    appearance none
    margin-top 10px
    padding 10px
    border-radius 5px
    border none
    color dark-grey

    &.invalid
      border-color error-red

    &:focus
      outline light-blue 2px solid !important

  input, textarea
    font-size 20px

  .error
    color error-red

header
  position fixed
  top 0
  z-index 1000
  width 100%
  background-color dark-blue
  // box-shadow 0 10px 10px 0 rgba( 0, 0, 0, 0.1 )

  .wrapper
    display flex
    align-items center
    align-content space-between
    justify-content space-between
    margin auto
    padding 20px
    font-size 14px
    text-align right

    .link-group
      margin-left auto

    .menu
      position absolute
      right 30px
      width 50px
      height 50px
      background-repeat no-repeat
      background-position center
      cursor pointer

    a
      font-weight 400
      color white
      text-transform uppercase
      cursor pointer

      &:hover
        color pink

    button
      margin-left 20px
      background-color pink
      font-size 14px
      text-transform uppercase

      &:hover
        background-color pink
        color white

  @media all and ( max-width 768px )
    .wrapper
      flex-direction column
      align-items flex-start
      padding 40px
      padding-top 30px

      .link-group
        position absolute
        top 140px
        right 30px
        margin-left auto
        padding 10px
        border-radius 10px
        background white
        display none

        &.active
          display block

        a
          padding 10px 40px 10px 10px
          text-align left
          color black

          &:hover
            color pink

      button
        margin-top 20px
        margin-left 0
        width 100%

article
  &.right
    margin-left 60px

footer
  display flex
  padding 30px
  align-items center
  justify-content space-between
  background-color dark-blue
  color white

  .link-group
    line-height 0px

  a
    color white
    font-weight 100
    font-size 14px

    &:hover
      color rgba( 255, 255, 255, 0.5 )

  @media all and ( max-width 640px )
    &
      padding 30px 0
      flex-direction column

    .link-group
      display flex
      flex-direction row
      padding-top 30px

      a
        padding 0 10px
        font-size 12px

    .copy
      font-weight bold
      padding-top 30px
