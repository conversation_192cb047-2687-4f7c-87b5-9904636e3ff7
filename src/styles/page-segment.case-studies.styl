.page-segment.case-studies
  position relative
  padding 0 0 200px 0
  background blue-gradient-2

  aside.decoration
    position absolute
    z-index 0
    bottom 0
    width 500px
    height 300px
    background-size cover
    background-position center
    background-repeat no-repeat

    &.left
      left -120px

    &.right
      right -120px

  .arrow-container
    position relative
    width 100%

    img.arrow
      position absolute
      top 20vw
      width 40px
      cursor pointer
      z-index 1

      &:hover
        opacity 0.8
        filter drop-shadow( 0px 10px 20px transparent-black-low )

      &.left-arrow
        left 30px
        transform rotate( 180deg )

      &.right-arrow
        right 30px

      @media all and ( min-width 1200px )
        top 250px

  h1
    margin 0
    padding 50px 0
    color white
    font-size 36px

  .case-study
    width 60%
    height 0px
    opacity 0
    box-sizing border-box
    max-width 700px
    margin auto
    color white
    overflow hidden
    transition none

    img.study,
    video.study
      display block
      width 100%
      // max-width 800px
      max-height 400px
      margin 0 auto 20px auto
      // padding-bottom 20px
      border-radius 5px
      background dark-blue
      filter drop-shadow( 0px 10px 20px transparent-black-low )
      overflow hidden

    .quote-text
      font-size 30px
      padding-bottom 20px

    .attribution
      font-size 20px
      font-weight 400
      color light-blue
      padding-bottom 20px

    .stats
      padding-top 40px
      display flex
      // justify-content space-between

      .stat
        display flex
        justify-content center
        align-items center

        .number
          padding-right 10px
          font-size 40px
          font-weight bold

        .text
          font-size 20px
        .subtext
          font-size 12px

      .stat:nth-child( odd )
        padding-right 30px
        border-right 1px solid light-blue

      .stat:nth-child( even )
        padding-left 30px

      .stat:last-child
        padding-right 0

    &.active
      width 100%
      height 100%
      min-height 830px
      max-height 830px
      opacity 1
      transition opacity 1s, width 1s, height 0.1s

  @media screen and ( max-width 768px )
    padding-bottom 80px

    aside.decoration
      background-position bottom
      background-size 45%

    .arrow-container
      position static

      img.arrow
        &.left-arrow
        &.right-arrow
          top 50px
          width 50px

    .carousel
      padding 0 40px

    .case-study
      .stats
        flex-direction column
        align-items flex-start

        .stat:nth-child( odd ),
        .stat:nth-child( even )
          padding-right 0
          border-right none
          padding-left 0
