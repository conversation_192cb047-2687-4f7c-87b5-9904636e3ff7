.page-segment.main
  padding-bottom 80px

  .jumbotron
    position relative
    max-width 1200px
    padding 40px 20px
    display flex
    justify-content space-between
    align-items center

    .image-container
      width 50%

      img.background-image
        position absolute
        z-index 0
        right -110px
        height 250px
        top 140px

      .cover-image
        position relative
        top 25px
        .cover-video-placeholder
            position absolute
            left 0
            z-index 2
            width 100%
            max-width 800px
            cursor pointer
            box-shadow 0 30px 35px -5px rgba(0, 0, 0, 0.2), 0 20px 20px -5px rgba(0, 0, 0, 0.1)
            img.play-button
                position absolute
                top calc(50% - 30px)
                left calc(50% - 30px)
                width 60px
                margin auto
                z-index 5
            img.cover
                z-index 4
                width 100%
                max-width 800px
                box-shadow 0 30px 35px -5px rgba(0, 0, 0, 0.2), 0 20px 20px -5px rgba(0, 0, 0, 0.1)
            img.cover2
                z-index 3
                position absolute
                left 0px
                top 4px
                width 100%
                max-width 800px
        video
            position relative
            z-index 1
            display block
            width 100%
            max-width 800px
            margin auto
            padding-bottom 20px
            border-radius 5px
            filter drop-shadow( 0px 10px 20px rgba( 0, 0, 0, 0.15 ) )


    .text-container
      position relative
      z-index 2
      width 40%

      h1
        padding 20px 0 15px 0
      .text
        font-size 24px

    form.contact-us
      position relative
      z-index 2
      min-width none

      .input-group
        justify-content flex-start
        align-items flex-start

        .input-with-label
          width auto
          margin-top 0
          font-size 15px

          input[type="email"]
            margin-top 0
            margin-right 10px
            padding 12px 20px
            border 1px solid #E0E0E0
            font-size 16px
            color blue

        button
          margin-top 0
          width auto
          height 45px
          text-transform uppercase
          padding 15px 20px
          font-size 15px

  .column
    height 160px

    img
      width 50px

  @media all and ( max-width 768px )
    .jumbotron
      flex-direction column

      .image-container
        width 100%
        padding-top 40px

        img.background-image
          right -50px
          bottom 130px
          top auto

        img.cover-image
          width 100%
          max-width 600px
          margin auto
          padding-bottom 20px

      .text-container
        width 100%
        padding 0 20px

        h1
          padding 0 0 15px 0

      form.contact-us
        margin-bottom 30px
        padding 0 20px
        width 100%
        min-width auto

        .input-group
          flex-direction column
          align-items flex-start

          .input-with-label
            width 100%
            margin 0 0 20px 0

        button
          width 100%
          margin-top 20px
          height 50px

    .column
      padding-bottom 20px

      &:last-child
        padding-bottom 0

.page-segment.city-logo-scroller
  min-height 200px
  background-size cover
  background-repeat repeat-x

  animation scroll 80s linear infinite

  @keyframes scroll
    0%
      background-position-x 50px
    100%
      background-position-x -2880px

.page-segment.footer-contact-form
  background blue-gradient-2
  color white
  min-height auto

  .wrapper
    flex-direction column
    align-items center
    padding 80px 20px

  .jumbotron
    padding-top 0
    display flex
    flex-direction column

    .title
      padding 0 0 15px 0

    .text
      font-size 20px
      color light-blue

  @media all and ( max-width 768px )
    .wrapper
      flex-wrap nowrap
      padding 50px
      box-sizing border-box

    .jumbotron
      width auto
      padding 0 40px 0

    .contact-us-form-group
      width 100%

form.contact-us
  min-width 600px
  margin-top 30px
  color white

  .input-group
    display flex
    flex-direction row
    justify-content space-between
    align-items flex-end

    .input-with-label
      width calc(50% - 10px)

      label
        color light-blue

  .input-group:last-child
    .input-with-label
      width calc(70% - 10px)

    button
      width calc(30% - 10px)
      height 43px
      line-height 10px

  @media all and ( max-width 768px )
    min-width auto

    .input-group
      flex-direction column
      align-items flex-start

      .input-with-label
        width 100%
        margin 0 0 20px 0

    .input-group:last-child
      .input-with-label
        width 100%
      button
        width auto
        margin-top 20px
        height 50px
