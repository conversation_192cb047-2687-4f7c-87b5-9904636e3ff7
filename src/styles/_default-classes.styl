.alert-wrapper
  position fixed
  left 0
  top 0
  z-index 1002
  display flex
  justify-content center
  width 100%

  .alert
    position relative
    top 0
    padding 30px 15px
    width 100%
    background-color dark-blue
    color white
    transition opacity .5s
    font-size 25px

.hidden
  display block
  height 0px !important
  width  0px !important
  min-height 0 !important
  min-width 0 !important
  margin 0px !important
  padding 0px !important
  border-width 0px !important
  opacity 0 !important
  overflow hidden !important
  filter blur( 20px ) brightness( 1.75 )

// @media all and ( min-width 641px )
@media all and ( min-width 768px )
  .mobile-shown
    display none !important

@media all and ( max-width 768px )
  .mobile-hidden
    display none !important

.pink-text
  color pink !important

.text-left
  text-align left !important
.text-center
  text-align center !important

.link-group a
  padding 0 10px

  @media all and ( max-width 768px )
    display: block;
    text-align: center;

.clear
  clear both

.flex-container
  display flex
  justify-content space-between
  margin auto
  width 100%
  max-width 1000px

  .subtitle
    font-size 24px
    font-weight bold

  .text
    font-size 16px

  .column
    display flex
    flex-direction column
    align-items center
    justify-content space-between

  @media all and ( max-width 768px )
    flex-direction column

.jumbotron
  margin 0 auto
  padding 20px
  width calc( 90vw )
  max-width 1080px

  h1
    margin 0
    font-weight 600
    font-size 45px
    font-weight 200

  h2
    margin 0
    font-size 35px
    font-weight 200

  h3
    margin-top 0
    color blue
    font-size 30px
    font-weight 300

  .title
    font-size 36px
    padding 40px 0
    font-weight 400

  .text
    font-size 18px

  button
    margin-top 70px
    padding 15px 50px 19px 50px
    font-size 25px

  &.left
    margin-right 90px
  &.right
    margin-left 70px

  @media all and ( max-width 768px )
    &.left,
    &.right
      margin-left 0
      margin-right 0

.page-segment
  min-height 500px
  background-position center
  background-size cover
  background-repeat no-repeat
  background white
  overflow hidden

  .wrapper
    display flex
    align-items center
    margin auto
    padding 0 50px
    max-width 1080px
    border-radius 10px

    @media all and ( max-width 640px )
      flex-direction column
      flex-wrap wrap
      padding 0 20px
      width calc( 100% - 40px )
      min-width auto

      .jumbotron
        margin-top 50px !important
        padding 0 0 20px 0 !important;
        width 100% !important

        .text
          max-width 100% !important

      .image-wrapper
        width calc( 100% - 40px )
        text-align center
        img
          height auto
          width auto
          max-height calc( 100vw - 130px )

