.page-segment.about-us
  background-color offwhite

  .wrapper
    max-width 900px
    flex-direction column
    padding 0

  .jumbotron
    padding-bottom 70px
    width auto

    h1
      padding 50px 0

  h2
    position relative
    z-index 3
    font-weight 400
    color light-blue

  .flex-container.team
    position relative

    img.decoration
      position absolute
      z-index 0
      left calc(50% - 400px)
      top 20px
      width 800px

    .column
      position relative
      z-index 1
      flex-direction row
      margin-top 70px
      border-radius 5px
      overflow hidden
      background white
      box-shadow 0 10px 20px 0 rgba( 0, 0, 0, 0.1 )

      img.team-photo
        width 50%

      .text
        width 50%
        padding 30px
        font-size 15px

  .flex-container.founders
    padding 60px 0

    h3
      margin 10px 0
      font-weight 100
      font-size 24px
      color light-blue
    h4
      margin 0px 0 10px 0

    .column
      align-items flex-start
      padding 40px
      text-align left
      background white
      box-shadow 0px 2px 27px rgba( 0, 0, 0, 0.2 )
      border-radius 5px

      &:first-child
        margin-right 40px

    .image-wrapper
      display flex
      justify-content center
      align-items center
      position relative
      width 100%

      .team-photo
        width calc( 60% + 20px )

    .social-media-wrapper
      margin-right 20px
      width calc( 40% - 40px )
      height 144px
      border-radius 0px 5px 5px 0px
      background transparent-blue

      .link-group
        position relative
        margin-top 32px
        padding-top 25px
        height 55px
        width calc( 100% + 20px )
        text-align center
        border-radius 0px 5px 5px 0px
        background blue-gradient

  @media screen and ( max-width 768px )
    padding 0 40px

    .flex-container.team
      flex-direction column
      align-items center

      img.decoration
        left calc(50% - 380px)
        top 150px
        width 770px
        transform rotate( 90deg )

      .column
        flex-direction column
        width 100%
        margin-bottom 20px

        img.team-photo
          width 100%

        .text
          box-sizing border-box
          width 100%
          padding 30px

    .flex-container.founders
      flex-direction column
      align-items center

      .column
        box-sizing border-box
        width 100%
        margin-bottom 20px

        &:first-child
          margin-right 0
          margin-bottom 40px

        .image-wrapper
          .team-photo
            width 60%

          .social-media-wrapper
            width calc( 40% - 10px )

            .link-group
              display: flex
              flex-direction row
              justify-content center
              align-items center
              padding 0
              height 80px
