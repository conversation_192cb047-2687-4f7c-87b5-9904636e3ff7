.page-segment.features
  padding 70px 0
  background-color offwhite

  h1
    padding-bottom 40px
    color text-blue

  .jumbotron
    flex-direction column

  .flex-container:nth-child(odd)
    flex-direction row-reverse

    .column.video img
      right -30px
      left auto

  .flex-container
    display flex
    align-items center
    justify-content space-between
    padding 40px 20px

    .column.video
      position relative

      img
        position absolute
        top 50px
        left -30px
        z-index 0
        width 200px

      video
        position relative
        z-index 1
        width 50vw
        max-width 550px
        height auto
        border-radius 5px
        background dark-blue
        box-shadow 0 10px 20px rgba( 0, 0, 0, 0.15 )
        user-select none
        -webkit-user-select none

    .column.text
      position relative
      right -20px
      width 400px
      height 180px
      padding-left 40px
      align-items flex-start

      .subtitle
        font-weight 400
        font-size 20px

      .text
        font-size 16px

      a
        color pink

        &:hover
          color blue

  .flex-container:nth-child(odd)
    flex-direction row-reverse

    .column.video img
      right -30px
      left auto

    .column.text
      left -20px
      right auto
      padding-left 0
      padding-right 40px

  @media all and ( max-width 768px )
    padding 40px 0

    .wrapper
      padding 40px
      padding-top 0
      width calc( 100% - 80px )

    .jumbotron
      padding-top 0
      margin-top 0

      h1
        padding-bottom 80px

    .flex-container,
    .flex-container:nth-child(odd)
      flex-direction column
      align-items flex-start
      padding 0 0 10px 0 

      .column.video img
        display none

      .column.video video
        align-items flex-start
        text-align left
        width 100%
        height auto
        margin-bottom 40px

      .column.text
        flex-direction column
        align-items flex-start
        text-align left
        left 0
        right auto
        padding-left 0
        padding-right 0
        width 100%
        height auto
        padding-bottom 20px

        img
          padding-bottom 10px

        .subtitle
          padding-bottom 10px
          font-size 20px

        .text
          padding-bottom 30px
          font-size 16px

        a
          padding-bottom 20px
          color pink

          &:hover
            color blue
