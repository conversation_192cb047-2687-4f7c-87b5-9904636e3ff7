import { createContext, useState, ReactChild, useContext } from 'react';

import { ClientService } from 'services';
import Banner from 'components/Banner';
import Published from "popups/Published";

export const PublishContext = createContext<any>(null);

interface PublishProps {
  service: ClientService,
  children: ReactChild | ReactChild[]
}

export const PublishContextProvider = (props: PublishProps) => {
  const defaultState = props.service.client?.isPublished === false ? true : false
  const { service, children } = props

  const [showBanner, setShowBanner] = useState(defaultState)
  const [showPopUp, setPopUp] = useState(false)

  const updateClient = () => {
    service.publishClient(() => {
      setShowBanner(false)
      setPopUp(true)
    })
  }

  const message = "Your Rep'd website will not go live until you're ready and you hit the 'Launch site' button."
  const link = service.client?.link || ''

  return (
    // <PublishContext.Provider value={{ showBanner: (message: string) => setShowBanner(true) }}>
    //   {showBanner && (
    //     <Banner callback={updateClient} link={link} message={message} />
    //   )}

    //   <Published isOpen={showPopUp} handleClose={setPopUp} link={link} />

      <>
        {children}
      </>
    // {/* </PublishContext.Provider> */}
  );
};

export const usePublish = () => useContext(PublishContext);
