import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware'

interface FilterState {
  locationFilters: FilterType[];
  timeFilter: FilterType | null;
  setTimeFilter: (filter: FilterType | null) => void;
  setLocationFilters: (filters: FilterType[]) => void;
  addLocationFilter: (filter: FilterType) => void;
  removeLocationFilter: (filter: FilterType) => void;
}

export type FilterType = {
  name: string;
  value: string;
}

export const useFilterStore = create<FilterState>()(
  devtools(
    persist(
      (set, get) => ({
        locationFilters: [],
        timeFilter: null,
        setTimeFilter: (filter: FilterType | null) => {
          set((state) => ({ timeFilter: filter }));
        },
        setLocationFilters: (filters: FilterType[]) => {
          set((state) => ({ locationFilters: filters }));
        },
        addLocationFilter: (filter: FilterType) => {
          const locationFilters = get().locationFilters;
          locationFilters.push(filter);
          set((state) => ({ locationFilters: locationFilters }));
        },
        removeLocationFilter: (filter: FilterType) => {
          const locationFilters = get().locationFilters;
          const filteredLocationFilters = locationFilters.filter(item => item.value !== filter.value);
          set((state) => ({ locationFilters: filteredLocationFilters }));
        },
      }),
      {
        name: 'filter-storage',
      },
    )
  )
)