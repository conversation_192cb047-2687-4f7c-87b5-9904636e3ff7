import { create } from 'zustand';
import { devtools } from 'zustand/middleware'

interface UIState {
  uiType: "default" | "pdf";
  setUiType: (uiType: "default" | "pdf") => void;
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  setSidebarOpen: (isOpen: boolean) => void;
  isPdfExporting: boolean;
  setPdfExporting: (isExporting: boolean) => void;
}

export const useUIStore = create<UIState>()(
  devtools(
    (set) => ({
      uiType: "default",
      setUiType: (uiType: "default" | "pdf") => {
        set({ uiType });
      },
      isSidebarOpen: false,
      toggleSidebar: () => set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
      setSidebarOpen: (isOpen: boolean) => set({ isSidebarOpen: isOpen }),
      isPdfExporting: false,
      setPdfExporting: (isExporting: boolean) => set({ isPdfExporting: isExporting }),
    })
  )
)