import Notification from 'components/Notification';
import { createContext, useState, ReactChild, useContext, useEffect } from 'react';

export const NotificationContext = createContext<any>(null);

export const NotificationContextProvider = (props: { children: ReactChild | ReactChild[] }) => {
  const [showAlert, setShowAlert] = useState(false)
  const [msg, setMsg] = useState("")
  const handleClose = () => { setShowAlert(false); setMsg("") }

  useEffect(() => { setTimeout(handleClose, 7000) })

  return (
    <NotificationContext.Provider value={{ showAlert: (message: string) => { setShowAlert(true); setMsg(message) } }}>
      <>
        {props.children}
      </>
      {showAlert && (
        <Notification handleClose={handleClose} message={msg} />
      )}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => useContext(NotificationContext);