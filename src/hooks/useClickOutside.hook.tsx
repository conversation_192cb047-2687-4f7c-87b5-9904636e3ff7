import { RefObject, SyntheticEvent, useEffect, useState } from 'react';

export default function useClickOutside(ref: RefObject<HTMLDivElement>) {
  const [isOutside, setIsOutside] = useState(false);

  useEffect(() => {

    function handleClickOutside(event: SyntheticEvent<MouseEvent> | any) {
      if (ref.current && !ref.current.contains(event.target)) {
        setIsOutside(true);
      }
    }

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [ref]);

  return {
    isOutside,
    setIsOutside
  };
}
