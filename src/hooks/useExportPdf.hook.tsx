import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { jsPDF } from 'jspdf';
import {
    PageConfig,
    waitForLoading,
    capturePageToCanvas,
    addPageToPdf,
    createPdf
} from '../helpers/pdfExport.helper';
import { useUIStore } from './zustand/uiStore';

type Checked = boolean;

const useExportPdf = (set: (uiType: "default" | "pdf") => void) => {
    const navigate = useNavigate();
    const [originalPath, setOriginalPath] = useState<string>('');
    const setPdfExporting = useUIStore(state => state.setPdfExporting);

    const capturePageAndAddToPdf = async (pdf: jsPDF, pageConfig: PageConfig, needsNewPage: boolean): Promise<void> => {
        const { path, alias, selector, waitTime } = pageConfig;

        navigate(path);
        await waitForLoading();
        const canvas = await capturePageToCanvas(selector, waitTime);
        await addPageToPdf(pdf, canvas, alias, needsNewPage);
    };

    const exportPDF = async (exportEngagements: Checked, exportVideos: Checked, exportEmails: Checked, loading: boolean) => {
        try {
            // Set PDF exporting state to true to show the loader
            setPdfExporting(true);

            // If we're already loading, wait before proceeding
            if (loading) {
                await waitForLoading();
            }

            set("pdf");
            setOriginalPath(window.location.pathname);

            // Initialize PDF
            const pdf = createPdf();

            // Configure pages to export with page-specific settings
            const pagesToExport: PageConfig[] = [
                {
                    path: '/voter-analytics',
                    enabled: exportEngagements,
                    alias: 'engagements',
                    // selector: '.main-content',
                    waitTime: 1000
                },
                {
                    path: '/voter-analytics/videos',
                    enabled: exportVideos,
                    alias: 'videos',
                    // Video page might need a specific selector and more time to load
                    waitTime: 1000
                },
                {
                    path: '/voter-analytics/emails',
                    enabled: exportEmails,
                    alias: 'emails',
                    waitTime: 1000
                }
            ];

            // Filter only enabled pages
            const enabledPages = pagesToExport.filter(page => page.enabled);

            // Add each enabled page to PDF
            for (let i = 0; i < enabledPages.length; i++) {
                await capturePageAndAddToPdf(pdf, enabledPages[i], i > 0);
            }

            pdf.save("export.pdf");

            navigate('/voter-analytics'); // originalPath
        } catch (error) {
            console.error('Error exporting PDF:', error);
        } finally {
            // Reset UI state
            set("default");
            // Set PDF exporting state to false to hide the loader
            setPdfExporting(false);
        }
    };

    return { exportPDF };
};

export default useExportPdf;

