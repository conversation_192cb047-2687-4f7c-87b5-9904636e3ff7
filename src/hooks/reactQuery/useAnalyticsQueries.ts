import { useQuery, useQueryClient } from '@tanstack/react-query';
import { StatService } from 'services';
import * as interfaces from 'interfaces';
import { FilterType } from 'hooks/zustand/filterStore';

// Query keys
export const QUERY_KEYS = {
  STATS: 'stats',
  ANALYTICS: 'analytics',
  QUESTION_ANALYTICS: 'questionAnalytics',
  TRENDING_ANALYTICS: 'trendingAnalytics',
  VIDEOS: 'videos',
  EMAIL_ANALYTICS: 'emailAnalytics',
  EMAIL_CHART_DATA: 'emailChartData',
  EMAIL_BULK_SEND_DATA: 'emailBulkSendData',
};

// Fetch stats
export const useStats = (service: StatService) => {
  return useQuery({
    queryKey: [QUERY_KEYS.STATS],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        service.fetch((stats: any) => {
          resolve(stats);
        });
      });
    },
  });
};

// Fetch analytics
export const useAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return new Promise<interfaces.EngagementAnalyticsInterface>((resolve) => {
        service.fetchAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (data: interfaces.EngagementAnalyticsInterface) => {
            resolve(data);
          }
        );
      });
    },
  });
};

// Fetch question analytics
export const useQuestionAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.QUESTION_ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        service.fetchQuestionAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (questionData: any) => {
            resolve(questionData);
          }
        );
      });
    },
  });
};

// Fetch trending analytics
export const useTrendingAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TRENDING_ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        service.fetchTrendingAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (trendingData: any) => {
            resolve(trendingData);
          }
        );
      });
    },
  });
};

// Fetch videos
export const useVideos = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.VIDEOS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return new Promise<{
        videos: interfaces.VideoStatInterface[],
        videoFeedbackStats: interfaces.VideoStatInterfaceVideo['videoFeedbackStats'][],
        completionRateStats: interfaces.VideoStatInterfaceVideo['completionRateStats'][],
        nationalCompletionRate: { average_completion_rate: number }[]
      }>((resolve) => {
        service.getVideos(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (data: any) => {
            resolve(data);
          }
        );
      });
    },
  });
};

// Fetch email analytics
export const useEmailAnalytics = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.EMAIL_ANALYTICS, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        service.fetchEmailAnalytics(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (emailData: any) => {
            resolve(emailData);
          }
        );
      });
    },
  });
};

// Fetch email chart data
export const useEmailChartData = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.EMAIL_CHART_DATA, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return service.fetchEmailChartData(
        timeFilter,
        locationFilters,
        isSuperAdmin
      );
    },
  });
};

// Fetch email bulk send data
export const useEmailBulkSendData = (
  service: StatService,
  timeFilter: FilterType | null,
  locationFilters: FilterType[],
  isSuperAdmin: boolean
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.EMAIL_BULK_SEND_DATA, timeFilter?.value, locationFilters.map(f => f.value), isSuperAdmin],
    queryFn: () => {
      return new Promise<any>((resolve) => {
        service.fetchEmailBulkSendData(
          timeFilter,
          locationFilters,
          isSuperAdmin,
          (bulkSendData: any) => {
            resolve(bulkSendData);
          }
        );
      });
    },
  });
};

// Utility function to invalidate queries when filters change
export const useInvalidateQueriesOnFilterChange = () => {
  const queryClient = useQueryClient();
  
  return {
    invalidateAnalyticsQueries: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ANALYTICS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.QUESTION_ANALYTICS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.TRENDING_ANALYTICS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.VIDEOS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EMAIL_ANALYTICS] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EMAIL_CHART_DATA] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.EMAIL_BULK_SEND_DATA] });
    }
  };
};
