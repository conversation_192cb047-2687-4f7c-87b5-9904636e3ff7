import express from 'express';
import { celebrate } from 'celebrate';
import { Op } from 'sequelize';
import service from '../services/answerService.js';
import validation from '../validations/answerValidation.js';
import ngpVanExportRequestsModel from '../models/ngpVanExportRequestsModel.js';
import { getAnswerSends } from '../services/ngpVanService.js';
import { conditionalCelebrate } from '../helpers/validationHelper.js';

const router = express.Router();

// End-points

// celebrate( validation.publish )
router.put('/api/v1.0.0/answers/:id/publish', conditionalCelebrate(), ( request, response, next ) => {
	return(
		service.publish( Object.assign( request.params, request.body ), request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Answer Published',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

// celebrate( validation.publish )
router.put('/api/v1.0.0/answers/:id/replace-video', conditionalCelebrate(), ( request, response, next ) => {
	return(
		service.replace( Object.assign( request.params, request.body ), request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Answer Replaced',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

// celebrate( validation.create )
router.post( '/api/v1.0.0/answers', conditionalCelebrate(), ( request, response, next ) => {
	return(
		service.create( request.body, request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'answer Created',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

// celebrate( validation.update )
router.put( '/api/v1.0.0/answers/:id', conditionalCelebrate(), ( request, response, next ) => {
	console.log( 'update', request.body );

	return(
		service.update( Object.assign( request.params, request.body ), request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Answer Updated',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

// Allow video duration to be updated from the webapp
router.put( '/api/v1.0.0/answers/webapp/:id', conditionalCelebrate(), ( request, response, next ) => {
	if (!request.body?.videoDuration)
		return next( new Error( 'Missing videoDuration' ) );

	return(
		service.update( Object.assign( request.params, { videoDuration: request.body?.videoDuration } ), request.auth )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Answer Updated',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

router.get( '/api/v1.0.0/answers/:id', ( request, response, next ) => {
	return(
		service.getAnswer( request.params.id )
			.then( dto => {
				response.status( 201 ).json( {
					'message': 'Answer',
					'totalEntries': 1,
					'data': dto
				} );
			} )
			.catch( next )
	);
} );

router.get( '/api/v1.0.0/answers', ( request, response, next ) => {
	const isAdmin = ( request.auth && request.auth.user && [ 'admin', 'super admin', 'manager' ].includes( request.auth.user.accessLevel ) ) || false;
	const clientId = request.query.clientId;
	const page = request.query.page;

	// Build the 'and' criteria string
	let andCriteria = 'client_id:' + clientId;

	const criteria = {
		pagination: {
			page: page !== undefined ? parseInt(page) : 0,
			limit: page !== undefined ? 20 : true,
			order: []
		},
		any: undefined,
		and: andCriteria,
		or: undefined,
		showDisabled: false
	};

	// If not admin, only show non-draft answers (isDraft is null or false)
	if (!isAdmin) {
		criteria.customPredicates = {
			[Op.or]: [
				{ isDraft: null },
				{ isDraft: false }
			]
		};
	}

	console.log(criteria)

	var totalEntries;

	const filterSearchResults = async data => {
		totalEntries = data[0];
		let answers = data[1];

		var result = [];

		answers.sort((a, b) => {
			// First sort by isPinned (pinned items first)
			if (a.isPinned && !b.isPinned) return -1;
			if (!a.isPinned && b.isPinned) return 1;

			// Then sort by creation date (newest first)
			return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
		});
		await answers.map( ( answer, i ) => {
			// for ( let n in answer.comments )
			// 	if ( answer.comments[i].userId == request.query.userId ) {
			// 		answer.commented = true; break;
			// 	}

			for ( const like of answer.userLikes )
			  if ( like.userId == request.auth.user.id ) {
			    answer.liked = true;
					break;
				}

			answer.sends = {};
			answer.userLikes = [];

			if ( answer.question ) answer.question.userVotes = [];

			result[result.length] = answer;

			return answer;
		} );

		return result;
	};

	const addNgpVanData = async ( answers ) => {
		if (request.auth.user.accessLevel.match( /admin|manager/ ) === null )
		  return answers;

		var validAnswerIds = await ngpVanExportRequestsModel.findAll( {
			where: { answer_id: answers.map( a => a.id ) }
		} );

		validAnswerIds = validAnswerIds.map( a => a.answerId );

		for ( const i in answers ) {
			const answer = answers[i];

			if ( isAdmin && validAnswerIds.includes( answer.id ) )
				answers[i].sends = await getAnswerSends( request.auth.user, answer.id );
		}

		return answers;
	};

	return (
		service.search( criteria, request.auth, page === undefined )
			.then( filterSearchResults )
			.then( addNgpVanData )
			.then( async ( answers ) => {
				return response.status( 200 ).json( {
					'message'      : 'Answers Retrieved',
					'totalEntries' : totalEntries,
					'page'  : criteria.pagination.page,
					'limit' : criteria.pagination.limit,
					'data'  : answers
				} );
			} )
			.catch( next )
	);
} );

router.delete('/api/v1.0.0/answers', conditionalCelebrate(validation.delete), (request, response, next) => {
	return service.disable(request.body.id, request.auth)
		.then(dto => {
			response.status(200).json({
				'message': 'Answer Disabled',
				'totalEntries': 1,
				'data': [dto]
			});
		})
		.catch(next);
});

router.delete('/api/v1.0.0/answers/:id', conditionalCelebrate(), (request, response, next) => {
	return service.disable(request.params.id, request.auth)
		.then(dto => {
			response.status(200).json({
				'message': 'Answer Disabled',
				'totalEntries': 1,
				'data': [dto]
			});
		})
		.catch(next);
});

export default {
	router: router,

	securityConstraints: [ {
		regex: '/api/v.*/answers',
		methods: [ 'POST' ],
		accessLevels: [ 'super admin', 'admin', 'manager', 'guest admin' ]
	}, {
		regex: '/api/v.*/answers',
		methods: [ 'PUT', 'DELETE' ],
		accessLevels: [ 'super admin', 'admin', 'manager', 'guest admin' ]
	}, {
		regex: '/api/v.*/answers/webapp',
		methods: [ 'PUT' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/answers',
		methods: [ 'GET' ],
		accessLevels: []
	} ]
};
