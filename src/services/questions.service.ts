import * as interfaces from '../interfaces';
import { ApiService } from './api.service';

export class QuestionService extends ApiService {
    id: string
    questions: interfaces.QuestionInterface[] = [];
    transcripts: interfaces.TranscriptInterface[] = [];

    constructor(token?: string, clientId?: string) {
        super(token, clientId);
        this.id = 'Questions';
    }

    async getQuestions(callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.questions}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Get`;

        await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.QuestionInterface[] = response.data.data;

            if (!responseData || responseData.length < 1)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

            callback(this.questions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async getQuestion(questionId: string, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.questions}?clientId=${this.clientId}&questionId=${questionId}`;
        const errorIdentifier = `${this.id} -> Get`;

        await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.QuestionInterface[] = response.data.data[0];

            if (!responseData || responseData.length < 1)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

            callback(this.questions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async removeQuestion(questionId: string, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.questions}/${questionId}`;
        const errorIdentifier = `${this.id} -> Remove`;

        // return values

        await this.axiosInstance.delete(endpoint).then((response: any) => {
            const responseData: any = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

            callback(this.questions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async approveQuestion(values: any, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.questions}/${values.id}`;
        const errorIdentifier = `${this.id} -> Approve`;

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: any = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

            callback(this.questions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async addSuggestedNotesToQuestion(values: { id: string, suggestedNotes: string }) {
        const endpoint = `${this.base}${this.paths.questions}/${values.id}`;
        const errorIdentifier = `${this.id} -> Add Suggested Notes`;

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: any = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async updateQuestionText(values: { id: string, text: string }, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.questions}/${values.id}`;
        const errorIdentifier = `${this.id} -> Update Question Text`;

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: any = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

            if (callback) callback(this.questions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async shareQuestion(values: { id: string, invitees: string[], dueDate?: string }) {
        const endpoint = `${this.base}${this.paths.questions}/${values.id}/share`;
        const errorIdentifier = `${this.id} -> Share Question`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: any = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questions = responseData;

        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.questions;
    }

    async createQuestion(values: any, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.questions}`;
        const errorIdentifier = `${this.id} -> Create`;
        let isError = false;

        const res = await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: any = response.data?.data?.[0];
            if (!responseData || response.errors) {
                isError = true;
                this.reportInvalidResponseError(errorIdentifier, response);
                return response;
            }

            this.questions = responseData;

            callback(this.questions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        if (isError) throw res.data;

        return this.questions;
    }

    async getTranscripts(callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.transcripts}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Get Transcripts`;

        await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.TranscriptInterface[] = response.data.data;

            if (!responseData) // || responseData.length < 1
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.transcripts = responseData;

            callback(this.transcripts || []);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.transcripts;
    }

    async createTranscript(values: any) {
        const endpoint = `${this.base}${this.paths.transcripts}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Create Transcript`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: any = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.transcripts;
    }
}
