import React, { create<PERSON>ontext, ReactChild, useContext } from "react";
import { ClientInterface, UserInterface } from "../interfaces";

type Type = {
  client?: ClientInterface;
  token?: string;
  user?: UserInterface;
};

export const AuthContext = createContext<Type>({});

type Props = Type & { children: ReactChild | ReactChild[] };
export const AuthProvider = ({ children, client, token, user }: Props) => {
  return (
    <AuthContext.Provider
      value={{
        client,
        token,
        user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export function useAuthContext() {
  return useContext(AuthContext);
}
