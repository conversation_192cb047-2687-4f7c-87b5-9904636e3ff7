import * as interfaces from "../interfaces";
import { ApiService } from "./api.service";

export class FileService extends ApiService {
  id: string;
  user?: interfaces.UserInterface;
  answer?: interfaces.AnswerInterface;

  constructor(token?: string, clientId?: string) {
    super(token, clientId);
    this.id = "Files";
  }

  async uploadAnswer(values: any, callback?: VoidFunction | any) {
    // TODO: Move to the answers service
    const endpoint = `${this.base}${this.paths.answers}`;
    const errorIdentifier = `${this.id} -> Upload Answer?`;

    await this.axiosInstance
      .post(endpoint, values)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answer = responseData;

        if (callback) callback(this.answer);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answer;
  }

  async uploadVideo(
    values: any,
    callback?: VoidFunction | any,
    onProgress?: any
  ) {
    const endpoint = `${this.base}${this.paths.files}`;
    const errorIdentifier = `${this.id} -> Upload Video`;

    await this.axiosInstance
      .post(endpoint, values, {
        onUploadProgress: onProgress
          ? (progressEvent: any) => {
              let percentComplete =
                (progressEvent.loaded / progressEvent.total) * 100;
              onProgress(percentComplete);
            }
          : null,
      })
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.user = responseData;

        if (callback) callback(this.user);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.user;
  }

  async uploadTest(values: any) {
    const endpoint = `${this.base}${this.paths.uploadTest}`;

    return await this.axiosInstance.post(endpoint, values);
  }

  async uploadImage(values: any, callback?: VoidFunction | any) {
    const endpoint = `${this.base}${this.paths.images}`;
    const errorIdentifier = `${this.id} -> Upload Image`;

    await this.axiosInstance
      .post(endpoint, values)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.user = responseData;

        if (callback) callback(this.user);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.user;
  }
}
