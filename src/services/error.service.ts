import * as interfaces from 'interfaces';
import { ApiService } from './api.service';
import axios from 'axios';

export class ErrorService extends ApiService {
  id: string;
  endpointErrors: interfaces.EndpointErrorInterface[] = [];

  constructor(token?: string, clientId?: string) {
    super(token, clientId);
    this.id = 'ErrorService';
  }

  async getEndpointErrors(callback?: (errors: interfaces.EndpointErrorInterface[]) => void) {
    const endpoint = `${this.base}/endpoint-errors`;
    const errorIdentifier = `${this.id} -> GetEndpointErrors`;

    try {
      // Ensure we have the latest token in headers
      const headers = this.setHeaders({
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      });

      // Use axios directly with explicit headers instead of the instance
      const response = await axios.get(endpoint, { headers });
      const responseData: interfaces.EndpointErrorsResponse = response.data;

      if (!responseData || !responseData.data) {
        this.reportInvalidResponseError(errorIdentifier, response);
        return [];
      }

      this.endpointErrors = responseData.data;

      if (callback) {
        callback(this.endpointErrors);
      }

      return this.endpointErrors;
    } catch (error: any) {
      // Check if it's an authentication error
      if (error.response && error.response.status === 401) {
        console.error('Authentication error when fetching endpoint errors. Token may have expired.');

        // If we have a token in localStorage, we could try to refresh it here
        const storedToken = localStorage.getItem('token');
        if (storedToken && storedToken !== this.token) {
          this.token = storedToken;
          // Could retry the request here if needed
        }
      }

      this.reportRequestError(errorIdentifier, error);

      // Return empty array but still call callback with empty array
      if (callback) {
        callback([]);
      }
      return [];
    }
  }
}
