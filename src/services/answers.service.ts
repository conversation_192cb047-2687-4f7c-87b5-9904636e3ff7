import { ApiService } from "./api.service";
import * as interfaces from "../interfaces";

export class AnswersService extends ApiService {
  id: string;
  answers: interfaces.AnswerInterface[] = [];
  answerList: interfaces.AnswerInterface[] = [];
  session?: interfaces.SessionInterface | null;

  constructor(token?: string, clientId?: string) {
    super(token, clientId);
    this.id = "Answers";
  }

  async getAnswer(answerId: string, callback: VoidFunction | any) {
    const endpoint = `${this.base}${this.paths.answers}/${answerId}?clientId=${this.clientId}`;
    const errorIdentifier = `${this.id} -> Get`;

    return await this.axiosInstance
      .get(endpoint)
      .then((response: any) => {
        const responseData: interfaces.AnswerInterface[] = response.data.data;

        callback(responseData);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });
  }

  async getAnswers(callback: VoidFunction | any) {
    const endpoint = `${this.base}${this.paths.answers}?clientId=${this.clientId}`;
    const errorIdentifier = `${this.id} -> Get`;

    await this.axiosInstance
      .get(endpoint)
      .then((response: any) => {
        const responseData: interfaces.AnswerInterface[] = response.data.data;

        if (!responseData || responseData.length < 1)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answerList = responseData;

        callback(this.answerList);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answerList;
  }

  async updateAnswer(
    values: interfaces.AnswerUpdateInterface,
    callback?: VoidFunction | any
  ) {
    const endpoint = `${this.base}${this.paths.answers}/${values.id}`;
    const errorIdentifier = `${this.id} -> Update`;

    // Unpin the old video before pinning the new video
    const pinnedAnswerId = this.answerList.find((answer) => answer.isPinned)?.id;
    const payload: interfaces.AnswerUpdateInterface = {
      isPinned: false,
      clientId: values.clientId,
      id: pinnedAnswerId || "",
    }
    if (values.isPinned && pinnedAnswerId) {
      await this.updateAnswer(payload)
    }

    await this.axiosInstance
      .put(endpoint, values)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answers = responseData;

        if (callback) callback(this.answers);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answers;
  }

  async publishAnswer(
    values: interfaces.AnswerUpdateInterface,
    callback?: VoidFunction | any
  ) {
    const endpoint = `${this.base}${this.paths.answers}/${values.id}/publish`;
    const errorIdentifier = `${this.id} -> Update`;

    await this.axiosInstance
      .put(endpoint, values)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answers = responseData;

        if (callback) callback(this.answers);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answers;
  }

  async replaceVideo(
    values: interfaces.AnswerUpdateInterface,
    callback?: VoidFunction | any
  ) {
    const endpoint = `${this.base}${this.paths.answers}/${values.id}/replace-video`;
    const errorIdentifier = `${this.id} -> Update`;

    await this.axiosInstance
      .put(endpoint, values)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answers = responseData;

        if (callback) callback(this.answers);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answers;
  }

  async createAnswer(values: any, callback: VoidFunction | any) {
    const endpoint = `${this.base}${this.paths.questions}`;
    const errorIdentifier = `${this.id} -> Create`;

    await this.axiosInstance
      .post(endpoint, values)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answers = responseData;

        callback(responseData);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answers;
  }

  async removeAnswer(answerId: string, callback: VoidFunction | any) {
    const endpoint = `${this.base}${this.paths.answers}/${answerId}`;
    const errorIdentifier = `${this.id} -> Remove`;

    await this.axiosInstance
      .delete(endpoint)
      .then((response: any) => {
        const responseData: any = response.data.data[0];

        if (!responseData || response.errors)
          return this.reportInvalidResponseError(errorIdentifier, response);

        this.answers = responseData;

        callback(this.answers);
      })
      .catch((error: any) => {
        this.reportRequestError(errorIdentifier, error);
      });

    return this.answers;
  }
}
