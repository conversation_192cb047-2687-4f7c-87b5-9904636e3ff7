import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

const { REACT_APP_API_URL, APP_URL } = process.env;
const API_BASE_PATH = "api/v1.0.0";
const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
  Accepts: "application/json",
};

export const API_URL_ROUTES = {
  sessions: "/sessions",
  currentSession: "/sessions/current",
  currentUser: "/users/me",
  updateUser: "/users",
  verify: "/users/verify",
  block: "/users/block",

  questions: "/questions",
  answers: "/answers",
  clients: "/clients",
  clientsList: "/clients/list",

  ngpVanLists: "/ngpvan/lists",
  ngpVanList: "/ngpvan/list",
  ngpVanTestEmail: "/ngpvan/test-email",
  ngpVanAnswerExports: "/ngpvan/answerExports",
  ngpVanGetActivistCodes: "/ngpvan/get-activist-codes",
  ngpVanSyncActivistCodes: "/ngpvan/get-activist-codes/sync",

  import: "/ngpvan/import",

  files: "/files/documents",
  images: "/files/images",
  uploadTest: "/upload-test",

  adminStats: "/admin-stats",

  stats: "/stats",
  analytics: "/stats/analytics",
  sentiment: "/stats/sentiment",
  questionAnalytics: "/stats/questions",
  trendingAnalytics: "/stats/trending",
  videoStats: "/stats/videos",
  emailAnalytics: "/stats/emails",
  emailChartData: "/stats/emails/chart",
  emailBulkSends: "/stats/emails/bulk-sends",
  transcripts: "/transcripts",
};

export class ApiService {
  base: string;
  baseURL?: string;
  basePath: string;
  paths: any;

  public appURL: string;
  public token?: string;
  public clientId?: string;

  userId?: string;

  axiosInstance: AxiosInstance;

  constructor(token?: string, clientId?: string) {
    this.appURL = APP_URL || 'https://app.repd.us';
    this.baseURL = REACT_APP_API_URL;
    this.basePath = API_BASE_PATH;
    this.base = `${this.baseURL}/${this.basePath}`;
    this.paths = API_URL_ROUTES;

    this.token = token || "";
    this.clientId = clientId || "";

    const userIdFromSearch = window.location.search.match(/\&uti=(.+)/);
    const userIdRaw = userIdFromSearch && userIdFromSearch[1] ? userIdFromSearch[1] : '';
    const userId = userIdRaw.toString().match(/^\d+$/) ? Number(userIdRaw).toString() : atob(userIdRaw.toString()).replace(/[^\d]/g, "");

    if (userId) this.userId = userId;

    this.axiosInstance = axios.create({ baseURL: this.base });

    if (this.token) this.axiosInstance.defaults.headers = this.setHeaders();
    
    // Add response interceptor for better error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        this.handleRequestError('API Request', error);
        
        // Check for authentication errors
        if (error.response && error.response.status === 401) {
          console.warn('Authentication error detected. Session may have expired.');
          // Optional: Clear token and redirect to login
          // localStorage.removeItem('token');
          // window.location.href = '/login';
        }
        
        return Promise.reject(error);
      }
    );
  }

  setUrl(endpoint: string = "") {
    return `${REACT_APP_API_URL}${API_BASE_PATH}${endpoint}`;
  }

  setHeaders(customHeaders: any = {}) {
    return {
      Authorization: this.token,
      userId: this.userId,
      ...DEFAULT_HEADERS,
      ...(customHeaders ? customHeaders : {}),
    };
  }

  reportInvalidResponseError(errorIdentifier: string, response: any) {
    console.error(`${errorIdentifier} -> Invalid Response`, response);
  }
  
  handleRequestError(errorIdentifier: string, error: any) {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`${errorIdentifier} -> Server Error (${error.response.status})`, {
        data: error.response.data,
        status: error.response.status,
        headers: error.response.headers
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error(`${errorIdentifier} -> No Response`, error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error(`${errorIdentifier} -> Request Setup Error`, error.message);
    }
    
    // Keep the original method for backward compatibility
    this.reportRequestError(errorIdentifier, error);
  }
  
  reportRequestError(errorIdentifier: string, error: any) {
    console.error(`${errorIdentifier} -> Request Error`, error);
  }
  
  // Add a method to check API connectivity
  async checkApiConnection(): Promise<boolean> {
    try {
      const response = await this.axiosInstance.get('/health-check', {
        timeout: 5000 // 5 second timeout
      });
      return response.status === 200;
    } catch (error) {
      console.warn('API health check failed:', error);
      return false;
    }
  }
}
