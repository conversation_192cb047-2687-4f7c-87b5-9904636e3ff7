import { ApiService } from './api.service';
import * as interfaces from '../interfaces';
import axios from 'axios';

export interface AdminStatEventProperties {
  userId: string;
  clientId: string;
  eventAction: string;
  eventCategory: string;
  eventLabel: string;
  accessLevel: string;

  // Additional properties specific to admin events
  pageUrl?: string;
  componentName?: string;
  actionDetails?: string;

  // System information
  origin: string;
  host: string;
  ipa: string;
  userAgent?: string;
  screenSize?: string;
}

export class AdminStatsService extends ApiService {
  private id: string;
  adminStatEvents: interfaces.AdminStatEventInterface[] = [];

  constructor(token?: string, clientId?: string) {
    super(token, clientId);
    this.id = 'AdminStatsService';
  }

  sendAdminStats(data: Partial<AdminStatEventProperties>) {
    const errorIdentifier = `${this.id} -> Send Admin Stats`;

    return this.axiosInstance.post(`${this.base}${this.paths.adminStats}`, data).catch((err: unknown) => {
      this.reportRequestError(errorIdentifier, err);
      return [];
    });
  }

  /**
   * Track an admin panel event
   * @param action The action being performed
   * @param eventProperties Additional properties for the event
   * @returns Promise with the response
   */
  trackAdminEvent(action: string, eventProperties: Partial<AdminStatEventProperties> = {}) {
    // Get user and client information from local storage or context
    const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') || '{}') : {};
    const client = localStorage.getItem('client') ? JSON.parse(localStorage.getItem('client') || '{}') : {};

    // Build event properties
    const eventData: Partial<AdminStatEventProperties> = {
      userId: user.id || this.userId || '',
      clientId: client.id || this.clientId || '',
      eventAction: action,
      eventCategory: eventProperties.eventCategory || 'AdminPanel',
      eventLabel: eventProperties.eventLabel || '',
      accessLevel: user.accessLevel || eventProperties.accessLevel || '',

      // System information
      origin: window.location.origin,
      host: window.location.pathname + window.location.search,
      ipa: user.ipa || eventProperties.ipa || '0.0.0.0',
      userAgent: navigator.userAgent,
      screenSize: `${window.innerWidth}x${window.innerHeight}`
    };

    return this.sendAdminStats(eventData);
  }

  trackEvent(componentName: string, action: string, additionalProps: Partial<AdminStatEventProperties> = {}) {
    return this.trackAdminEvent(action, {
      eventCategory: "Press",
      eventLabel: componentName,
      ...additionalProps
    });
  }

  /**
   * Get admin stat events for the last 7 days
   * @param callback Optional callback function to handle the response
   * @returns Promise with the admin stat events
   */
  async getAdminStatEvents(callback?: (events: interfaces.AdminStatEventInterface[]) => void) {
    const endpoint = `${this.base}${this.paths.adminStats}`;
    const errorIdentifier = `${this.id} -> GetAdminStatEvents`;

    try {
      // Calculate date 7 days ago
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      // Format date as ISO string
      const startDate = sevenDaysAgo.toISOString();

      // Ensure we have the latest token in headers
      const headers = this.setHeaders({
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      });

      // Use axios directly with explicit headers instead of the instance
      const response = await axios.get(`${endpoint}?start=${startDate}`, { headers });
      const responseData: interfaces.AdminStatEventsResponse = response.data;

      if (!responseData || !responseData.data) {
        this.reportInvalidResponseError(errorIdentifier, response);
        return [];
      }

      this.adminStatEvents = responseData.data;

      if (callback) {
        callback(this.adminStatEvents);
      }

      return this.adminStatEvents;
    } catch (error: any) {
      // Check if it's an authentication error
      if (error.response && error.response.status === 401) {
        console.error('Authentication error when fetching admin stat events. Token may have expired.');

        // If we have a token in localStorage, we could try to refresh it here
        const storedToken = localStorage.getItem('token');
        if (storedToken && storedToken !== this.token) {
          this.token = storedToken;
          // Could retry the request here if needed
        }
      }

      this.reportRequestError(errorIdentifier, error);

      // Return empty array but still call callback with empty array
      if (callback) {
        callback([]);
      }
      return [];
    }
  }
}
