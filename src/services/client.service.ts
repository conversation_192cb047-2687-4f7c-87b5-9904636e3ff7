import { ApiService } from './api.service';
import * as interfaces from 'interfaces';
import axios from 'axios';

import logger from 'utils/logger';

export class ClientService extends ApiService {
    id: string
    client?: interfaces.ClientInterface;
    sessions?: interfaces.SessionInterface[] = [];

    constructor(token?: string, clientId?: string, client?: interfaces.ClientInterface) {
        super(token, clientId);
        this.id = 'Clients';
        this.client = client; // Set from the app
    }

    async list(callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.clientsList}`;
        const errorIdentifier = `${this.id} -> Get Sessions`;

        await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.SessionInterface[] = response.data.data;

            if (!responseData || responseData.length < 1)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.sessions = responseData;

            callback(this.sessions);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.sessions;
    }

    async create(values: interfaces.NewClientInterface, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.clients}`;
        const errorIdentifier = `${this.id} -> Create`;

        const data = {
            user: {
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email
            },
            client: {
                name: values.clientName,
                clientType: values.clientType,
            }
        }

        await this.axiosInstance.post(endpoint, data).then((response: any) => {
            const responseData: interfaces.ClientInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            if (callback) callback(responseData);
        }).catch((error: any) => {
            callback(error);
            this.reportRequestError(errorIdentifier, error);
        });

        return this.client;
    }

    async update(values: any, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.clients}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Update`;

        // logger.info(`Updating client: ${this.clientId}`, values);
        // console.log(`Updating client: ${this.clientId}`, values);

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: interfaces.ClientInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.client = responseData;

            this.updateLocalClient();

            if (callback) callback(this.client);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.client;
    }

    async getAiResponse(value: any, callback?: VoidFunction | any) {
        const prompt = `Provide a teleprompter script response to the question: ${value}`;

        const host = window.location.hostname;
        const isStaging = host === 'localhost' || host.includes('staging') || host.includes('mock');

        const url = `https://api${isStaging ? '-staging' : ''}.repd.us/api/v1.0.0/ai-service`;

        const errorIdentifier = `${this.id} -> Update`;

        // Use direct API call for new URL
        await axios.post(url, {
            question: prompt,
            clientId: this.clientId,
            useMarvin: true,
            isTeleprompter: true,
        }).then((response: any) => {
            const responseData = response.data?.marvinAnswers?.answer?.answer || null;

            if (!responseData)
                return this.reportInvalidResponseError(errorIdentifier, response);

            if (callback) callback(
                responseData.replace(/<\//gm, "\r\n\r\n</")
                    .replace(/<[^>]*>?/gm, '')
                    .replace(/&quot;/gm, "'")
                    .replace(/&amp;/gm, "&")
                    .replace(/&lt;/gm, "<")
                    .replace(/&gt;/gm, ">")
                    .replace(/&nbsp;/gm, " ")
                    .replace(/&ldquo;/gm, '"')
                    .replace(/&rdquo;/gm, '"')
                    .replace(/&rsquo;/gm, "'")
                    .replace(/&lsquo;/gm, "'")
                    .replace(/&hellip;/gm, "...")
                    .replace(/&mdash;/gm, "--")
                    .replace(/&ndash;/gm, "-")
                    .replace(/&#39;/gm, "'")
                    .replace(/&#34;/gm, '"')
                    .replace(/&#60;/gm, "<")
                    .replace(/&#62;/gm, ">")
                    .replace(/&#160;/gm, " ")
                    .replace(/&#8220;/gm, '"')
                    .replace(/&#8221;/gm, '"')
                    .replace(/•/gm, '')
                    .replace(/ +/gm, ' ')
                    .replace(/^ /gm, '')
                    .replace(/\n\n+/gm, '\n\n')
                    .replace(/\n+: /gm, ': \n')
                    .replace(/\n+\./gm, '.\n')
                    .replace(/(.+|)Teleprompter Script(.+|)\s*/gm, "")
                    // .replace(/For more (information|details).*/gm, '')
            );
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    async publishClient(callback?: Function) {
        if (!this.client) return
        else {
            this.client.isPublished = true

            return this.update(this.client, callback)
        }
    }

    updateLocalClient() {
        if (this.client) localStorage.setItem('client', JSON.stringify(this.client));
    }
}
