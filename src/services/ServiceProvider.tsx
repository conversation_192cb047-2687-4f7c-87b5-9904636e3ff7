import React, { createContext, ReactChild, useContext } from "react";
import { QuestionService } from "./questions.service";
import { UserService } from "./user.service";
import { ClientService } from "./client.service";
import { FileService } from "./file.service";
import { StatService } from "./stats.service";
import { NgpVanService } from "./ngpvan.service";
import { AnswersService } from "./answers.service";
import { AdminStatsService } from "./adminStats.service";

type Type = {
  questionService?: QuestionService;
  answersService?: AnswersService;
  userService?: UserService;
  clientService?: ClientService;
  fileService?: FileService;
  statService?: StatService;
  ngpVanService?: NgpVanService;
  adminStatsService?: AdminStatsService;
};

export const ServiceContext = createContext<Type>({});

type Props = Type & { children: ReactChild | ReactChild[] };
export const ServiceProvider = ({
  children,
  fileService,
  answersService,
  questionService,
  ngpVanService,
  userService,
  statService,
  clientService,
  adminStatsService,
}: Props) => {
  return (
    <ServiceContext.Provider
      value={{
        fileService,
        answersService,
        questionService,
        ngpVanService,
        userService,
        statService,
        clientService,
        adminStatsService,
      }}
    >
      {children}
    </ServiceContext.Provider>
  );
};

export function useServiceContext() {
  return useContext(ServiceContext);
}
