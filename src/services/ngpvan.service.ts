import * as interfaces from '../interfaces';
import { ApiService } from './api.service'

export class NgpVanService extends ApiService {
    id: string
    ngpVan?: interfaces.NgpVanInterface[];
    activistCodes?: interfaces.ActivistCodeInterface[] = [];

    constructor(token?: string, clientId?: string) {
        super(token, clientId);
        this.id = 'ngpVan';
    }

    async getNgpVan(callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.ngpVanLists}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Get Ngp Van`;

        await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: any = response.data;

            if (!responseData)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.ngpVan = responseData;

            callback(this.ngpVan);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.ngpVan;
    }

    async postNgpVan(values: any, answerId: string, selectedSavedListId: string, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.ngpVanList}/${selectedSavedListId}/${answerId}`;
        const errorIdentifier = `${this.id} -> Send Saved List Information`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: any = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.ngpVan = responseData;

            if (callback) callback(this.ngpVan);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.ngpVan;
    }

    async postNgpVanTestEmail(values: any, answerId: string, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.ngpVanTestEmail}/${answerId}`;
        const errorIdentifier = `${this.id} -> Send Saved List Information`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: any = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.ngpVan = responseData;

            if (callback) callback(this.ngpVan);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.ngpVan;
    }

    async getAnswerExports(answerId: string, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.ngpVanAnswerExports}/${answerId}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Get Exported Answers`;

        await this.axiosInstance.get(endpoint).then((response: interfaces.NgpVanInterface) => {
            const responseData: any = response;

            if (!responseData)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.ngpVan = responseData;

            callback(this.ngpVan);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.ngpVan;
    }

    async importNgpVan(values: any, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.import}`;
        const errorIdentifier = `${this.id} -> Import from CSV`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: interfaces.NgpVanInterface[] = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.ngpVan = responseData;

            callback(this.ngpVan);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.ngpVan;
    }

    async getActivistCodes(callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.ngpVanGetActivistCodes}`;
        const errorIdentifier = `${this.id} -> Get Activist Codes`;

        await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: any = response.data;

            if (!responseData)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.activistCodes = responseData;

            callback(this.activistCodes);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.activistCodes;
    }

    async syncActivistCodes(values: any, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.ngpVanSyncActivistCodes}`;
        const errorIdentifier = `${this.id} -> Sync Activist Codes`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: any = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.activistCodes = responseData;

            if (callback) callback(this.activistCodes);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.activistCodes;
    }
}
