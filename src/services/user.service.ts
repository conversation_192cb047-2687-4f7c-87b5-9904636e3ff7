import { ApiService } from './api.service';
import * as interfaces from '../interfaces';

export class UserService extends ApiService {
    id: string
    user?: interfaces.UserInterface;

    constructor(token?: string, clientId?: string) {
        super(token, clientId);
        this.id = 'Users';
    }

    async update(values: interfaces.UserUpdateInterface, callback: VoidFunction | any = null) {
        const endpoint = `${this.base}${this.paths.currentUser}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Update`;

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: interfaces.UserInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.user = responseData;

            callback(this.user);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });

        return this.user;
    }

    async createUser(values: interfaces.UserInterface, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.updateUser}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Create`;

        await this.axiosInstance.post(endpoint, values).then((response: any) => {
            const responseData: interfaces.UserInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.user = responseData;

            callback(this.user);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
            callback(this.user, error?.response?.data || error);
        });

        return this.user;
    }

    async updateUser(values: interfaces.UserInterface, callback: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.updateUser}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Update`;

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: interfaces.UserInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.user = responseData;

            callback(this.user);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
            callback(this.user, error?.response?.data || error);
        });

        return this.user;
    }

    async block(values: { id: string, isBlocked: boolean }, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.block}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Block user`;

        await this.axiosInstance.put(endpoint, values).then((response: any) => {
            const responseData: interfaces.UserInterface = response.data;

            if (!responseData || response.errors)
                this.reportInvalidResponseError(errorIdentifier, response);

            callback(responseData);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
            callback(null);
        });
    }

    setUser(user: interfaces.UserInterface) {
        this.user = user;
    }
}
