import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';

import App from './App';
import reportWebVitals from './reportWebVitals';
import { QueryProvider } from './hooks/reactQuery/queryClient';

import './index.scss';

ReactDOM.render(
  <BrowserRouter>
    <React.StrictMode>
      <QueryProvider>
        <App />
      </QueryProvider>
    </React.StrictMode>
  </BrowserRouter>,
  document.getElementById('root')
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
