@tailwind base;
@tailwind components;
@tailwind utilities;

@import './styles/variables';

// #webpack-dev-server-client-overlay {
//   display: none;
// }

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

body {
  min-height: 200vh;
  font-family: Nunito, 'Nunito Sans', sans-serif;
  margin: 0;
  font-size: 16px;
  color: charcoal;
  background-color: #f6f8fa;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
}

* {
  animation-duration: 0.25s;
  animation-delay: 0.5s;
  animation-direction: normal;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  transition: height 0.5s, margin 0.5s, padding 0.5s, border-width 0.5s, filter 0.5s, opacity 0.5s, color 0.25s,
    background 0.25s, background-color 0.25s, border-color 0.25s;
  box-sizing: border-box;
}

.Toastify {
  font-size: 14px;
}

@media all and (max-height: 730px) and (min-width: 800px) {
  #root {
    transform: scale(0.8);
    transform-origin: 0 0 0;
    width: 125%;
    height: 125%;
  }
}

@media all and (min-width: 641px) {

  body,
  .Header {
    min-width: 1200px;
  }
}

.rdp-dropdown_month div {
  display: none !important;
}

.rdp-dropdown_year div {
  display: none !important;
}

.rdp-vhidden {
  display: none !important;
}

.rdp-caption_dropdowns {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: initial;
  vertical-align: initial;
}