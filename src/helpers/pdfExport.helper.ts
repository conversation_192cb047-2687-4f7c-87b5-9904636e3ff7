import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

export interface PageConfig {
    path: string;
    enabled: boolean;
    alias: string;
    waitTime?: number; // Extra wait time for pages that need more time to render
    selector?: string; // Custom selector for specific pages
}

/**
 * Waits for page loading to complete
 */
export const waitForLoading = async (maxAttempts = 20, interval = 500): Promise<boolean> => {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        // Check if the loading element exists on the page
        const loadingElement = document.getElementById('loader');
        if (!loadingElement) {
            // Give extra time for components to render after loading is complete
            await new Promise(resolve => setTimeout(resolve, 1000));
            return true;
        }
        // Wait for the specified interval before checking again
        await new Promise(resolve => setTimeout(resolve, interval));
    }

    console.warn('Timed out waiting for loading to complete');
    return false;
};

/**
 * Captures the current page to a canvas element
 */
export const capturePageToCanvas = async (selector?: string, extraWaitTime = 0): Promise<HTMLCanvasElement> => {
    return new Promise(async (resolve, reject) => {
        try {
            // Wait for loading to complete
            await waitForLoading();

            // Additional wait time for specific pages if needed
            if (extraWaitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, extraWaitTime));
            }

            // Prepare images for export
            document.querySelectorAll("img").forEach((img) => {
                img.setAttribute("crossOrigin", "anonymous");
            });

            // Wait an additional second for any final rendering
            setTimeout(() => {
                // Get the main content area to capture based on the selector or fallbacks
                const mainContent = selector
                    ? document.querySelector(selector)
                    : document.body;

                console.log(mainContent)

                if (!mainContent) {
                    reject(new Error('Content element not found'));
                    return;
                }

                // Ensure all inner content is fully loaded and measurable
                // This helps with nested scrollable containers
                const allScrollContainers = mainContent.querySelectorAll('*');
                allScrollContainers.forEach(el => {
                    if (el instanceof HTMLElement &&
                        (getComputedStyle(el).overflow === 'auto' ||
                            getComputedStyle(el).overflow === 'scroll' ||
                            getComputedStyle(el).overflowY === 'auto' ||
                            getComputedStyle(el).overflowY === 'scroll')) {
                        // Force the scrollable element to expand to its full height
                        el.style.maxHeight = 'none';
                        el.style.overflow = 'visible';
                    }
                });

                // Get the full scrollable height of the content
                const contentHeight = Math.max(
                    mainContent.scrollHeight,
                    (mainContent as HTMLElement).offsetHeight,
                    mainContent.clientHeight
                );

                const contentWidth = mainContent.clientWidth;

                console.log(`Capturing ${selector || '#app'} with dimensions: ${contentWidth}x${contentHeight}`);

                html2canvas(mainContent as HTMLElement, {
                    useCORS: true,
                    allowTaint: true,
                    scale: 1,
                    logging: false,
                    width: contentWidth,
                    height: contentHeight,
                    imageTimeout: 0,
                    backgroundColor: '#FFFFFF',
                    // This ensures we capture the full height
                    windowHeight: contentHeight,
                    // Improve capturing of all elements
                    onclone: (document, clonedDoc) => {
                        // Make sure all elements in the clone are visible
                        const clonedContent = selector
                            ? clonedDoc.querySelector(selector)
                            : (clonedDoc as unknown as Document).body;

                        if (clonedContent) {
                            const elements = clonedContent.querySelectorAll('*');
                            elements.forEach((el: Element) => {
                                if (el instanceof HTMLElement) {
                                    // Make all containers visible
                                    el.style.overflow = 'visible';
                                    el.style.maxHeight = 'none';
                                }
                            });
                        }
                    }
                })
                    .then(canvas => {
                        // Calculate cropping dimensions
                        const maxWidth = 1400;
                        const maxHeight = 1700;
                        const originalWidth = canvas.width;
                        const originalHeight = canvas.height;

                        let cropWidth = Math.min(originalWidth, maxWidth);
                        let cropHeight = Math.min(originalHeight, maxHeight);

                        const cropX = Math.max((originalWidth - cropWidth) / 2, 0);
                        const cropY = 0; // Top-center pivot

                        // Create a new canvas with the cropped dimensions
                        const croppedCanvas = document.createElement('canvas');
                        croppedCanvas.width = cropWidth;
                        croppedCanvas.height = cropHeight;
                        const ctx = croppedCanvas.getContext('2d');

                        if (ctx) {
                            // Draw the cropped portion of the original canvas onto the new canvas
                            ctx.drawImage(
                                canvas,
                                cropX, cropY, cropWidth, cropHeight, // Source
                                0, 0, cropWidth, cropHeight             // Destination
                            );

                            resolve(croppedCanvas);
                        } else {
                            reject(new Error('Could not get 2D context for cropped canvas'));
                        }
                    })
                    .catch(error => reject(error));
            }, 1000);
        } catch (error) {
            reject(error);
        }
    });
};

/**
 * Adds a page capture to the PDF document
 */
export const addPageToPdf = async (
    pdf: jsPDF,
    canvas: HTMLCanvasElement,
    alias: string,
    needsNewPage: boolean
): Promise<void> => {
    // Use the actual page dimensions
    const pageWidth = canvas.width;
    const pageHeight = canvas.height;
    
    // For first page, set the dimensions directly instead of adding a page
    if (needsNewPage) {
        pdf.addPage([pageWidth * 0.264583, pageHeight * 0.264583], 'p'); // 0.264583 is the conversion factor from px to mm
    } else {
        // For the first page, ensure it has the correct dimensions
        pdf.deletePage(1); // Remove the default first page
        pdf.addPage([pageWidth * 0.264583, pageHeight * 0.264583], 'p'); // Add with correct dimensions
        pdf.setPage(1); // Return to the first page
    }

    // Add the image at full size with no margins
    const imgData = canvas.toDataURL("image/png");
    pdf.addImage(
        imgData,
        'PNG',
        0, 0, // x, y - no margins
        pageWidth * 0.264583, // width in mm
        pageHeight * 0.264583, // height in mm
        `page-${alias}`,
        "NONE"
    );
};

/**
 * Creates a new PDF document with default settings
 */
export const createPdf = (width?: number, height?: number): jsPDF => {
    // Create with standard dimensions initially - we'll replace the first page
    const pdf = new jsPDF({
        orientation: 'p',
        unit: 'mm',
        format: 'a4',
        putOnlyUsedFonts: true,
    });
    
    return pdf;
};