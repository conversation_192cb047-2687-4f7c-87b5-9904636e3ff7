@import 'styles/variables';

.DraftItem {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    padding: 10px 10px 20px 10px;
    border-bottom: 1px solid $grey;
    font-size: 20px;

    .SentBy {
        margin-bottom: 10px;
    }

    .DraftWrapper {
        display: flex;

        .thumbnail {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
            width: 100%;
            height: 250px;
            max-width: 400px;
            background: black;
            border-radius: 10px;
            color: $grey;
            font-size: 65px;
            text-align: center;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;

            video {
                max-width: 100%;
                max-height: 100%;
                border-radius: 10px;
            }

            button{
                position: absolute;
                width: 100%;
                height: 100%;
                color: transparent;
                background-color: transparent;
                border: none;
                border-radius: 10px;

                &:hover {
                    color: white;
                    background-color: black;
                    opacity: 0.5;
                }

                svg {
                    width: 30%;
                    height: auto;
                }
            }
        }

        .meta {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            align-content: flex-start;
            flex-wrap: wrap;
            width: calc(100% - 300px - 20px);
            width: 100%;
        }

        .user {
            display: flex;
            align-items: center;
            margin-right: 10px;

            .name {
                margin-left: 10px;
                color: $dark-grey;
                font-size: 16px;
            }
        }

        .buttonBar {
            position: relative;

            button {
                font-size: 18px;
                margin-left: 10px;

                &:first-child {
                    margin-left: 0;
                }
            }

            .dropdownBar {
                padding-top: 10px;
                position: absolute;
                right: 0;
                top: 50px;
                z-index: 100;
                border-radius: 10px;
                background: white;
                box-shadow: 5px 5px 20px 5px $transparent-black-low;
                transition: opacity 5s, height 5s;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                button {
                    margin-bottom: 10px;
                    margin-left: 10px;
                    margin-right: 10px;
                    font-size: 16px;
                }
            }
        }


        .question {
            display: flex;
            flex-wrap: wrap;
            width: 100%;

            .questionText {
                width: 100%;
                padding-top: 15px;
                font-size: 24px;
                font-weight: 300;
            }

            .attributes {
                display: flex;
                padding-top: 15px;
                font-size: 16px;
                color: $dark-grey;
            }
        }

        &:last-child {
            border: none;
            padding: 0;
        }
    }
}

.SentByHighlight {
    background-color: $pale-mint-green;
    border-radius: 10px;
}

@media (max-width : 640px) {
    .DraftItem {
        .DraftWrapper {
            flex-wrap: wrap;

            .thumbnail {
                margin: auto;
                width: 100%;
            }

            .meta {
                margin-top: 10px;
            }

            .buttonBar {
                margin-top: 10px;
            }

        }

    }
}