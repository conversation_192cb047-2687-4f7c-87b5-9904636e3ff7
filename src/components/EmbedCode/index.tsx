import { ClientInterface } from '../../interfaces/client.interfaces';
import { useEffect } from 'react';

interface EmbedCodeProps {
  client: ClientInterface;
}

export function EmbedCode({ client }: EmbedCodeProps) {
  useEffect(() => {
    // Only create embed for Arlington (client ID 211)
    if (client.id !== '211') return;

    const iframe = document.createElement('iframe');
    iframe.id = 'repd-embed';
    
    // Get the current page URL
    const referral = window.location.href;
    
    // Construct embed URL using client name
    const embedUrl = new URL(`https://embed.repd.us/${client.name.toLowerCase()}`);
    embedUrl.searchParams.append('referrer', encodeURIComponent(referral));
    
    iframe.src = embedUrl.toString();
    iframe.style.cssText = `
      position: fixed;
      bottom: 0rem;
      right: 0rem;
      border: none;
      z-index: 50;
      transition: all 0.3s ease;
      background: transparent;
      width: 200px;
      height: 200px;
      max-height: calc(100vh - 40px);
      max-width: 98vw;
      border-radius: 10px;
    `;

    // Handle messages from iframe for resize events
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== 'https://embed.repd.us') return;
      
      const { type, dimensions } = event.data;
      const initialHeight = dimensions.height === '200px';

      if (type === 'resize') {
        iframe.style.width = dimensions.width;
        iframe.style.height = initialHeight ? '200px' : parseInt(dimensions.height) + 400 + 'px';
      }
    };

    // Ensure smooth transition when height changes
    requestAnimationFrame(() => {
      iframe.style.transition = 'all 0.3s ease';
    });

    window.addEventListener('message', handleMessage);
    document.body.appendChild(iframe);

    // Cleanup function
    return () => {
      window.removeEventListener('message', handleMessage);
      iframe.remove();
    };
  }, [client.id, client.name]); // Dependencies array

  return null; // No need to render anything as we're directly manipulating the DOM
}
