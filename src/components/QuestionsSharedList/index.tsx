import { useState } from "react";

import * as interfaces from "interfaces";

import QuestionsSharedItem from "components/QuestionsItem/QuestionsSharedItem";

import {
  QuestionService,
  AnswersService,
  FileService,
  UserService,
  ClientService
} from "services";
import { useServiceContext } from "services/ServiceProvider";

import pageClasses from "styles/PageWrapper.module.scss";
import classes from "./QuestionsList.module.scss";
import { cn } from "utils/utils";
import Button from "shared/Button";

export default function QuestionsSharedList(props: {
  service?: QuestionService;
  answersService?: AnswersService;
  fileService?: FileService;
  userService?: UserService;
  clientService?: ClientService,
  client: interfaces.ClientInterface;
  user?: interfaces.UserInterface;
}) {
  const { service, fileService, answersService, userService, clientService, client, user } =
    props;
  const { adminStatsService } = useServiceContext();
  const [questions, setQuestions]: interfaces.QuestionInterface[] | any =
    useState([]);
  const [sortBy, setSortBy] = useState<"dueDate" | "invited">("dueDate");

  const fetchQuestionList = () => {
    service?.getQuestions(setQuestions);
  };

  if (service && questions.length === 0) fetchQuestionList();

  const QuestionsTemplate = () => {
    const questionsSortComparator = (
      a: interfaces.QuestionInterface,
      b: interfaces.QuestionInterface
    ) => {
      const getLastShareDate = (question: interfaces.QuestionInterface, sortKey: "dueDate" | "createdAt") => {
        if (question.shares && question.shares.length > 0) {
          return new Date(question.shares[question.shares.length - 1].dueDate).getTime();
        }
        return 0;
      };

      if (sortBy === "dueDate") {
        const dateA = getLastShareDate(a, "dueDate");
        const dateB = getLastShareDate(b, "dueDate");
        if (!dateA) return 1;
        if (!dateB) return -1;
        return dateA - dateB;
      } else {
        return getLastShareDate(b, "createdAt") - getLastShareDate(a, "createdAt");
      }
    };

    const filterCriteria = (question: interfaces.QuestionInterface): boolean => {
      const canShowQuestion = question.isShared;
      return canShowQuestion;
    }

    const filteredQuestions = questions
      .filter(filterCriteria)
      .sort(questionsSortComparator);

    if (filteredQuestions.length === 0) {
      return <div className={pageClasses.NoQuestions}>🎉 All done, no questions to show! Please check back in later.</div>;
    } else {
      return (
        <div className={pageClasses.limitHeight}>
          {filteredQuestions
            .map((question: interfaces.QuestionInterface) => (
              <QuestionsSharedItem
                key={question.id}
                question={question}
                fileService={fileService}
                answersService={answersService}
                userService={userService}
                clientService={clientService}
                client={client}
                service={service}
                refetchQuestionList={fetchQuestionList}
              />
            ))}
        </div>
      );
    }
  };

  return (
    <div className={classes.QuestionsList}>
      <div className={pageClasses.TitleWithTabs}>
        <div className={pageClasses.Tabs}>
          <div
            className={cn(
              pageClasses.Tab, pageClasses.active
            )}
          >
            Shared Questions
          </div>
        </div>
        <div className={pageClasses.Buttons}>
          <Button
            text={"Sort by" + (sortBy === "dueDate" ? " Due Date" : " Invite Date")}
            callback={() => {
              const newSortBy = sortBy === "dueDate" ? "invited" : "dueDate";
              setSortBy(newSortBy);
              adminStatsService?.trackEvent('QuestionsShared', 'sort_by_' + (newSortBy === "dueDate" ? "due_date" : "invite_date"));
            }}
          />
        </div>
      </div>

      {questions.length > 0 && <QuestionsTemplate />}
    </div>
  );
}
