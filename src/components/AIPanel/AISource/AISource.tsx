import classes from "./AISource.module.scss";
import { cn } from "utils/utils";
import Button from "shared/Button";
import { useServiceContext } from "services/ServiceProvider";

// Default values for props
const DEFAULTS = {
    PROGRESS: 66,
    SOURCE_NAME: "Emporia Main",
    SOURCE_URL: "https://www.emporiaks.gov",
    LAST_SCRAPED: "Mon 17th April, 2025"
};

interface AISourceProps {
    id?: number;
    progress?: number;
    sourceName?: string;
    sourceUrl?: string;
    lastScraped?: string;
    onEditClick?: (sourceId: number) => void;
}

export default function AISource(props: AISourceProps) {
    const { adminStatsService } = useServiceContext();

    // Use provided values or fallback to defaults
    const progress = props.progress !== undefined ? props.progress : DEFAULTS.PROGRESS;
    const sourceName = props.sourceName || DEFAULTS.SOURCE_NAME;
    const sourceUrl = props.sourceUrl || DEFAULTS.SOURCE_URL;
    const lastScraped = props.lastScraped || DEFAULTS.LAST_SCRAPED;

    // Ensure progress is within valid range (0-100)
    const validProgress = Math.min(100, Math.max(0, progress));

    const handleEditClick = () => {
        adminStatsService?.trackEvent('AIPanel', 'edit_source');
        if (props.onEditClick && props.id !== undefined) {
            props.onEditClick(props.id);
        }
    };

    return (
        <div className={cn(classes.AISource)}>
            <div className={classes.sourceHeader}>
                <h3 className={classes.sourceName}>{sourceName}</h3>
                <div className={classes.sourceProgress}>
                    <div className={classes.progressBar}>
                        <div
                            className={classes.progress}
                            style={{ width: `${validProgress}%` }}
                        ></div>
                    </div>
                    <div className={classes.progressPercentage}>
                        {validProgress}%
                    </div>
                </div>
            </div>

            <div className={classes.sourceLink}>
                <a href={sourceUrl}>{sourceUrl}</a>
            </div>

            <div className={classes.sourceFooter}>
                <p className={classes.lastScraped}>Last Scraped: {lastScraped}</p>
                <Button text="Edit" callback={handleEditClick} />
            </div>
        </div>
    );
}
