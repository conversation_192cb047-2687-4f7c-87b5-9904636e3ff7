@import 'styles/variables';

.AISource {
    border: 1px solid $grey;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;

    .sourceHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .sourceName {
            font-size: 20px;
            font-weight: 600;
        }

        .sourceProgress {
            display: flex;
            align-items: center;

            .progressBar {
                width: 300px;
                height: 10px;
                border-radius: 5px;
                background-color: $offwhite;
                margin-right: 10px;

                .progress {
                    height: 100%;
                    width: 0%;
                    /* Width will be set dynamically via inline style */
                    border-radius: 5px;
                    background-color: $mint-green;
                }
            }

            .progressPercentage {
                font-size: 14px;
            }
        }
    }

    .sourceLink {
        font-size: 16px;
        color: $blue;
        margin-bottom: 15px;
    }

    .sourceFooter {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .lastScraped {
            font-size: 14px;
            color: $dark-grey;
        }
    }

}