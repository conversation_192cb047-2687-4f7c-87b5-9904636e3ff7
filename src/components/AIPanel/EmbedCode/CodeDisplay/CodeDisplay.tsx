import classes from "./CodeDisplay.module.scss";
import { cn } from "utils/utils";

interface CodeDisplayProps {
  code: string;
  language?: string;
  className?: string;
}

export default function CodeDisplay({ code, language = "html", className }: CodeDisplayProps) {

  return (
    <div className={cn(classes.CodeDisplay, className)}>
      <div className={classes.header}>
        <span className={classes.language}>{language.toUpperCase()}</span>
      </div>
      <pre className={classes.codeBlock}>
        <code>{code}</code>
      </pre>
    </div>
  );
}
