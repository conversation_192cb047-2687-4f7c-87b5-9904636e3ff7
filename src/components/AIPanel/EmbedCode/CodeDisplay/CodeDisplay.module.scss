@import 'styles/variables';

.CodeDisplay {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid $grey;
  margin-top: 1rem;
  background-color: $offwhite;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: $offwhite;
    border-bottom: 1px solid $grey;

    .language {
      font-size: 12px;
      font-weight: 600;
      color: $dark-grey;
    }

    .copyButton {
      display: flex;
      align-items: center;
      gap: 6px;
      background: none;
      border: none;
      color: $dark-blue;
      font-size: 14px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: $pale-blue;
      }

      svg {
        font-size: 14px;
      }
    }
  }

  .codeBlock {
    margin: 0;
    padding: 16px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    color: $charcoal;
    background-color: #f8f9fa;
    border-radius: 0 0 6px 6px;
    user-select: text;
  }
}