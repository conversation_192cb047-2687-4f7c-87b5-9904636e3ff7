import classes from "./EmbedCode.module.scss";
import { cn } from "utils/utils";
import CodeDisplay from "components/AIPanel/EmbedCode/CodeDisplay/CodeDisplay";
import Button from "shared/Button";
import { useState } from "react";

export default function EmbedCode(props: {
    code?: string;
}) {
    const { code = `<script src="https://embed.repd.us/widget.js" id="repd-widget" data-client="client-name"></script>` } = props;

    const [copied, setCopied] = useState(false);

    const handleCopy = () => {
        navigator.clipboard.writeText(code).then(() => {
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        });
    };

    return (
        <div className={cn(classes.EmbedCode)}>
            <div className={classes.header}>
                <h2>Your Embed Code</h2>
                <Button text={copied ? "Copied!" : "Copy"} callback={handleCopy} />
            </div>

            <p>To embed your HTML code, please copy the below into your HEAD, BODY or FOOTER tags of the website.</p>

            <CodeDisplay code={code} language="html" />
        </div>
    );
}
