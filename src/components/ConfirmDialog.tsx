import React from "react";
import Modal from "../shared/Modal";

type Props = {
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
};
export default function ConfirmDialog({
  title,
  message,
  onConfirm,
  onCancel,
}: Props) {
  return (
    <Modal>
      <div className="bg-white rounded-lg overflow-hidden transform transition-all sm:max-w-lg sm:w-full">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            {title}
          </h3>
          <div className="mt-2">
            <p className="text-sm text-gray-500">{message}</p>
          </div>
        </div>
        <div className="bg-gray-100 py-3 px-6 flex justify-center text-center">
          <button
            onClick={onConfirm}
            className=" justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 "
          >
            Confirm
          </button>
          <button
            onClick={onCancel}
            className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </Modal>
  );
}
