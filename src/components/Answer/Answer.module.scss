@import 'styles/variables';

.answerQuestion {
  width: 100%;
  font-size: 16px;

  .title {
    text-align: center;
    font-weight: 400;
  }

  .subTitle {
    color: $dark-blue;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 20px;
  }

  .question {
    padding-bottom: 5px;
    margin: 20px 0 10px 0;
    width: 100%;
    font-size: 16px;
    border-bottom: 1px solid #d8d8d8;
    color: #999;
    font-weight: 300;
  }

  .or {
    margin-top: 30px;
    color: $dark-blue;
    font-weight: 400;
    text-align: center;
  }

  .buttonWrapper {
    display: flex;
    justify-content: center;
    margin-top: 25px;

    button {

      &:first-child{
        margin-right: 10px;
      }

      color: $blue;
      padding: 7px 35px;

      &:hover {
        background-color: $pale-blue;
      }
    }
  }
}
