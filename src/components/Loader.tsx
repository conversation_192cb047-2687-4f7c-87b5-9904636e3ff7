import React from 'react';
import classes from './Loader.module.scss';
import { cn } from 'utils/utils';

type LoaderProps = {
  title?: string;
  customId: string;
  absolute?: boolean;
}

const Loader = ({ title = "Loading your data...", customId, absolute }: LoaderProps) => {
  return (
    <div className={cn(classes.loaderContainer, absolute && classes.absolute)} data-html2canvas-ignore="true" id={customId}>
      {title}
      <div className={classes.spinner}></div>
    </div>
  );
};

export default Loader;
