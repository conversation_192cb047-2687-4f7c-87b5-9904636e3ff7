import React, { useState } from "react";

import * as interfaces from "interfaces";
import { AnswersService, NgpVanService, FileService, ClientService } from "services";

import AnswerItem from "../AnswerItem";
import DraftItem from "../DraftItem";

export default function AnswersList(props: {
  answersService: AnswersService;
  ngpVanService: NgpVanService;
  clientService?: ClientService,
  fileService: FileService;
  user?: interfaces.UserInterface;
  isDrafts: boolean;
}) {
  const { answersService, ngpVanService, fileService, clientService, user, isDrafts } = props;
  const [answers, setAnswers]: interfaces.AnswerInterface[] | any = useState(
    []
  );
  const [openedDropdownId, setOpenedDropdownId] = useState(null);

  if (answersService && answers.length === 0)
    answersService.getAnswers(setAnswers);

  return !isDrafts ? (
    <>
      {answers.length > 0 &&
        answers
          .filter((answer: interfaces.AnswerInterface) => !answer.isDraft)
          .map((answer: interfaces.AnswerInterface) => (
            <AnswerItem
              key={answer.id}
              answer={answer}
              setAnswers={setAnswers}
              answersService={answersService}
              dropdownOpened={openedDropdownId === answer.id}
              setOpenedDropdownId={setOpenedDropdownId}
              ngpVanService={ngpVanService}
              fileService={fileService}
              clientService={clientService}
              user={user}
            />
          ))}
    </>
  ) : (
    <>
      {answers.length > 0 &&
        answers
          .filter((answer: interfaces.AnswerInterface) => answer.isDraft)
          .map((answer: interfaces.AnswerInterface) => (
            <DraftItem
              key={answer.id}
              answer={answer}
              setAnswers={setAnswers}
              dropdownOpened={openedDropdownId === answer.id}
              setOpenedDropdownId={setOpenedDropdownId}
              answersService={answersService}
              fileService={fileService}
              ngpVanService={ngpVanService}
              clientService={clientService}
              user={user}
            />
          ))}
    </>
  );
}
