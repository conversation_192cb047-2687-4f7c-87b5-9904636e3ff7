import { useEffect, useState } from "react";

import classes from "./ProgressBar.module.scss";

interface ProgressBarProps {
  uploadingProgress: number;
  calculatedConversionETA: number;
}

export default function ProgressBar({
  uploadingProgress,
  calculatedConversionETA,
}: ProgressBarProps) {
  const [startTimestamp, setStartTimestamp] = useState<number>(0);
  const [timeRemaining, setTimeRemaining] = useState<string>("");

  useEffect(() => {
    setStartTimestamp(new Date().getTime());
  }, []);

  useEffect(() => {
    if (startTimestamp) {
      const timestamp = new Date().getTime();
      setTimeRemaining(
        new Date(
          (100 - uploadingProgress) /
            (uploadingProgress / (timestamp - startTimestamp))
        )
          .toISOString()
          .substring(14, 19)
      );
    }
  }, [uploadingProgress]);

  return (
    <div className={classes.progressWrapper}>
      <progress max="100" value={uploadingProgress} />
      <div className={classes.progressMessage}>
        <span>{uploadingProgress < 100 ? "Uploading..." : "Uploaded"}</span>
        {uploadingProgress < 100 && (
          <span>{`${Math.round(
            uploadingProgress
          )}% - Time Left: ${timeRemaining}`}</span>
        )}
      </div>
    </div>
  );
}
