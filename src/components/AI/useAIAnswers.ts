import { useEffect, useState } from "react";
import axios from "axios";
import { TrackerServiceInstance } from "services";
import { useClientContext } from "../../hooks";
import { MarvinResponseItem } from "./types";

import { OtherServiceInstance, createOtherServiceInstance } from "services";

export type AIAnswers = {
  classification: "Simple" | "Complex";
  marvinAnswers: (string | [string, MarvinResponseItem[]])[];
  matchingAnswers: any[];
};

createOtherServiceInstance();

export default function useAIAnswers(question?: string) {
  const { client } = useClientContext();
  const [answers, setAnswers] = useState<AIAnswers | null | "busy">(null);

  const isRude = question ? !!OtherServiceInstance?.isRude(question) : false;

  const host = window.location.hostname;
  const isStaging = host === 'localhost' || host === 'staging.repd.us';

  const url = `https://api${isStaging ? '-staging' : ''}.repd.us/api/v1.0.0/ai-service`;

  useEffect(() => {
    if (question) {
      setAnswers("busy");

      try {
        axios
          .post(url, {
            question,
            clientId: client.id,
          })
          .then((response) => {
            const { classification, marvinAnswers } =
              response.data;

            TrackerServiceInstance?.trackEvent("repd_morpheus_ai", {
              event_category: 'search_query',
              aiSearchText: question,
              aiResponse: response.data,
            });

            // console.log(
            //   'useAIAnswers -> response.data',
            //   {
            //     marvinAnswers,
            //     classification,
            //     matchingAnswers,
            //   }
            // );

            setAnswers({
              classification,
              marvinAnswers: marvinAnswers?.answer?.answer ? 
                [marvinAnswers.answer.answer, marvinAnswers.answer.sources || []] : 
                [],
              matchingAnswers: marvinAnswers?.answer?.matchingAnswers || [],
            });
          });
      } catch (error) {
        console.log('useAIAnswers -> Error', error);
        setAnswers(null);
      }
    }
  }, [question]);

  return { answers, setAnswers, isRude };
}
