@import 'styles/variables';

.newLoadingBar {
  width: calc(100% - 32px);
  margin-left: 16px;
  height: 8px;
  // background-color: var(--progress-bar-background, rgba(0, 0, 0, 0.2));
  border-radius: 0.5rem;
  margin-top: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.newProgressBar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  // background-color: white;
  border-radius: 0.5rem;
  animation: progressAnimation 8s linear forwards;
  z-index: 1;
  /* box-shadow: 0 0 10px var(--progress-bar-color, rgba(233, 78, 119, 0.5)); */
}

.gradientAnimation {
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.5) 50%,
      rgba(255, 255, 255, 0) 100%);
  animation: gradientSlide 1.5s infinite linear;
  z-index: 2;
}

@keyframes progressAnimation {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

@keyframes gradientSlide {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(200%);
  }
}

/* Add a pulsing effect to the loading bar */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

.newProgressBar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
  animation: pulse 2s infinite;
}

.QuestionInput {
  margin-top: -120px;
  padding: 15px;
  background-color: white;
  border-width: 1px;
  border-radius: 20px;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  box-shadow: 0 0px 20px 0px #0057FF1A;

  &.modal {
    margin-top: 0;
    padding-left: 10px;
  }
}

.QuestionInputModal {
  flex-direction: row;
  gap: 6px;
}

.QuestionInputWrapper {
  text-align: center;
  padding: 25px 0 15px 0;
  border-radius: 10px;
  margin-bottom: 0px;

  .inputRow {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
}

.placeholderText {
  font-size: 16px;
  color: #445472;
  font-weight: 500;
}

.recentAnswersWrapper {
  margin-top: 10px;
  position: relative;
  width: 100%;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 40px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
    pointer-events: none;
    z-index: 1;
  }
}

.recentAnswersContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  overflow-x: auto;
  position: relative;
  white-space: nowrap;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */

  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

.recentAnswersLabel {
  font-size: 14px;
  color: #445472;
  font-weight: 400;
}

.recentAnswer {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 15px 10px 15px;
  gap: 4px;
  cursor: pointer;
  border: 1px solid #C7D7E4;
  border-radius: 20px;
}

.recentAnswer:hover {
  background-color: #F2F6FF;
}

.answerIcon {
  width: 18px;
  height: 18px;
  filter: brightness(0) saturate(100%);
}

.answerLabel {
  font-size: 14px;
  color: #445472;
  font-weight: 500;
}

.searchIcon {
  margin-top: 2px;
  width: 18px;
  height: 18px;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.arrowIconContainer {
  background-color: #2c4475;
  border-radius: 20px;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  overflow: hidden;
}

.arrowIcon {
  width: 20px;
  height: 20px;
}

.subtext {
  color: rgb(156 163 175 / var(--tw-text-opacity));
  font-size: 0.75rem;
  line-height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 0px;
  color: grey;

  div {
    display: flex;
    align-items: center;
  }
}

.AiMainContent {
  max-width: 700px;
  height: 100%;
  overflow: scroll;
}

.AiContentWrapper {
  height: calc(90vh - 30px);
  overflow: hidden;
  max-width: 950px;
  width: 100vw;
  gap: 2rem;
  justify-content: space-between;
  flex-direction: row;
  display: flex;
}

.ExampleQuestions {
  border-radius: 5px;
  background-color: #2c44751f;
  margin-top: 30px;
  padding: 2rem;
  border-width: 1px;
  overflow-y: auto;

  h2 {
    font-weight: bold;
    color: #2c4475;
  }

  .questionContainer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 20px;

    button.exampleQuestion {
      margin: 2px 0;
      padding: 5px 15px;
      border-radius: 40px;
      background-color: white;
      font-size: 16px;
      color: #34538D;
      border: 1px solid #D8D8D8;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

.playIcon {
  background: rgba(0, 0, 0, 0.2);
  width: 100px;
  padding: 10px;
  border-radius: 80px;
}

.AiAnswer {
  // height: calc(100% - 145px);
  overflow: visible scroll;

  a {
    color: #0070f3;
    text-decoration: underline;

    &:hover {
      color: #0070f3;
    }
  }

  .thumbnail {
    aspect-ratio: 16 / 9;
    position: relative;
    overflow: hidden;
    width: 100%;

    img {
      background-color: rgb(0 0 0 / 0.25);
      justify-content: center;
      align-items: center;
      display: flex;
      position: absolute;
      top: 0px;
      right: 0px;
      bottom: 0px;
      left: 0px;
      cursor: pointer;
    }
  }
}

.aiResponseExtension {
  position: relative;
  top: -1px;
  border-top: 1px dashed #e1e1e1;
  background: white;
  word-break: break-word;

  &.stacked1 {
    top: -2px;
  }
}

.SeparateSection {
  margin-top: 20px;
  border-radius: 10px;
  padding: 30px;
  background: #f2f6ff;
  border: 1px solid #dbe6ff;

  h2 {
    color: #3b5ca2;
  }

  .source {
    background: white;
    border-radius: 5px;
    padding: 10px;
    color: grey;
    margin: 10px 0;
    word-break: break-word;
  }
}

.SuggestedQuestions {
  height: 100%;
  width: 13rem;
  display: inline-block;
  vertical-align: baseline;
  overflow: auto;

  .imageWrapper {
    position: relative;

    img,
    .blur {
      position: relative;
      z-index: 2;
      width: 100%;
      border-radius: 5px;
      margin-bottom: 10px;
      cursor: pointer;
      aspect-ratio: 16 / 9;
      height: auto;
      object-fit: contain;
      background-color: #00254570;
    }

    .blur {
      z-index: 1;
      top: 0;
      position: absolute;
      filter: blur(2px);
      border: 1px solid white;
    }
  }
}

@keyframes shuffle {
  0% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-10px);
  }

  50% {
    transform: translateX(10px);
  }

  75% {
    transform: translateX(-10px);
  }

  100% {
    transform: translateX(0);
  }
}

.shuffleAnimation {
  animation: shuffle 1.5s ease-out;
}

@media screen and (max-width: 640px) {
  .MobileSearchHeader {
    position: relative;
    top: -1rem;
    left: -1rem;
    width: 100vw;
    background: rgb(50, 50, 98);
    color: white;
    padding: 60px 20px 20px 20px;
    margin: 0 0 10px 0;
    font-size: 1rem;
  }

  .recentAnswersContainer {
    margin-top: 20px;

    .recentAnswer {
      padding: 10px 30px 10px 15px;
    }
  }

  .AiContentWrapper {
    flex-direction: column;
    max-width: calc(100%);
    gap: 0;
  }

  .QuestionInputWrapper {
    // Wrapper only applies to mobile view on the client landing page, not in the pop up.
    padding: 20px 10px;
    margin-top: 0;
    margin-bottom: 10px;
    text-align: center;

    .QuestionInput {
      margin: 0;
    }
  }

  .SuggestedQuestions {
    width: 100%;
  }
}