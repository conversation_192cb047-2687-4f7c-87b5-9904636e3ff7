.homelessness {}

.label {
  width: 180px;
  position: relative;
}

.wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  color: var(--color-slategray);
}

.facesIcon {
  height: 24px;
  width: 24px;
  overflow: hidden;
  flex-shrink: 0;
}

.b,
.facesIcon {
  position: relative;
}

.facesParent {
  width: 80px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-3xs);
}

.arrowsIcon {
  height: 24px;
  width: 24px;
  overflow: hidden;
  flex-shrink: 0;
}

.arrowsIcon,
.b1 {
  position: relative;
}

.hidden {
  opacity: 0;
}

.arrowsParent {
  justify-content: space-evenly;
  max-width: 50px;
  margin-left: 10px;
  gap: 5px;
  flex: 1;
  width: 100%;
}

.arrowsParent,
.frameParent,
.root {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.frameParent {
  justify-content: flex-start;
  gap: var(--gap-xl);
}

.root {
  border-radius: var(--br-7xs);
  background-color: var(--color-white);
  overflow: hidden;
  cursor: pointer;
  width: 100%;
  justify-content: space-between;
  padding: var(--padding-mini) var(--padding-xl);
  text-align: right;
  font-size: var(--font-size-xl);
  color: var(--color-black);
  transition: none !important;
  //font-family: var(--font-plus-jakarta-sans);
}

.selected {
  border: 1px solid var(--color-gainsboro-200);
  color: var(--color-crimson);
}

.root:hover {
  color: var(--color-crimson);
}

.mobileContainer {
  padding: 10px;
  border: 1px solid var(--color-gainsboro-200);
  width: 100%;
  border-radius: 6px;
}

.showOnMobile {
  display: none;
}

.categoryDetails {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mobileTitle {
  font-size: 24px;
  font-weight: 700;
  color: #e73067;
  padding-bottom: 10px;
}

.categoryDetailsLabel {
  font-size: 12px;
  font-weight: 700;
  color: #445472;
}

.categoryDetailsValue {
  font-size: 14px;
  font-weight: 700;
}

.mobileTitle {
  width: 100%;
  display: block;
}

.mobileContainer {
  display: flex;
  flex-direction: column;
}

.showOnMobile {
  display: none;
}

.hidden {
  display: none;
}

@media (max-width: 640px) {
  .hideOnMobile {
    display: none;
  }

  .showOnMobile {
    display: flex;
  }

  .showOnMobile {
    display: flex;
  }

  .label {
    width: 110px;
  }
}