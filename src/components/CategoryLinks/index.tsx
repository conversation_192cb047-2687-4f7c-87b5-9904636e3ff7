import { FunctionComponent } from "react";
import styles from "./CategoryLinks.module.scss";
import { cn } from "utils/utils";

export type CategoryLinksType = {
  className?: string;
  categoryName?: string;
  sentiment?: string;
  faces?: string;
  volume: number;
  volumeDiff: number;
  onClick: () => void;
  selected: boolean;
  mobile?: boolean;
};

const CategoryLinks: FunctionComponent<CategoryLinksType> = ({
  className = "",
  categoryName,
  sentiment,
  faces,
  volumeDiff,
  volume,
  onClick,
  selected,
  mobile
}) => {
  return (
    !mobile ? <div
      onClick={onClick}
      className={[styles.root, className, selected && styles.selected].join(" ")}
    >
      <div className={styles.frameParent}>
        <div className={styles.wrapper}>
          <div className={styles.label}>
            <b className={styles.homelessness}>{categoryName || "Uncategorized"}</b>
          </div>
        </div>
        <div className={styles.facesParent}>
          <img className={styles.facesIcon} alt="" src={faces} />
          <b className={styles.b}>{sentiment}</b>
        </div>
        <div className={styles.arrowsParent}>
          <img className={cn(styles.arrowsIcon, volumeDiff <= 0 && styles.hidden)} alt="" src="/analytics/arrows.svg" />
          <b className={styles.b1}>{volume}%</b>
        </div>
      </div>
    </div> : <div className={cn(styles.showOnMobile, styles.mobileContainer)}>
      <h2 className={styles.mobileTitle}>{categoryName || "Uncategorized"}</h2>
      <div className={styles.categoryDetails}>
        <b className={styles.categoryDetailsLabel}>Sentiment</b>
        <img className={styles.facesIcon} alt="" src={faces} />
        <b className={styles.categoryDetailsValue}>{sentiment}</b>
        <b className={styles.categoryDetailsLabel}>Volume</b>
        <img className={cn(styles.arrowsIcon, volumeDiff <= 0 && styles.hidden)} alt="" src="/analytics/arrows.svg" />
        <b className={styles.categoryDetailsValue}>{volume}%</b>
      </div>
    </div>
  );
};

export default CategoryLinks;
