import { useEffect, useRef, useState } from "react";

import { faPhotoVideo } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import VideoSubtitlesPreview from "components/VideoSubtitlesPreview";
import ProgressBar from "../ProgressBar";

import classes from "./UploadVideo.module.scss";
import { FileService } from "../../services";

interface UploadVideoProps {
  file: any;
  setFile: any;
  setImgSrc: any;
  setVideoDuration: (duration: number) => void;
  loading: boolean;
  uploadingPercent: number;
  fileService?: FileService;
}

const CONVERSION_ETA_MS_PER_MB: number = parseInt(
  process.env.CONVERSION_ETA_PER_MB || "1800"
);

export default function UploadVideo({
  file,
  setFile,
  setImgSrc,
  setVideoDuration,
  loading,
  uploadingPercent,
  fileService,
}: UploadVideoProps) {
  const fileInputField: any = useRef(null);

  const [isOver, setIsOver] = useState(false);
  const [videoSrc, setVideoSrc] = useState("");

  const handleUploadBtnClick = () => {
    if (file) fileInputField?.current?.click();
  };

  const dropFile = (event: any) => {
    event.preventDefault();
    event.persist();

    const isVideoFile: boolean =
      event.dataTransfer.files[0]?.name
        .toString()
        .match(/.mp4|.MP4|.mov|.MOV/) !== null;

    if (isVideoFile) setFile(event.dataTransfer.files);

    setIsOver(false);
  };

  useEffect(() => {
    if (file?.length > 0) {
      setVideoSrc(URL.createObjectURL(file[0]));
    }
    return () => {
      URL.revokeObjectURL(videoSrc);
    };
  }, [file]);

  return file?.length > 0 ? (
    <>
      <VideoSubtitlesPreview
        src={videoSrc}
        setImgSrc={setImgSrc}
        fileName={file[0].name}
        loading={loading}
        file={file[0]}
        fileService={fileService}
        setVideoDuration={setVideoDuration}
      />
      {loading && (
        <ProgressBar
          uploadingProgress={uploadingPercent}
          calculatedConversionETA={
            (file[0].size / 1000000) * CONVERSION_ETA_MS_PER_MB
          }
        />
      )}
    </>
  ) : (
    <div
      className={`${classes.uploadVideo} ${
        isOver && classes.uploadVideoHovered
      }`}
      onClick={handleUploadBtnClick}
      onDrop={dropFile}
      onDragOver={(e) => {
        e.preventDefault();
        setIsOver(true);
      }}
      onDragLeave={(e) => {
        e.preventDefault();
        setIsOver(false);
      }}
    >
      <label htmlFor="video">
        <form action="">
          <input
            type="file"
            id="video"
            name="video"
            accept=".mp4,.MP4,.mov,.MOV"
            ref={fileInputField}
            onChange={(e) => setFile(e.target.files)}
          />

          <div className={classes.uploadIconWrapper}>
            <FontAwesomeIcon
              icon={faPhotoVideo}
              className={classes.uploadIcon}
            />
          </div>

          <div className={classes.textSection}>
            <h6>Drag and Drop or</h6>
            <p>Select an MP4 or MOV Video File</p>
          </div>
        </form>
      </label>
    </div>
  );
}
