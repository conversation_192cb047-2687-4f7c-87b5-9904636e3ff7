@import 'src/styles/variables';

@mixin upload-video-hovered {
  background-color: $blue !important;

  form {
    animation-duration: 0;
    color: white !important;

    .textSection {
      h6 {
        animation-duration: 0;
      }

      p {
        color: white !important;
      }
    }
  }
}

.uploadVideoHovered {
  @include upload-video-hovered;
}

.uploadVideo {
  background-color: $pale-blue;
  border-radius: 5px;
  cursor: pointer;

  &:hover {
    @include upload-video-hovered;
  }

  form {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $blue;
    transition: none;
    justify-content: center;
    padding: 20px;

    input {
      display: none;
    }

    .uploadIconWrapper {
      padding: 15px;
      transition: none;

      .uploadIcon {
        padding: 0px;
        transition: none;
        height: 50px;
        width: 62.5px;
      }
    }

    .textSection {
      text-align: center;
      padding: 15px;
      transition: none;

      h6 {}

      p {
        text-decoration: underline;
        color: $mint-green;
      }
    }
  }
}