import { faXbox } from "@fortawesome/free-brands-svg-icons"
import { faClosedCaptioning } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import classes from "./Chip.module.scss"


export default function Chip({title, handleRemove}: {title: string; handleRemove: () => void;}){
  return(
    <div className={classes.chipWrapper}>
      <div style={{display: "flex", alignItems: "center"}} className={classes.chip}>
      <div>{title}</div>
      {/* <FontAwesomeIcon icon={faPhoenixFramework}/> */}
      <div className={classes.removeIcon} onClick={handleRemove}>
        x
      </div>
      </div>
    </div>
  )
}