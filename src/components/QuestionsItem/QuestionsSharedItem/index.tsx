import React, { useMemo, useState, useEffect } from "react";

import * as interfaces from "interfaces";
import {
    AnswersService,
    ClientService,
    FileService,
    QuestionService,
    UserService,
} from "services";
import { useServiceContext } from "services/ServiceProvider";

import ShareQuestion from "popups/ShareQuestion";
import Button from "shared/Button";
import IconAvatar from "shared/IconAvatar";

import classes from "./QuestionsSharedItem.module.scss";
import CustomConfirmModal from "shared/Modal/custom";
import QuestionSharedStatus, { QuestionSharedStatusEnum } from "components/QuestionSharedStatus";
import moment from "moment";

export default function QuestionsSharedItem(props: {
    question: interfaces.QuestionInterface;
    service?: QuestionService;
    fileService?: FileService;
    answersService?: AnswersService;
    userService?: UserService;
    clientService?: ClientService,
    client: interfaces.ClientInterface;
    refetchQuestionList: () => void;
}) {
    const { question, userService, clientService, service, client, refetchQuestionList } = props;
    const { adminStatsService } = useServiceContext();

    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
    const [hasCopied, setHasCopied] = useState(false);

    const handleRemoveQuestion = () => {
        setIsConfirmModalOpen(false);
        service?.removeQuestion(question.id, refetchQuestionList);
    };

    const handleCopyLink = () => {
        navigator.clipboard.writeText(question.shares?.[0]?.shareLink || '');
        setHasCopied(true);
        adminStatsService?.trackEvent('QuestionsShared', 'copy_link');

        setTimeout(() => {
            setHasCopied(false);
        }, 1000);
    };

    const questionStatus = useMemo(() => {
        if (question.shares?.some(share => share.isAnswered)) {
            return QuestionSharedStatusEnum.completed;
        } else if (question.shares?.some(share => share.isClicked)) {
            return QuestionSharedStatusEnum.clicked;
        } else if (question.shares?.some(share => share.isOpened)) {
            return QuestionSharedStatusEnum.opened;
        } else {
            return QuestionSharedStatusEnum.unopened;
        }
    }, [question]);

    const uniqueInvitedUsers = useMemo(() => {
        // Handle case when shares is undefined
        if (!question.shares || question.shares.length === 0) {
            return '';
        }

        // Extract all valid emails
        const emails: string[] = [];
        question.shares.forEach(share => {
            if (share.user && share.user.email) {
                emails.push(share.user.email);
            }
        });

        // Filter out duplicates
        const uniqueEmails = Array.from(new Set(emails));

        return uniqueEmails.join(', ');
    }, [question.shares]);

    return (
        <div className={classes.QuestionsItem}>
            <div className={classes.meta}>
                <div className={classes.user}>
                    <IconAvatar
                        imageUrl={question.user?.imageUrl}
                        categoryIcon={question.category}
                    />

                    <span className={classes.name}>
                        <div>
                            <span className={classes.bold}>Invited:</span> {
                                moment(question.shares?.[question.shares?.length - 1]?.createdAt).format("MMM D, YYYY")
                            }
                            &nbsp;&bull;&nbsp;
                            <span className={classes.bold}>Due date:</span> {
                                question.shares?.[question.shares?.length - 1]?.dueDate ?
                                    moment(question.shares?.[question.shares?.length - 1]?.dueDate).format("MMM D, YYYY") :
                                    "Never"
                            }
                        </div>
                        <div className={classes.email}><span className={classes.bold}>Users invited:</span> {uniqueInvitedUsers}</div>
                    </span>
                </div>

                <div className={classes.buttonContainer}>
                    {questionStatus !== QuestionSharedStatusEnum.unopened && <QuestionSharedStatus status={questionStatus} />}
                    <Button text={hasCopied ? "Copied" : "Copy Link"} callback={handleCopyLink} />
                    <Button
                        text="Resend"
                        iconText="share"
                        callback={() => {
                            setIsShareModalOpen(true);
                            adminStatsService?.trackEvent('QuestionsShared', 'open_resend_modal');
                        }}
                    />
                </div>
            </div>

            <div className={classes.questionText}>{question.text}</div>

            <ShareQuestion
                isOpen={isShareModalOpen}
                handleClose={() => {
                    setIsShareModalOpen(false);
                    adminStatsService?.trackEvent('QuestionsShared', 'close_resend_modal');
                }}
                question={question}
                questionService={service}
                clientService={clientService}
            />

            <CustomConfirmModal
                isOpen={isConfirmModalOpen}
                title="Confirm Removal"
                message="Are you sure you want to archive this question?"
                onConfirm={handleRemoveQuestion}
                onCancel={() => setIsConfirmModalOpen(false)}
            />
        </div>
    );
}
