import React, { useState } from "react";

import * as interfaces from "interfaces";
import {
  AnswersService,
  ClientService,
  FileService,
  QuestionService,
  UserService,
} from "services";
import { useNotification } from "hooks";
import { useServiceContext } from "services/ServiceProvider";

import ShareQuestion from "popups/ShareQuestion";
import BlockQuestion from "../../popups/BlockQuestion";
import Button from "shared/Button";
import IconAvatar from "shared/IconAvatar";

import classes from "./QuestionsItem.module.scss";
import VideoUploadModal from "../VideoHelpers/VideoUpload/VideoUploadModal";
// import VideoRecordModal from "../VideoHelpers/VideoRecord/VideoRecordModal";
import CounterButton from "../../shared/CounterButton";
import CustomConfirmModal from "shared/Modal/custom";

export default function QuestionsItem(props: {
  question: interfaces.QuestionInterface;
  service?: QuestionService;
  fileService?: FileService;
  answersService?: AnswersService;
  userService?: UserService;
  clientService?: ClientService,
  client: interfaces.ClientInterface;
  refetchQuestionList: () => void;
}) {
  const { question, userService, clientService, service, client, refetchQuestionList } = props;
  const { adminStatsService } = useServiceContext();

  const [isVideoUploadModalOpen, setIsVideoUploadModalOpen] = useState(false);
  // const [isVideoRecordModalOpen, setIsVideoRecordModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isBlockModalOpen, setIsBlockModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const { showAlert } = useNotification();

  const isArchived = question.isDenied || question.blockedForClient || (!question.isAnswered && !question.enabled);

  const handleRemoveQuestion = () => {
    setIsConfirmModalOpen(false);
    service?.removeQuestion(question.id, refetchQuestionList);
    adminStatsService?.trackEvent('QuestionsArchive', 'archive_question');

    // eslint-disable-next-line no-restricted-globals
    // if (confirm("Are you sure you want to archive this question?"))
  };

  const handleRestoreQuestion = () => {
    const payload = {
      id: question.id,
      isApproved: null,
      isDenied: null,
      enabled: true,
    };

    service?.approveQuestion(payload, refetchQuestionList);
    adminStatsService?.trackEvent('QuestionsArchive', 'restore_question');
  };

  const handleApproval = (action: "approve" | "deny") => {
    const isApprove = action === "approve";
    const payload = {
      id: question.id,
      isApproved: isApprove,
      isDenied: !isApprove,
    };

    service?.approveQuestion(payload, refetchQuestionList);
    adminStatsService?.trackEvent('QuestionsArchive', isApprove ? 'approve_question' : 'deny_question');
  };

  const handleBlock = () => {
    userService?.block(
      { id: question.user.id, isBlocked: question.blockedForClient },
      (response: any) => {
        if (response) {
          showAlert(
            `User ${question.blockedForClient ? "unblocked" : "blocked"}`
          );
          refetchQuestionList();
          adminStatsService?.trackEvent('QuestionsArchive', question.blockedForClient ? 'unblock_user' : 'block_user');
        } else
          showAlert(
            `Something went wrong ${question.blockedForClient ? "unblocking" : "blocking"
            } user`
          );
        setIsBlockModalOpen(false);
      }
    );
  };

  function getButtons(isApproved: boolean) {
    if (isApproved && !isArchived)
      return (
        <>
          <Button text="Archive" callback={() => {
            setIsConfirmModalOpen(true);
            adminStatsService?.trackEvent('QuestionsArchive', 'open_archive_confirm');
          }} />
          {/*<AnswerVideoMenu*/}
          {/*  setUploadModal={setIsVideoUploadModalOpen}*/}
          {/*  setRecordModal={setIsVideoRecordModalOpen}*/}
          {/*  votes={question.votes}*/}
          {/*/>*/}
          <CounterButton
            buttonCount={question.votes}
            buttonText="Answer"
            callback={() => {
              setIsVideoUploadModalOpen(true);
              adminStatsService?.trackEvent('QuestionsArchive', 'open_answer_modal');
            }}
          />
          {/*<button*/}
          {/*  className={`${*/}
          {/*    active ? "bg-green-400 text-white" : "text-gray-900"*/}
          {/*  } group flex w-full items-center rounded-md px-2 py-2 text-sm`}*/}
          {/*  onClick={() => setUploadModal(true)}*/}
          {/*>*/}
          {/*  Upload*/}
          {/*</button>*/}
          <Button
            text=""
            iconText="share"
            callback={() => {
              setIsShareModalOpen(true);
              adminStatsService?.trackEvent('QuestionsArchive', 'open_share_modal');
            }}
          />
        </>
      );
    else
      return (
        <>
          {isArchived && !question.blockedForClient && <Button text="Add Back" callback={() => {
            handleRestoreQuestion();
            adminStatsService?.trackEvent('QuestionsArchive', 'restore_question_button');
          }} />}
          {!isArchived && !question.blockedForClient && <Button text="Archive" callback={() => {
            setIsConfirmModalOpen(true);
            adminStatsService?.trackEvent('QuestionsArchive', 'open_archive_confirm');
          }} />}
          {!isArchived && !question.blockedForClient && !question.isApproved && (
            <Button
              text="Approve"
              customClass="success-outline"
              callback={() => {
                handleApproval("approve");
                adminStatsService?.trackEvent('QuestionsArchive', 'approve_question_button');
              }}
              iconText="thumbs-up"
            />
          )}
          {!question.blockedForClient && client.clientType === "Campaign" && !isArchived && (
            <Button
              text="Deny"
              customClass="error-outline"
              callback={() => {
                handleApproval("deny");
                adminStatsService?.trackEvent('QuestionsArchive', 'deny_question_button');
              }}
              iconText="thumbs-down"
            />
          )}
          {client.clientType === "Campaign" && !question.isApproved && (
            <Button
              text={question.blockedForClient ? "Unblock" : "Block"}
              customClass={
                question.blockedForClient ? "success-outline" : "error-outline"
              }
              callback={
                question.blockedForClient
                  ? () => {
                    handleBlock();
                    adminStatsService?.trackEvent('QuestionsArchive', 'unblock_user_button');
                  }
                  : () => {
                    setIsBlockModalOpen(true);
                    adminStatsService?.trackEvent('QuestionsArchive', 'open_block_modal');
                  }
              }
            />
          )}
        </>
      );
  }

  return (
    <div className={classes.QuestionsItem}>
      <div className={classes.meta}>
        <div className={classes.user}>
          <IconAvatar
            imageUrl={question.user?.imageUrl}
            categoryIcon={question.category}
          />

          <span className={classes.name}>
            <div>
              {question.overridingName || `${question.user.firstName} ${question.user.lastName}`} &bull; {new Date(question.createdAt).toLocaleString('en-US', { month: 'short', day: 'numeric' })}
            </div>
            <div className={classes.email}>{question.user.email}</div>
          </span>
        </div>

        <div className={classes.buttonContainer}>
          {getButtons(question.isApproved)}
        </div>
      </div>

      <div className={classes.questionText}>{question.text}</div>
      {/* <div className={classes.date}>
        {new Date(question.createdAt).toLocaleString('en-US', { month: 'short', day: 'numeric' })}
      </div> */}

      <ShareQuestion
        isOpen={isShareModalOpen}
        handleClose={() => setIsShareModalOpen(false)}
        question={question}
        questionService={service}
        clientService={clientService}
      />
      {isVideoUploadModalOpen && (
        <VideoUploadModal
          onClose={() => {
            setIsVideoUploadModalOpen(false);
          }}
          question={question}
          clientService={clientService}
        />
      )}
      {/* {isVideoRecordModalOpen && (
        <VideoRecordModal
          onClose={() => {
            setIsVideoRecordModalOpen(false);
          }}
          question={question}
          clientService={clientService}
        />
      )} */}

      <BlockQuestion
        isOpen={isBlockModalOpen}
        handleClose={() => setIsBlockModalOpen(false)}
        handleBlock={handleBlock}
      />

      <CustomConfirmModal
        isOpen={isConfirmModalOpen}
        title="Confirm Removal"
        message="Are you sure you want to archive this question?"
        onConfirm={handleRemoveQuestion}
        onCancel={() => setIsConfirmModalOpen(false)}
      />
    </div>
  );
}
