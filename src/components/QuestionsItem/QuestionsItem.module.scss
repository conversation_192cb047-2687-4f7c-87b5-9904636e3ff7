@import 'styles/variables';

.QuestionsItem {
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid $grey;
  font-size: 20px;

  .meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-content: space-between;
  }

  .user {
    display: flex;
    align-items: center;

    .name {
      margin-left: 10px;
      color: #999;
      font-size: 16px;

      .email {
        color: $dark-blue;
      }
    }
  }

  .questionText {
    padding-top: 10px;
    font-size: 18px;
    font-weight: 300;
  }

  .date {
    position: absolute;
    right: 0;
    bottom: 10px;
    padding: 2px 5px;
    color: $dark-grey;
    font-size: 16px;
    background: #ffffff99;
    border-radius: 50px;
  }

  .buttonContainer {
    float: right;

    button {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

@media (max-width: 640px) {
  .QuestionsItem {

    .meta {
      flex-direction: column-reverse;
      align-items: flex-start;
    }

    .user {
      margin-top: 10px;
    }

    .buttonContainer {
      align-self: flex-end;
    }
  }
}