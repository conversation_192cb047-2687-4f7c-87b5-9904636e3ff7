import Button from "shared/Button";

import classes from "./TeleprompterNotes.module.scss";
import { ClientService, QuestionService } from "services";
import { useEffect, useMemo, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Checkbox } from "shared/ui/checkbox";
import { useServiceContext } from "services/ServiceProvider";
import { Select } from "components/Select";
import { Input } from "shared/ui/input";
import { cn } from "utils/utils";

interface TeleprompterNotesProps {
  handleClose: () => void;
  handleNextStep: () => void;
  setNotes: React.Dispatch<React.SetStateAction<string>>;
  notes: string;
  clientService?: ClientService;
  question: any;
}

export default function TeleprompterNotes({
  handleClose,
  handleNextStep,
  notes,
  setNotes,
  clientService,
  question,
}: TeleprompterNotesProps) {
  const [generated, setGenerated] = useState<false | 'generating' | true>(false);
  const [shouldSaveScript, setShouldSaveScript] = useState(false);
  const [selectedTranscript, setSelectedTranscript] = useState<string | null>(null);
  const [transcriptName, setTranscriptName] = useState<string>("");
  const { questionService, adminStatsService } = useServiceContext();

  const recordHandler = () => {
    if (shouldSaveScript) {
      questionService?.createTranscript({
        name: transcriptName,
        text: notes,
        clientId: clientService?.client?.id,
      })
    }
    adminStatsService?.trackEvent('QuestionsArchive', 'lets_record');
    handleNextStep();
  }

  const transcripts = useMemo(() => {
    return questionService?.transcripts
  }, [questionService?.transcripts])

  useEffect(() => {
    if (selectedTranscript) {
      if (selectedTranscript === 'None') {
        // Do nothing, keep current notes
        return;
      }
      setNotes(transcripts?.find(item => item.name === selectedTranscript)?.text || "")
    }
  }, [selectedTranscript])

  const { client } = clientService || {};

  const handleParseResponse = (response: any) => {
    setGenerated('generating');
    adminStatsService?.trackEvent('QuestionsArchive', 'generate_teleprompter_script');

    clientService?.getAiResponse(question.text, (responseData: any) => {
      setNotes(responseData);
      setGenerated(true);
      adminStatsService?.trackEvent('QuestionsArchive', 'teleprompter_script_generated');
    })
  }

  return (
    <div className={classes.teleprompterNotes}>
      {window.MediaRecorder ? (
        <>
          <p className={classes.subTitle}>
            Add notes in here for you to read out while recording.
          </p>

          <div className={classes.bodySection}>
            <textarea
              defaultValue={notes || ""}
              placeholder="Describe yourself here..."
              onChange={(e) => setNotes(e.target.value)}
            ></textarea>

            {/* {client?.aiQuestionsEnabled && ( */}
              <div className={classes.aiBar}>
                <p>
                  <FontAwesomeIcon icon={['fas', 'magic']} />
                  Generate an answer to use as a script
                </p>

                <Button text={generated === 'generating' ? "Generating..." : generated === true ? "Re-Generate" : "Generate"} callback={handleParseResponse} />
              </div>
            {/* )} */}

            <div className={classes.actionWrapper}>
              <div className={classes.checkboxWrapper}>
                <Checkbox checked={shouldSaveScript} onCheckedChange={(value) => setShouldSaveScript(!!value)} className={classes.checkbox} name="recordCheck" id="recordCheck" />
                <label htmlFor="recordCheck">Save this script</label>
                <Input
                  className={cn(classes.nameInput, shouldSaveScript && classes.show)}
                  placeholder="Enter a name"
                  value={transcriptName}
                  onChange={(e) => setTranscriptName(e.target.value)}
                />
              </div>
              {transcripts && transcripts.length > 0 &&
                <div className={classes.selectWrapper}>
                  <Select
                    placeholder={"Select a transcript"}
                    items={['None'].concat(transcripts?.map(item => (item.name)) || [])}
                    selected={selectedTranscript}
                    setSelected={setSelectedTranscript} />
                </div>
              }
            </div>

            <div className={classes.buttonWrapper}>
              {/* Does not work. */}
              {/* <Button
                text="Cancel"
                customClass={classes.cancelButton}
                callback={handleClose}
              /> */}

              <Button
                text="Let's Record"
                customClass={classes.recordVideo}
                callback={recordHandler}
              />
            </div>
          </div>
        </>
      ) : (
        <div className={classes.errorWrapper}>
          <p className={classes.subTitle}>
            The video can not be recorded on this device, you can upload a video
            from your gallery.
          </p>

          <div className={classes.backButton}>
            <Button
              text="Back"
              customClass={classes.cancelButton}
              callback={handleClose}
            />
          </div>
        </div>
      )}
    </div>
  );
}
