@import 'styles/variables';

.teleprompterNotes {
  width: 100%;
  text-align: center;
  font-size: 16px;

  .title {
    padding-bottom: 20px;
    border-bottom: 1px solid $offwhite;
    margin-bottom: 10px;
    font-weight: 500;
  }

  .subTitle {
    font-size: 18px;
    color: $dark-blue;
    font-weight: 400;
  }

  .bodySection {
    padding: 20px;
    margin-top: 20px;
    background-color: $pale-blue;

    textarea {
      width: 100%;
      height: 322px;
      max-height: 500px;
      border: 1px solid $offwhite;
      padding: 10px;
      font-size: 16px;
      border-radius: 5px;
      white-space: pre-line;
      font-weight: 500;
      color: black;
      font-family: monospace;

      &:focus {
        outline: $blue 2px dashed !important;
      }

      &::placeholder {
        color: $grey;
        font-weight: 500;
      }
    }

    button {
      color: $blue;
      padding: 7px 35px;
      border-radius: 100px;

      &:hover {
        background-color: $pale-blue;
      }

      &:nth-child(2) {
        margin-left: 10px;
      }
    }

    .actionWrapper {
      display: flex;
      align-content: center;
      align-items: center;
      gap: 10px;

      .selectWrapper {
        margin-top: 10px;
        border-radius: 100px;
      }

      .nameInput {
        width: fit-content;
        background-color: white;
        margin-left: 0.75rem;
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
        display: none;
      }

      .show {
        opacity: 1 !important;
        display: block !important;
      }

      .checkboxWrapper {
        width: 100%;
        display: flex;
        align-items: center;
        padding-top: 15px;

        .checkbox {
          color: white;
          border-radius: 5px;
          padding: 0;
          margin-right: 10px;
          margin-top: -2px;
        }
      }
    }

    .buttonWrapper {
      margin-top: 25px;
      display: flex;
      justify-content: center;

      .recordVideo {
        margin-left: 10px;
      }
    }
  }

  .errorWrapper {
    padding: 20px;
    background-color: $pale-blue;
    border: 1px solid $red;
    border-radius: 20px;

    .backButton {
      margin-top: 25px;
    }
  }
}

.aiBar {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  align-items: center;
  background: white;
  // border: 1px solid #00000021;
  padding: 10px 20px;
  border-radius: 5px;
  color: $blue;
  box-shadow: 0 0 0 1px $blue;

  svg {
    margin-right: 10px;
  }

  button {
    color: $blue;

    &:hover {
      background-color: $pale-blue;
    }
  }
}

@media screen and (max-width: 640px) {
  .teleprompterNotes {
    .bodySection {
      textarea {
        padding: 10px;
      }

      .actionWrapper {
        flex-direction: column;

        .checkboxWrapper {
          flex-direction: column;
        }
      }
    }
  }

  .aiBar {
    flex-direction: column;
    gap: 10px;
  }
}