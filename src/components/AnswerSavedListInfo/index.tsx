import classes from "./AnswerSavedListInfo.module.scss"

export default function AnswerSavedListInfo({savedListInfo}: {savedListInfo?: any}){
  const totalNotified = savedListInfo?.totalNotifiedsavedListInfo?.totalNotified
  return(
    <div className={classes.answerSavedListInfo}>
      {/* test */}
      {/* <p>{savedListInfo.totalNotified} {savedListInfo.totalResend > 1 && "(2x)"} users were notified from the following Saved Lists: </p> */}
      <p>{totalNotified > 1 ? totalNotified : 1} {savedListInfo?.totalResends > 1 ? "(x2)"  : "" }users were notified from the following Saved Lists: </p>
      <ul>
        {savedListInfo?.listMetas?.map((listItem: any, index: number) => listItem?.name && <li key={index}>{listItem?.name}</li>)}
      </ul>
    </div>
  )
}