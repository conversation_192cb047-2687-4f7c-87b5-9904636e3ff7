import { FunctionComponent } from "react";
import styles from "./NavLink.module.scss";
import { NavLink, useLocation } from "react-router-dom";
import cn from "classnames";
import { useUIStore } from "../../hooks/zustand/uiStore";
import { useServiceContext } from "services/ServiceProvider";

export type SidebarItemType = {
  className?: string;
  title?: string;
  href?: string;
  icon?: string;
  suboption?: boolean;
  parentRoute?: string;
  aTag?: boolean;
};

const SidebarItem: FunctionComponent<SidebarItemType> = ({
  className = "",
  title,
  href = "/",
  icon,
  suboption = false,
  parentRoute = "",
  aTag
}) => {
  const location = useLocation();
  const setSidebarOpen = useUIStore(state => state.setSidebarOpen);
  const { adminStatsService } = useServiceContext();

  const handleClick = () => {
    // Track more specific events for sidebar navigation
    const category = 'Sidebar';
    const action = `sidebar_click_${(title ?? 'unknown').toLowerCase().replace(/\s+/g, '_')}`;

    adminStatsService?.trackEvent(category, action);

    setSidebarOpen(false);
  };

  return (
    aTag ? (
      <a
        href="mailto:<EMAIL>"
        className={cn(styles.navLink, className)}
        onClick={() => {
          adminStatsService?.trackEvent('Sidebar', 'sidebar_click_feedback_email');
          handleClick();
        }}
      >
        {icon && <img className={styles.icons} alt="" src={icon} />}
        <span className={styles.engagements}>{title}</span>
      </a>
    ) : (
      <NavLink
        className={cn(styles.navLink, className, suboption && !location.pathname.includes(parentRoute) && "hidden", suboption && location.pathname === href && styles.active, suboption && styles.suboption)}
        to={href}
        onClick={handleClick}
      >
        {icon && <img className={styles.icons} alt="" src={icon} />}
        <span className={styles.engagements}>{title}</span>
      </NavLink>
    )
  );
};

export default SidebarItem;
