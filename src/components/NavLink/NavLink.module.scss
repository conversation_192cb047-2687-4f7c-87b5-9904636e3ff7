.engagements,
.icons {
  position: relative;
  flex-shrink: 0;
}

.icons {
  width: 20px;
  height: 20px;
  overflow: hidden;
}

.engagements {
  text-decoration: none;
  width: 100%;
  font-size: var(--font-size-base);
  color: var(--color-slategray);
  font-weight: 500;
  text-align: left;
  display: inline-block;
}

.navLink {
  cursor: pointer;
  border: 0;
  width: 100%;
  padding: var(--padding-mini) var(--padding-xl);
  padding-left: 30px;
  border-radius: var(--br-7xs);
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: var(--gap-3xs);
}

.navLink:hover {
  background-color: var(--color-white);
  color: var(--color-crimson);
}

.active {
  background-color: var(--color-white);
  color: var(--color-crimson);
}

.suboption {
  margin-left: 20px;
  width: calc(100% - 30px);
  padding-left: 40px;
}