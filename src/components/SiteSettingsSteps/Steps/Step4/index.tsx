import { useEffect, useState } from "react"

import { ClientInterface } from 'interfaces/client.interfaces'

import classes from "./Step.module.scss"

import TopBar from 'assets/platform/edit-client/Top Bar.png';
import VideoLinks from 'assets/platform/edit-client/Video Links.png';
import AskPillLink from 'assets/platform/edit-client/Ask Pill Link.png';
import NewQuestionButton from 'assets/platform/edit-client/New Question Button.png';
import OpenQuestionsAndAnswers from 'assets/platform/edit-client/Open Questions & Answers.png';

type colours = 'topBarColour' | 'videoLinksColour' | 'newQuestionColour' | 'plusAskPillColour' | 'openQuestionsAndAnswersColour';

interface clientColorsInterface {
  topBarColour: string;
  videoLinksColour: string;
  newQuestionColour: string;
  plusAskPillColour: string;
  openQuestionsAndAnswersColour: string;
}

interface fieldInterface {
  name: colours;
  value: string;
}

const getColors = (client?: ClientInterface): clientColorsInterface => {
  return {
    topBarColour: client?.topBarColour || '#012B74',
    videoLinksColour: client?.videoLinksColour || '#4975E9',
    newQuestionColour: client?.newQuestionColour || '#29B36E',
    plusAskPillColour: client?.plusAskPillColour || '#29B36E',
    openQuestionsAndAnswersColour: client?.openQuestionsAndAnswersColour || '#0E509B',
  }
}

export default function Step4(props: { client: ClientInterface, setClient: Function }) {
  const { client, setClient } = props;

  const [clientColors, setClientColors]: [
    clientColorsInterface, (v: clientColorsInterface) => void
  ] = useState(getColors(client))

  const updateColors = () => {
    const colours: clientColorsInterface = getColors(client)

    setClientColors(colours)
  }

  function validate(event: any) {
    if (event.key.match(/[0-9a-zA-Z#]/) === null)
      event.preventDefault();
  }

  function onChangeField(event: any) {
    updateClient({
      name: event.target.name,
      value: event.target.value
    })
  }

  function updateClient({ name, value }: fieldInterface) {
    if (value.match(/^#/) === null) value = `#${value}`

    // client[name] = value;
    // setClient(client)

    setClient((prevClient: any) => ({
      ...prevClient,
      [name]: value,
    }));

    updateColors()
  }

  useEffect(updateColors, [client])

  return (
    <div className={classes.Step}>
      <form className={classes.colours} method="post">
        <div className={classes.inputWithLabel}>
          <h2>Top Bar Color</h2>

          <img className={classes.thumbnail} src={TopBar} alt="Top Bar" />

          <div className={classes.input}>
            <input type="color" name="topBarColour" defaultValue={client.topBarColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="topBarColour" placeholder={clientColors.topBarColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>

        {/* <div className={classes.inputWithLabel}>
          <h2>Video Links Color</h2>

          <img className={classes.thumbnail} src={VideoLinks} alt="Video Links" />

          <div className={classes.input}>
            <input type="color" name="videoLinksColour" defaultValue={client.videoLinksColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="videoLinksColour" placeholder={clientColors.videoLinksColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
          </div>
        </div> */}

        <div className={classes.inputWithLabel}>
          <h2>+Upvote Button Color</h2>

          <img className={classes.thumbnail} src={AskPillLink} alt="Ask Pill Link" />

          <div className={classes.input}>
            <input type="color" name="plusAskPillColour" defaultValue={client.plusAskPillColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="plusAskPillColour" placeholder={clientColors.plusAskPillColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2>New Question Button Color</h2>

          <img className={classes.thumbnail} src={NewQuestionButton} alt="New Question Button" />

          <div className={classes.input}>
            <input type="color" name="newQuestionColour" defaultValue={client.newQuestionColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="newQuestionColour" placeholder={clientColors.newQuestionColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2>Open Questions &amp; Answers Color</h2>

          <img className={classes.thumbnail} src={OpenQuestionsAndAnswers} alt="Open Questions &amp; Answers" />

          <div className={classes.input}>
            <input type="color" name="openQuestionsAndAnswersColour" defaultValue={client.openQuestionsAndAnswersColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="openQuestionsAndAnswersColour" placeholder={clientColors.openQuestionsAndAnswersColour} onChange={onChangeField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>
      </form>
    </div>
  )
}
