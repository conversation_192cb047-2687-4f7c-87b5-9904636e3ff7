@import 'src/styles/variables';

.Step {
  .inputWithLabel {
    margin: 0 0 50px 0;

    h2 {
      margin-block-start: 15px;
      margin-block-end: 15px;
      font-size: 24px;
      font-weight: bold;
    }

    .thumbnail {
      position: relative;
      padding: 5px;
      margin-top: 20px;
      margin-bottom: -15px;
      min-height: 0;
      width: 100%;
      background-color: $pale-blue;
      border-radius: 5px;
      text-align: center;
      overflow: hidden;

      &:hover {
        background-color: $blue;
      }
    }

    .input {
      display: flex;
      align-items: center;
      margin-top: 10px;
      padding: 10px 10px;
      width: 100%;
      border-radius: 5px;
      border: 1px solid $grey;
      font-size: 16px;

      input[type="color"] {
        padding: 0 2px;
        min-width: auto;
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 5px;
        overflow: hidden;
        background: white;
        appearance: color-well;
        background-color: $pale-blue;
        cursor: pointer;
      }

      input.label {
        padding-left: 10px;
        font-size: 18px;
        color: #000;
        font-weight: 600;
      }
    }
  }
}