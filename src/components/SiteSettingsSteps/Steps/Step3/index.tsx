import React, { useState } from "react"

import { ClientInterface } from 'interfaces/client.interfaces'

import Icons from 'shared/Icons';
import Button from 'shared/Button';

import classes from "./Step.module.scss"

export default function Step3(props: { client: ClientInterface, setClient: Function }) {
  const { client, setClient } = props;

  const [categories, setCategories] = useState(parseCatgeories())

  function parseCatgeories(useLocalCategories?: Boolean): string[] {
    const parsed = (
      (useLocalCategories ? categories.join(',') : client.categories) || ''
    ).replace(/\s+/g, ' ')
      .replace(/, /g, ',')
      .replace(/ ,/g, ',')
      .replace(/^ /, '')
      .replace(/ $/, '').split(',')

    // Sort categories alphabetically (case-insensitive)
    return parsed.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
  }

  function onBlurCategory(event: any, index: number) {
    const category = event.target.innerText.trim()

    // If the category is empty, remove it instead of setting to 'category'
    if (category.length === 0) {
      removeCategory(event, index)
      return
    }

    // Only update if the category actually changed
    if (categories[index] !== category) {
      const updatedCategories = [...categories]
      updatedCategories[index] = category
      // Sort categories alphabetically after update
      const sortedCategories = updatedCategories.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
      setCategories(sortedCategories)
      updateClient(sortedCategories)
    }
  }

  function onKeyDownCategory(event: any) {
    // Allow Enter to blur the field (finish editing)
    if (event.key === 'Enter') {
      event.preventDefault()
      event.target.blur()
    }
  }

  function removeCategory(event: any, index: number) {
    if (event && event.preventDefault) {
      event.preventDefault();
    }

    // Don't remove if it's the last category
    if (categories.length <= 1) {
      return
    }

    const updatedCategories = categories.filter((_, i) => i !== index)
    // Categories should already be sorted, but ensure they remain sorted
    const sortedCategories = updatedCategories.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))

    setCategories(sortedCategories)
    updateClient(sortedCategories)
  }

  function addCategory() {
    const newCategory = 'New Category'
    const updatedCategories = [...categories, newCategory]
    // Sort categories alphabetically after adding
    const sortedCategories = updatedCategories.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
    setCategories(sortedCategories)
    updateClient(sortedCategories)
  }

  function updateClient(c?: string[]) {
    setClient((prevClient: any) => ({
      ...prevClient,
      categories: (c || categories).join(','),
    }));
  }

  return (
    <div className={classes.Step}>
      <div className={classes.formThumbnailLabel}>
        <div>Categories organize questions.</div>
        <div>Get started by adding a category.</div><br />

        <Button text="+ Add A Category" callback={addCategory} />
      </div>

      <form className={classes.categoryForm}>
        {categories.map((category, index) => {
          return <div key={index} className={classes.inputWithButton}>
            <div
              className={classes.textInput}
              contentEditable="true"
              onBlur={(event) => onBlurCategory(event, index)}
              onKeyDown={onKeyDownCategory}
              suppressContentEditableWarning={true}
            >
              {category}
            </div>

            <Icons iconType={category} />

            <button className="remove" onClick={(event) => removeCategory(event, index)}>x</button>
          </div>
        })}
      </form>

      <div className="error categoryForm hidden">
        • Please make sure there are no duplicates<br />
        • Categories associated with existing questions cannot be removed
      </div>
    </div>
  )
}
