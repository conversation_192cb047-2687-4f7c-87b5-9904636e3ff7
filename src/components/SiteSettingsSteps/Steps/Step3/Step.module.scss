@import 'src/styles/variables';

.Step {
  .formThumbnailLabel {
    margin-top: 20px;
    text-align: center;

    button {
      padding: 5px 15px;
    }
  }

  .categoryForm {
    position: relative;
    display: block;
    margin-top: 20px;
    padding: 10px 10px 20px;
    min-height: 90px;
    background: white;
    border: 1px solid $offwhite;
    border-radius: 10px;
    font-size: 16px;

    .inputWithButton {
      display: inline-block;
      margin-top: 10px;
      margin-left: 10px;
      padding: 5px 5px 5px 15px;
      background: $pale-blue;
      border-radius: 40px;
      line-height: 1.8;
      color: $blue;

      .textInput {
        display: inline-block;
        vertical-align: middle;
        min-width: auto;
        background: transparent;
        border: none;
        border-radius: 40px;
        line-height: 1;
      }

      svg {
        margin-left: 5px;
        display: inline-block;
      }

      button {
        position: relative;
        top: -1px;
        margin-left: 10px;
        padding: 3px 8px 5px;
        border: 1px solid $blue;
        border-radius: 40px;
        font-size: 16px;
        color: $blue;
        line-height: 1;
        background: none;

        &:hover {
          color: $dark-blue;
          border-color: $dark-blue
        }
      }
    }
  }
}