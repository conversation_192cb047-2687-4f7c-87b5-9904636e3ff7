@import 'src/styles/variables';

.Step {
  .logoSection {
    margin: 0 0 50px 0;

    h2 {
      margin-block-start: 15px;
      margin-block-end: 15px;
      font-size: 24px;
      font-weight: bold;
    }

    p {
      margin: 0 0 15px 0;
      font-size: 14px;
    }

    .thumbnail {
      position: relative;
      padding: 20px;
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      background-color: $pale-blue;
      background-position: 50% calc(50% - 30px);
      background-size: cover;
      background-repeat: no-repeat;
      border-radius: 5px;
      text-align: center;
      cursor: pointer;
      overflow: hidden;

      .uploadText {
        display: block;
        padding: 10px 0;
        color: $blue;
        font-weight: 300;
        cursor: pointer;
        text-align: center;

        * {
          cursor: pointer;
        }

        svg,
        .icon {
          transform: scale(4);
          margin: 40px 0;
        }

        .link {
          color: $dark-blue;
          font-weight: 700;
        }

        &:hover {
          color: $dark-blue;

          .link {
            color: $blue;
          }
        }
      }

      label {
        cursor: pointer;
      }

      button {
        width: 170px;
        margin: 300px auto 0 auto;
        border: none;
        box-shadow: 0 0 20px 0 $transparent-black-low;
        pointer-events: none;
      }
    }

    .thumbnailLabel {
      margin-top: 20px;
      text-align: center;
    }
  }

  .inputWithLabel {
    margin: 0 0 50px 0;

    h2 {
      margin-block-start: 15px;
      margin-block-end: 15px;
      font-size: 24px;
      font-weight: bold;
    }

    p {
      margin: 0 0 15px 0;
      font-size: 14px;
    }

    .thumbnail {
      position: relative;
      padding: 5px;
      margin-top: 10px;
      margin-bottom: 15px;
      min-height: 0;
      width: 100%;
      background-color: $pale-blue;
      border-radius: 20px;
      text-align: center;
      overflow: hidden;

      &:hover {
        background-color: $blue;
      }
    }

    .input {
      display: flex;
      align-items: center;
      margin-top: 10px;
      padding: 10px 10px;
      width: 100%;
      border-radius: 5px;
      border: 1px solid $grey;
      font-size: 16px;

      input[type="color"] {
        padding: 0 2px;
        min-width: auto;
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 5px;
        overflow: hidden;
        background: white;
        appearance: color-well;
        background-color: $pale-blue;
        cursor: pointer;
      }

      input.label {
        padding-left: 10px;
        font-size: 18px;
        color: #000;
        font-weight: 600;
      }
    }
  }
}