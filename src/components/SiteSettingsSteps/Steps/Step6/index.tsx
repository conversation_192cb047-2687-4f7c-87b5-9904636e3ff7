import { useEffect, useState } from "react"
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { useNotification } from "hooks";
import { ClientInterface } from 'interfaces/client.interfaces'
import { FileService, AuthService } from 'services'

import Button from 'shared/Button';

import classes from "./Step.module.scss"

import EmbedPrimaryColor from 'assets/platform/edit-client/Embed Primary Color.png';
import EmbedProgressBarColor from 'assets/platform/edit-client/Embed Progress Bar Color.png';
import EmbedButtonColor from 'assets/platform/edit-client/Embed Button Color.png';

type embedColours = 'embedPrimaryColour' | 'embedAccentColour' | 'embedProgressBarColour' | 'embedButtonColour';

interface clientEmbedColorsInterface {
  embedPrimaryColour: string;
  embedAccentColour: string;
  embedProgressBarColour: string;
  embedButtonColour: string;
}

interface fieldInterface {
  name: embedColours;
  value: string;
}

const getEmbedColors = (client?: ClientInterface): clientEmbedColorsInterface => {
  return {
    embedPrimaryColour: client?.embedPrimaryColour || '#012B74',
    embedAccentColour: client?.embedAccentColour || '#4975E9',
    embedProgressBarColour: client?.embedProgressBarColour || '#38F580',
    embedButtonColour: client?.embedButtonColour || '#29B36E',
  }
}

export default function Step6(props: { client: ClientInterface, setClient: Function }) {
  const { showAlert } = useNotification();
  const { client, setClient } = props;

  const [clientEmbedColors, setClientEmbedColors]: [
    clientEmbedColorsInterface, (v: clientEmbedColorsInterface) => void
  ] = useState(getEmbedColors(client))

  const [whiteLogoFile, setWhiteLogoFile] = useState('')
  const [expandedWhiteLogoFile, setExpandedWhiteLogoFile] = useState('')

  const authService = new AuthService()
  const fileService = new FileService(authService.getToken(), client.id)

  // Set default values if not present
  client.embedPrimaryColour ||= '#2760CF'
  client.embedAccentColour ||= '#E94E77'
  client.embedProgressBarColour ||= '#2760CF'
  client.embedButtonColour ||= '#E94E77'

  const updateEmbedColors = () => {
    const colours: clientEmbedColorsInterface = getEmbedColors(client)
    setClientEmbedColors(colours)
  }

  function validate(event: any) {
    if (event.key.match(/[0-9a-zA-Z#]/) === null)
      event.preventDefault();
  }

  function onChangeColorField(event: any) {
    updateClient({
      name: event.target.name,
      value: event.target.value
    })
  }

  function updateClient({ name, value }: fieldInterface) {
    if (value.match(/^#/) === null) value = `#${value}`

    setClient((prevClient: any) => ({
      ...prevClient,
      [name]: value,
    }));

    updateEmbedColors()
  }

  // White logo upload functions
  function onDragFile(event: any) {
    event.preventDefault();
    event.stopPropagation();
  }

  function onDropFile(event: any) {
    event.preventDefault();
    updateWhiteLogo(event.dataTransfer.files);
  }

  function onChangeLogoField(event: any) {
    if (event?.target?.files)
      updateWhiteLogo(event.target.files);
    else
      console.error('Site Settings - Step 6 - White logo file not added', event?.target?.files);
  }

  function updateWhiteLogo(files: any) {
    const imageFileData: Blob = files[0];

    const formData = new FormData();
    const reader = new FileReader();

    reader.readAsDataURL(imageFileData);
    reader.onload = function (event) {
      if (event.target?.result)
        setWhiteLogoFile(event.target.result + '');
    };

    formData.append("file", imageFileData);

    fileService.uploadImage(formData).then((data: any) => {
      const whiteLogoURL = data.url;

      setClient((prevClient: any) => ({
        ...prevClient,
        whiteLogoURL,
      }));

      showAlert("The white logo is uploaded successfully, you can save the client now.")
    })
  }

  // Expanded white logo upload functions
  function onDragExpandedFile(event: any) {
    event.preventDefault();
    event.stopPropagation();
  }

  function onDropExpandedFile(event: any) {
    event.preventDefault();
    updateExpandedWhiteLogo(event.dataTransfer.files);
  }

  function onChangeExpandedLogoField(event: any) {
    if (event?.target?.files)
      updateExpandedWhiteLogo(event.target.files);
    else
      console.error('Site Settings - Step 6 - Expanded white logo file not added', event?.target?.files);
  }

  function updateExpandedWhiteLogo(files: any) {
    const imageFileData: Blob = files[0];

    const formData = new FormData();
    const reader = new FileReader();

    reader.readAsDataURL(imageFileData);
    reader.onload = function (event) {
      if (event.target?.result)
        setExpandedWhiteLogoFile(event.target.result + '');
    };

    formData.append("file", imageFileData);

    fileService.uploadImage(formData).then((data: any) => {
      const expandedWhiteLogoURL = data.url;

      setClient((prevClient: any) => ({
        ...prevClient,
        expandedWhiteLogoURL,
      }));

      showAlert("The expanded white logo is uploaded successfully, you can save the client now.")
    })
  }

  useEffect(updateEmbedColors, [client])

  return (
    <div className={classes.Step}>
      {/* White Logo Upload Section */}
      <div className={classes.logoSection}>
        <h2>White Logo</h2>
        <p>Upload a white version of your logo for use on dark backgrounds in the AI embed.</p>

        <form className={classes.thumbnail} style={{ backgroundImage: `url(${whiteLogoFile || client.whiteLogoURL})` }}
          encType="multipart/form-data"
          onDragEnd={onDragFile} onDragLeave={onDragFile}
          onDragOver={onDragFile} onDragEnter={onDragFile} onDrop={onDropFile}
        >
          {!client.whiteLogoURL && <label className={classes.uploadText} htmlFor="whiteLogoUpload">
            <FontAwesomeIcon className={classes.icon} icon={['fas', 'photo-video']} /><br />

            Drag and Drop or<br />

            <span className={classes.link}>Select a File</span>
          </label>}
          {client.whiteLogoURL && <label htmlFor="whiteLogoUpload" >
            <Button text="Change" />
          </label>}

          <input className="hidden" id="whiteLogoUpload" type="file" name="whiteLogo[]" accept="image/*" onChange={onChangeLogoField} />
        </form>

        <div className={classes.thumbnailLabel}>
          Accepted file types: PNG, SVG and GIF.<br />
          File should not exceed 500kb.
        </div>
      </div>

      {/* Expanded White Logo Upload Section */}
      <div className={classes.logoSection}>
        <h2>Expanded White Logo</h2>
        <p>Upload an expanded white version of your logo for use in larger spaces in the AI embed.</p>

        <form className={classes.thumbnail} style={{ backgroundImage: `url(${expandedWhiteLogoFile || client.expandedWhiteLogoURL})` }}
          encType="multipart/form-data"
          onDragEnd={onDragExpandedFile} onDragLeave={onDragExpandedFile}
          onDragOver={onDragExpandedFile} onDragEnter={onDragExpandedFile} onDrop={onDropExpandedFile}
        >
          {!client.expandedWhiteLogoURL && <label className={classes.uploadText} htmlFor="expandedWhiteLogoUpload">
            <FontAwesomeIcon className={classes.icon} icon={['fas', 'photo-video']} /><br />

            Drag and Drop or<br />

            <span className={classes.link}>Select a File</span>
          </label>}
          {client.expandedWhiteLogoURL && <label htmlFor="expandedWhiteLogoUpload" >
            <Button text="Change" />
          </label>}

          <input className="hidden" id="expandedWhiteLogoUpload" type="file" name="expandedWhiteLogo[]" accept="image/*" onChange={onChangeExpandedLogoField} />
        </form>

        <div className={classes.thumbnailLabel}>
          Accepted file types: PNG, SVG and GIF.<br />
          File should not exceed 500kb.
        </div>
      </div>

      {/* Color Configuration Section */}
      <form className={classes.colours} method="post">
        <div className={classes.inputWithLabel}>
          <h2>Primary Color</h2>
          <p>The main color used for the background of the AI embed.</p>

          <img className={classes.thumbnail} src={EmbedPrimaryColor} alt="Embed Primary Color" />

          <div className={classes.input}>
            <input type="color" name="embedPrimaryColour" defaultValue={client.embedPrimaryColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="embedPrimaryColour" placeholder={clientEmbedColors.embedPrimaryColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2>Accent Color</h2>
          <p>Accent color used for certain highlights and errors in the AI embed.</p>

          <div className={classes.input}>
            <input type="color" name="embedAccentColour" defaultValue={client.embedAccentColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="embedAccentColour" placeholder={clientEmbedColors.embedAccentColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2>Progress Bar Color</h2>

          <img className={classes.thumbnail} src={EmbedProgressBarColor} alt="Embed Progress Bar Color" />

          <div className={classes.input}>
            <input type="color" name="embedProgressBarColour" defaultValue={client.embedProgressBarColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="embedProgressBarColour" placeholder={clientEmbedColors.embedProgressBarColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2>Button Color</h2>

          <img className={classes.thumbnail} src={EmbedButtonColor} alt="Embed Button Color" />

          <div className={classes.input}>
            <input type="color" name="embedButtonColour" defaultValue={client.embedButtonColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
            <input className={classes.label} type="text" name="embedButtonColour" placeholder={clientEmbedColors.embedButtonColour} onChange={onChangeColorField} maxLength={7} onKeyPress={validate} />
          </div>
        </div>


      </form>
    </div>
  )
}
