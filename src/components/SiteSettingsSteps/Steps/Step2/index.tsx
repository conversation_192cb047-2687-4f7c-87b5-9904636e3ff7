import React from "react"

import { ClientInterface } from 'interfaces/client.interfaces'

import classes from "./Step.module.scss"

interface fieldInterface {
  name: 'firstName' | 'lastName' | 'email' | 'websiteURL' | 'donationURL' | 'volunteerURL' | 'videoBannerURL' | 'customPagePath' | 'ngpVanApiKey' | 'ngpVanUsername' | 'isLocked' | 'aiQuestionsEnabled',
  value: string | boolean
}

const updateClientLink = function (c: ClientInterface): string {
  const pathname: string = c.name.toString().trim().replace(/[^a-z0-9]+/ig, '-')

  const subdomain = window.location.host.match('localhost') ? `staging` : (
    window.location.host.match('staging') ? `staging` : (
      `app`
    )
  );

  c.link = `https://${subdomain}.repd.us/${pathname}`

  return c.link
}

export default function Step2(props: { client: ClientInterface | any, setClient: Function }) {
  const { client, setClient } = props;

  function onChangeField(event: any) {
    if (['websiteURL', 'email', 'name'].includes(event.target.name) && !event.target.value) {
      event.target.value = client[event.target.name]

      return;
    }

    updateClient({
      name: event.target.name,
      value: event.target.value
    })
  }

  function updateClient({ name, value }: fieldInterface) {
    // client[name] = value
    // client.link = updateClientLink(client)

    setClient((prevClient: any) => {
      const updatedClient = {
        ...prevClient,
        [name]: value
      };
      return {
        ...updatedClient,
        link: updateClientLink(updatedClient)
      };
    });
  }

  function handleChangeIsLocked() {
    updateClient({ name: 'isLocked', value: !client.isLocked });
  }

  function handleChangeAiEnabled() {
    updateClient({ name: 'aiQuestionsEnabled', value: !client.aiQuestionsEnabled });
  }

  return (
    <div className={classes.Step}>
      <form className={classes.campaignDetails} method="post" action="">
        <div className={classes.inputWithLabel}>
          <div className={classes.checkbox}>
            <input type="checkbox" name="isLocked" defaultChecked={client.isLocked} onChange={() => handleChangeIsLocked()} />
            Password protected
          </div>

          <small>
            <i>Password required to enter the site.</i>
          </small>
        </div>

        <div className={classes.inputWithLabel}>
          <label>{client.clientType === 'Government' ? 'Full City Name*' : 'Official Campaign Name*'}</label>
          <input type="text" name="name" defaultValue={client.name} onChange={onChangeField} />
        </div>

        <div className={classes.inputWithLabel}>
          <label>{client.clientType === 'Government' ? 'Admin Email Address*' : 'Campaign Email Address*'}</label>
          <input type="email" name="email" defaultValue={client.email} onChange={onChangeField} />

          <small>
            <i>This will be used for donations and volunteers.</i>
          </small>
        </div>

        <div className={classes.inputWithLabel}>
          <label>{client.clientType === 'Government' ? 'City Website*' : 'Campaign Website*'}</label>
          <input type="text" name="websiteURL" defaultValue={client.websiteURL} placeholder="https://" onChange={onChangeField} />
        </div>

        {client.clientType !== 'Government' &&
          <div className={classes.inputWithLabel}>
            <label>"Paid for by" text</label>
            <input type="text" name="campaignName" defaultValue={client.campaignName} placeholder="Empty" onChange={onChangeField} />

            <small>
              <i>This will be shown in "Paid for by" text on the footer.</i>
            </small>
          </div>
        }

        <div className={classes.inputWithLabel}>
          <label>{client.clientType === 'Government' ? 'Right Action Button URL' : 'Donation Page URL'}</label>
          <input type="text" name="donationURL" defaultValue={client.donationURL} placeholder="https://" onChange={onChangeField} />
        </div>
        <div className={classes.inputWithLabel}>
          <label>{client.clientType === 'Government' ? 'Middle Action Button URL' : 'Volunteer Page URL'}</label>
          <input type="text" name="volunteerURL" defaultValue={client.volunteerURL} placeholder="https://" onChange={onChangeField} />

          {client.clientType !== 'Government' &&
            <small>
              <i>We build you a custom volunteer form.</i> <br />
              <i>However, if you'd like to drive to a specific volunteer page, add it here.</i>
            </small>
          }
        </div>
        <div className={classes.inputWithLabel}>
          <label>Video Banner URL</label>
          <input type="text" name="videoBannerURL" defaultValue={client.videoBannerURL} placeholder="https://" onChange={onChangeField} />
        </div>
        <div className={classes.inputWithLabel}>
          <label>Custom Page Path</label>
          <input type="text" name="customPagePath" defaultValue={client.customPagePath} placeholder="custom-path" onChange={onChangeField} />
        </div>

        {client.clientType !== 'Government' &&
          <>
            <div className={classes.inputWithLabel}>
              <label>NGP VAN Username</label>
              <input type="text" name="ngpVanUsername" placeholder="••••" defaultValue={client.ngpVanUsername} onChange={onChangeField} />
            </div>
            <div className={classes.inputWithLabel}>
              <label>NGP VAN API Key</label>
              <input type="text" name="ngpVanApiKey" placeholder="••••-••••-••••" defaultValue={client.ngpVanApiKey} onChange={onChangeField} />
            </div>
            <div className={classes.videoGuide}>
              <div className={classes.ngpInformation}>
                You can request an API key following NGP VAN's helpdesk article
                <a href="https://help.ngpvan.com/van/s/article/2969508-requesting-and-approving-api-keys" target="_blank"> here</a>
              </div>
            </div>
          </>
        }

        {(client.id == '15' || client.id == '211') && ( // DaveCavell & Arlington (Demo)
          <>
            <div className={classes.inputWithLabel}>
              <div className={classes.checkbox}>
                <input type="checkbox" name="aiQuestionsEnabled" defaultChecked={client.aiQuestionsEnabled} onChange={() => handleChangeAiEnabled()} />
                AI Enabled
              </div>

              <small>
                <i>Allow users access to Repd AI when asking questions.</i>
              </small>
            </div>

            {client.aiQuestionsEnabled &&
              <div className={classes.inputWithLabel}>
                <label>Polymorphic API Org Id</label>
                <input type="text" name="polymorphicOrgId" placeholder="••••-••••-••••" defaultValue={client.polymorphicOrgId} onChange={onChangeField} />
              </div>
            }
          </>
        )}
      </form>
    </div>
  )
}
