@import 'src/styles/variables';

.Step {
  .inputWithLabel {
    display: flex;
    flex-direction: column;
    margin: 20px 0 10px 0;

    label {
      color: black;
      font-weight: 600;
    }

    input {
      padding: 10px 10px;
      border-radius: 5px;
      border: 1px solid $grey;
      appearance: none;
      font-size: 16px;
    }

    small {
      padding-top: 5px;
      padding-bottom: 10px;
    }

    .checkbox {
      display: flex;
      flex-direction: row;
      margin-top: 10px;
      font-weight: 600;
      color: black;

      input[type=checkbox] {
        margin-right: 5px;
        appearance: auto;
      }
    }
  }

  .videoGuide {
    // .ngpInformation {}

    a {
      color: $blue;
    }
  }
}