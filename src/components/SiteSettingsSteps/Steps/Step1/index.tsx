import { useState } from "react"
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

import { useNotification } from "hooks";
import { ClientInterface } from 'interfaces/client.interfaces'
import { FileService, AuthService } from 'services'

import Button from 'shared/Button';

import classes from "./Step.module.scss"

export default function Step1(props: { client: ClientInterface, setClient: Function }) {
  const { showAlert } = useNotification();
  const { client, setClient } = props;

  const [imageFile, setImageFile] = useState('')

  const authService = new AuthService()
  const fileService = new FileService(authService.getToken(), client.id)

  client.topBarColour ||= '#012B74'
  client.videoLinksColour ||= '#4975E9'
  client.newQuestionColour ||= '#29B36E'
  client.plusAskPillColour ||= '#29B36E'
  client.openQuestionsAndAnswersColour ||= '#0E509B'

  // AI Embed Design defaults
  client.embedPrimaryColour ||= '#2760CF'
  client.embedAccentColour ||= '#E94E77'
  client.embedProgressBarColour ||= '#2760CF'
  client.embedButtonColour ||= '#E94E77'

  function onDragFile(event: any) {
    event.preventDefault();
    event.stopPropagation();
  }

  function onDropFile(event: any) {
    event.preventDefault();

    updateClient(event.dataTransfer.files);
  }

  function onChangeField(event: any) {
    if (event?.target?.files)
      updateClient(event.target.files);
    else
      console.error('Site Settings - Step 4 - File not added', event?.target?.files);
  }

  function updateClient(files: any) {
    const imageFileData: Blob = files[0];

    const formData = new FormData();
    const reader = new FileReader();

    reader.readAsDataURL(imageFileData);
    reader.onload = function (event) {
      if (event.target?.result)
        setImageFile(event.target.result + '');
    };

    formData.append("file", imageFileData);

    fileService.uploadImage(formData).then((data: any) => {
      const logoURL = data.url;

      // setClient(client)

      setClient((prevClient: any) => ({
        ...prevClient,
        logoURL,
      }));

      showAlert("The logo is uploaded successfully, you can save the client now.")
    })
  }

  return (
    <div className={classes.Step}>
      <form className={classes.thumbnail} style={{ backgroundImage: `url(${imageFile || client.logoURL})` }}
        encType="multipart/form-data"
        onDragEnd={onDragFile} onDragLeave={onDragFile}
        onDragOver={onDragFile} onDragEnter={onDragFile} onDrop={onDropFile}
      >
        {!client.logoURL && <label className={classes.uploadText} htmlFor="clientLogo">
          <FontAwesomeIcon className={classes.icon} icon={['fas', 'photo-video']} /><br />

          Drag and Drop or<br />

          <span className={classes.link}>Select a File</span>
        </label>}
        {client.logoURL && <label htmlFor="clientLogo" >
          <Button text="Change" />
        </label>}

        <input className="hidden" id="clientLogo" type="file" name="avatar[]" accept="image/*" onChange={onChangeField} />
      </form>

      <div className={classes.thumbnailLabel}>
        Accepted file types: PNG, SVG and GIF.<br />
        File should not exceed 500kb.
      </div>
    </div>
  )
}