@import 'src/styles/variables';

.Step {
  .thumbnail {
    position: relative;
    padding: 20px;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background-color: $pale-blue;
    background-position: 50% calc(50% - 30px);
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 5px;
    text-align: center;
    cursor: pointer;
    overflow: hidden;

    .uploadText {
      display: block;
      padding: 10px 0;
      color: $blue;
      font-weight: 300;
      cursor: pointer;
      text-align: center;

      * {
        cursor: pointer;
      }

      svg,
      .icon {
        transform: scale(4);
        margin: 40px 0;
      }

      .link {
        color: $dark-blue;
        font-weight: 700;
      }

      &:hover {
        color: $dark-blue;

        .link {
          color: $blue;
        }
      }
    }

    label {
      cursor: pointer;
    }

    button {
      width: 170px;
      margin: 300px auto 0 auto;
      border: none;
      box-shadow: 0 0 20px 0 $transparent-black-low;
      pointer-events: none;
    }
  }

  .thumbnailLabel {
    margin-top: 20px;
    text-align: center;
  }
}