import { useEffect, useState } from "react"

import { ClientInterface, TextOptionsInterface } from 'interfaces/client.interfaces'

import classes from "./Step.module.scss"

import headerHome from 'assets/platform/edit-client/custom-language/header-home.jpg';
import headerDonate from 'assets/platform/edit-client/custom-language/header-donate.png';
import headerVolunteer from 'assets/platform/edit-client/custom-language/header-volunteer.png';
import videoDonateButton from 'assets/platform/edit-client/custom-language/video-donate-button.png';
import videoDonateText from 'assets/platform/edit-client/custom-language/video-donate-text.png';
import postQuestionButton from 'assets/platform/edit-client/custom-language/post-question-button.png';
import postQuestionText from 'assets/platform/edit-client/custom-language/post-question-text.png';
import emailDonateButton from 'assets/platform/edit-client/custom-language/email-donate-button.png';
import PostVideoSurveyTexts from 'assets/platform/edit-client/Post Video Survey Texts.png';
import QuestionExpectancy from 'assets/platform/edit-client/custom-language/question-expectancy.png';
import aiPrompt from 'assets/platform/edit-client/custom-language/ai-prompt.png';
import aiQuestions from 'assets/platform/edit-client/custom-language/ai-questions.jpg';

type textOptions = 'questionExpectancy' | 'embedTitleText'

type customTextType = 'headerHomeLinkText' | 'headerDonateLinkText' | 'headerVolunteerLinkText' | 'emailDonateCtaText' |
  'postQuestionText' | 'postQuestionBtnText' | 'donateText' | 'donateCtaText' |
  'aiPromptDesktop' | 'aiPromptMobile' | 'aiDefaultQuestion1' | 'aiDefaultQuestion2' | 'aiDefaultQuestion3';

interface clientTextTypesInterface {
  headerHomeLinkText: string;
  headerDonateLinkText: string;
  headerVolunteerLinkText: string;
  emailDonateCtaText: string;
  postQuestionText: string;
  postQuestionBtnText: string;
  donateText: string;
  donateCtaText: string;
  questionExpectancy?: string;
  aiPromptDesktop: string;
  aiPromptMobile: string;
  aiDefaultQuestion1: string;
  aiDefaultQuestion2: string;
  aiDefaultQuestion3: string;
  embedTitleText?: string;
}

interface fieldInterface {
  name: customTextType | textOptions;
  value: string;
}

const getTextTypes = (client?: ClientInterface): clientTextTypesInterface => {
  return {
    headerHomeLinkText: client?.headerHomeLinkText || 'Home link text',
    headerDonateLinkText: client?.headerDonateLinkText || 'Donate link text',
    headerVolunteerLinkText: client?.headerVolunteerLinkText || 'Volunteer link text',
    emailDonateCtaText: client?.emailDonateCtaText || 'Email donate button text',
    postQuestionText: client?.postQuestionText || 'Have something to ask? text',
    postQuestionBtnText: client?.postQuestionBtnText || 'Post question button text',
    donateText: client?.donateText || 'Video bar donate text',
    donateCtaText: client?.donateCtaText || 'Video bar donate button text',
    questionExpectancy: client?.textOptions?.questionExpectancy || 'You can expect a response within 3 - 5 business days"?',
    aiPromptDesktop: client?.textOptions?.aiPromptDesktop || 'Ask City Hall (e.g. How do I register to vote?)',
    aiPromptMobile: client?.textOptions?.aiPromptMobile || 'Ask City Hall',
    aiDefaultQuestion1: client?.textOptions?.aiDefaultQuestion1 || 'How do I register to vote?',
    aiDefaultQuestion2: client?.textOptions?.aiDefaultQuestion2 || 'What is the City Hall address?',
    aiDefaultQuestion3: client?.textOptions?.aiDefaultQuestion3 || 'What is the City Hall phone',
    embedTitleText: client?.textOptions?.embedTitleText || 'Embed Title Text'
  }
}

export default function Step5(props: { client: ClientInterface, setClient: Function }) {
  const { client, setClient } = props;

  const [clientTextTypes, setClientTextTypes]: [
    clientTextTypesInterface, (v: clientTextTypesInterface) => void
  ] = useState(getTextTypes(client))

  const updateTextTypes = () => {
    const textTypes: clientTextTypesInterface = getTextTypes(client)

    setClientTextTypes(textTypes)
  }

  function validate(event: any) {
    // if (event.key.match(/[0-9a-zA-Z#]/) === null)
    //   event.preventDefault();
  }

  function onChangeField(event: any) {
    updateClient({
      name: event.target.name,
      value: event.target.value
    })
  }

  function onChangeTextField(event: any) {
    const textOptions = client['textOptions'] || {} as TextOptionsInterface;

    if (event.target.value) {
      textOptions[event.target.name as keyof TextOptionsInterface] = event.target.value;
    } else {
      delete textOptions[event.target.name as keyof TextOptionsInterface]
    }

    client['textOptions'] = textOptions;

    setClient((prevClient: any) => ({
      ...prevClient,
      textOptions,
    }));
  }

  function updateClient({ name, value }: fieldInterface) {
    // client[name as customTextType] = value;

    setClient((prevClient: any) => ({
      ...prevClient,
      [name]: value,
    }));
    updateTextTypes()
  }

  // When a toc item is clicked, scroll to the corresponding section
  // useEffect(() => {
  //   const links = document.querySelectorAll('.toc a');
  //
  //   links.forEach((link: any) => {
  //     link.addEventListener('click', (e: any) => {
  //       e.preventDefault();
  //
  //       const href = link.getAttribute('href');
  //       const target = document.querySelector(href);
  //
  //       if (target) {
  //         target.scrollIntoView({
  //           behavior: 'smooth',
  //           block: 'start',
  //         });
  //       }
  //     });
  //   });
  // }, []);

  useEffect(updateTextTypes, [client])

  return (
    <div className={classes.Step}>
      {/* Fixed table of contents */}
      <div className={classes.toc}>
        <h2>Table of Contents</h2>
        <ul>
          <li><a href="#home-link-text">Home Link Text</a></li>
          <li><a href="#volunteer-link-text">Middle Action Button Text</a></li>
          <li><a href="#donate-link-text">Right Action Button Text</a></li>
          <li><a href="#email-donate-cta-text">Email Action Button Text</a></li>
          <li><a href="#post-question-text">Have something to ask? Text</a></li>
          <li><a href="#post-question-btn-text">Post Question Button Text</a></li>
          <li><a href="#donate-text">Video Action Bar Text</a></li>
          <li><a href="#donate-cta-text">Video Action Button Text</a></li>
          <li><a href="#question-expectancy">Answer Expectancy</a></li>
          <li><a href="#embed-title-text">Embed Title Text</a></li>
          <li><a href="#ai-prompt">Custom AI Presentation & Questions</a></li>
          <li><a href="#post-video-survey-texts">Post-Video Survey Texts</a></li>
        </ul>
      </div>

      <form className={classes.colours} method="post">
        {/* <div className={classes.inputWithLabel}>
          <h2 id="home-link-text">Home Link Text</h2>

          <img className={classes.thumbnail} src={headerHome} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="headerHomeLinkText" placeholder={clientTextTypes.headerHomeLinkText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div> */}

        <div className={classes.inputWithLabel}>
          <h2 id="volunteer-link-text">{client.clientType === 'Government' ? 'Middle Action Button Text' : 'Volunteer Link Text'}</h2>

          <img className={classes.thumbnail} src={headerVolunteer} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="headerVolunteerLinkText" placeholder={clientTextTypes.headerVolunteerLinkText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="donate-link-text">{client.clientType === 'Government' ? 'Right Action Button Text' : 'Donate Link Text'}</h2>

          <img className={classes.thumbnail} src={headerDonate} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="headerDonateLinkText" placeholder={clientTextTypes.headerDonateLinkText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="email-donate-cta-text">{client.clientType === 'Government' ? 'Email Action Button Text' : 'Email Donate Button Text'}</h2>

          <img className={classes.thumbnail} src={emailDonateButton} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="emailDonateCtaText" placeholder={clientTextTypes.emailDonateCtaText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="post-question-text">Have something to ask? Text</h2>

          <img className={classes.thumbnail} src={postQuestionText} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="postQuestionText" placeholder={clientTextTypes.postQuestionText} onChange={onChangeField} maxLength={80} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="post-question-btn-text">Post Question Button Text</h2>

          <img className={classes.thumbnail} src={postQuestionButton} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="postQuestionBtnText" placeholder={clientTextTypes.postQuestionBtnText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="donate-text">{client.clientType === 'Government' ? 'Video Action Bar Text' : 'Video Bar Donate Text'}</h2>

          <img className={classes.thumbnail} src={videoDonateText} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="donateText" placeholder={clientTextTypes.donateText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="donate-cta-text">{client.clientType === 'Government' ? 'Video Action Button Text' : 'Video Bar Donate Button Text'}</h2>

          <img className={classes.thumbnail} src={videoDonateButton} alt="Top Bar" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="donateCtaText" placeholder={clientTextTypes.donateCtaText} onChange={onChangeField} maxLength={40} onKeyPress={validate} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="question-expectancy">Answer Expectancy</h2>

          <img className={classes.thumbnail} src={QuestionExpectancy} alt="Answer Expectancy" />

          <div className={classes.input}>
            <input className={classes.label} type="text" name="questionExpectancy" placeholder={client.textOptions?.questionExpectancy} onChange={onChangeTextField} maxLength={255} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="embed-title-text">Embed Title Text</h2>

          <div className={classes.input}>
            <input className={classes.label} type="text" name="embedTitleText" placeholder={client.textOptions?.embedTitleText || 'Embed Title Text'} onChange={onChangeTextField} maxLength={255} />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="ai-prompt">Custom AI Presentation & Questions</h2>

          <img className={classes.thumbnail} src={aiPrompt} alt="AI Prompt" />
          <img className={classes.thumbnail} src={aiQuestions} alt="AI Prompt Questions" />

          <div className={classes.inputBox}>
            <label>Desktop AI Bar Message</label>
            <input
              name="aiPromptDesktop"
              defaultValue={client.textOptions?.aiPromptDesktop}
              placeholder={'Ask City Hall (e.g. How do I register to vote?)'}
              onChange={onChangeTextField}
              maxLength={64}
            />

            <label>Mobile AI Bar Message</label>
            <input
              name="aiPromptMobile"
              defaultValue={client.textOptions?.aiPromptMobile}
              placeholder={'Ask City Hall'}
              onChange={onChangeTextField}
              maxLength={64}
            />

            <label>Default Question #1</label>
            <input
              name="aiDefaultQuestion1"
              defaultValue={client.textOptions?.aiDefaultQuestion1}
              placeholder={'How do I register to vote?'}
              onChange={onChangeTextField}
              maxLength={64}
            />

            <label>Default Question #2</label>
            <input
              name="aiDefaultQuestion2"
              defaultValue={client.textOptions?.aiDefaultQuestion2}
              placeholder={'What is the City Hall address?'}
              onChange={onChangeTextField}
              maxLength={64}
            />

            <label>Default Question #3</label>
            <input
              name="aiDefaultQuestion3"
              defaultValue={client.textOptions?.aiDefaultQuestion3}
              placeholder={'What is the City Hall phone number?'}
              onChange={onChangeTextField}
              maxLength={64}
            />
          </div>
        </div>

        <div className={classes.inputWithLabel}>
          <h2 id="post-video-survey-texts">Post-Video Survey Texts</h2>

          <img className={classes.thumbnail} src={PostVideoSurveyTexts} alt="Video Links" />

          <div className={classes.inputBox}>
            <label>Question Text</label>
            <input
              name="postVideoSurveyQuestion"
              defaultValue={client.textOptions?.postVideoSurveyQuestion}
              placeholder={`Does this answer make you more likely to support ${client.name}?`}
              onChange={onChangeTextField}
              maxLength={128}
            />

            <label>Low Score Text</label>
            <input
              name="postVideoSurveyLessLikely"
              defaultValue={client.textOptions?.postVideoSurveyLessLikely}
              placeholder={'Less Likely'}
              onChange={onChangeTextField}
              maxLength={64}
            />

            {/* <label>Neutral Score Text</label>
            <input
              name="postVideoSurveyNeutral"
              defaultValue={client.textOptions?.postVideoSurveyNeutral}
              placeholder={'Neutral'}
              onChange={onChangeTextField}
              maxLength={64}
            /> */}

            <label>High Score Text</label>
            <input
              name="postVideoSurveyMoreLikely"
              defaultValue={client.textOptions?.postVideoSurveyMoreLikely}
              placeholder={'More Likely'}
              onChange={onChangeTextField}
              maxLength={64}
            />
          </div>
        </div>
      </form>
    </div>
  )
}
