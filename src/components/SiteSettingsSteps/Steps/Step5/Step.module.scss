@import 'src/styles/variables';

// Fixed table of contents
.toc {
  position: fixed;
  top: 160px;
  left: 10px;
  background-color: #FFFFFF55;
  z-index: 100;
  padding: 20px 0;
  border-radius: 10px;
  opacity: 0.5;

  ul {
    display: flex;
    flex-direction: column;
    justify-content: center;
    list-style-type: none;
    margin: 0;
    padding: 0;

    li {
      margin: 0 10px;
      font-size: 18px;
      font-weight: 600;
      color: $blue;
      cursor: pointer;

      &:hover {
        color: $dark-blue;
      }
    }
  }
}

.Step {
  .inputWithLabel {
    margin: 0 0 50px 0;

    h2 {
      margin-block-start: 15px;
      margin-block-end: 15px;
      font-size: 24px;
      font-weight: bold;
    }

    .thumbnail {
      position: relative;
      padding: 5px;
      margin-top: 20px;
      margin-bottom: -15px;
      min-height: 0;
      width: 100%;
      background-color: $pale-blue;
      border-radius: 5px;
      text-align: center;
      overflow: hidden;

      &:hover {
        background-color: $blue;
      }
    }

    .inputBox {
      display: flex;
      align-items: start;
      flex-direction: column;
      margin-top: 10px;
      padding: 10px 10px;
      width: 100%;
      border-radius: 5px;
      border: 1px solid $grey;
      font-size: 16px;

      label {
        margin-top: 10px;
        color: black;
        font-weight: 600;
      }

      input {
        padding: 10px 10px;
        border-radius: 5px;
        border: 1px solid $grey;
        width: 100%;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .input {
      padding: 10px 10px;
      display: flex;
      align-items: center;
      margin-top: 10px;
      padding: 10px 10px;
      width: 100%;
      border-radius: 5px;
      border: 1px solid $grey;

      input.label {
        padding: 10px 10px;
        border-radius: 5px;
        border: 1px solid $grey;
        font-size: 18px;
        font-weight: 600;
        width: 100%;
      }
    }
  }
}

@media screen and (max-width: 640px) {
  .toc {
    display: none;
  }
}