import { ReactNode, useState } from "react"
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import cn from 'classnames'

import { ClientInterface } from 'interfaces/client.interfaces'

import Step1 from "./Steps/Step1"
import Step2 from "./Steps/Step2"
import Step3 from "./Steps/Step3"
import Step4 from "./Steps/Step4"
import Step5 from "./Steps/Step5"
import Step6 from "./Steps/Step6"

import classes from "./SiteSettingsSteps.module.scss"

interface FormStepProps {
  children: ReactNode;
  title: string;
  step: number;
  opened: Boolean;
  saved: Boolean;
  handleClose: () => void
  handleOpen: () => void
}

function FormStep({ children, title, step, opened, saved, handleClose, handleOpen }: FormStepProps) {
  function handleClick() {
    if (opened) handleClose()
    else handleOpen()
  }

  return (
    <div className={classes.formStep}>
      <div className={classes.stepLabel} onClick={handleClick}>
        <div className={classes.stepTitle}>
          <FontAwesomeIcon icon={'check'} className={cn(classes.checkIcon, saved ? classes.active : classes.inactive)} />

          <p>Step {step}</p>

          <FontAwesomeIcon
            icon={opened ? 'chevron-down' : 'chevron-right'}
            className={classes.arrowIcon}
          />
        </div>
        <div className={classes.stepLabel}>{title}</div>
      </div>

      {opened ? (
        <div className={classes.stepContent}>
          {children}
        </div>
      ) : null}
    </div>
  )
}

interface SiteSettingProps {
  client: ClientInterface,
  savedIndex: number,
  setClient: Function,
  setStep: Function
}

export default function SiteSettingsSteps(props: SiteSettingProps) {
  const { client, savedIndex, setClient, setStep } = props;

  const [openIndex, setIsOpen] = useState(0)

  const handleClose = () => setIsOpen(-1)
  const handleOpen = (index: number) => {
    setIsOpen(index)
    setStep(index + 1)
  }

  const formSteps = [
    { title: "Logo/Brand" },
    { title: client.clientType === 'Government' ? "City Details" : "Campaign Details" },
    { title: "Categories" },
    { title: "Website Design" },
    { title: "AI Embed Design" },
    { title: "Language Customization" }
  ]

  return (
    <div className={classes.SettingsSteps}>
      <div className={classes.formWithSteps}>
        {formSteps.map((step, index) => {
          var opened = openIndex === index,
            saved = openIndex === savedIndex && openIndex === index

          if (saved) handleOpen(index + 1)

          saved = savedIndex === index

          return <FormStep key={index}
            title={step.title}
            step={index + 1}
            opened={opened}
            saved={saved}
            handleClose={handleClose}
            handleOpen={() => handleOpen(index)}
          >
            {index === 0 && <Step1 client={client} setClient={setClient} />}
            {index === 1 && <Step2 client={client} setClient={setClient} />}
            {index === 2 && <Step3 client={client} setClient={setClient} />}
            {index === 3 && <Step4 client={client} setClient={setClient} />}
            {index === 4 && <Step6 client={client} setClient={setClient} />}
            {index === 5 && <Step5 client={client} setClient={setClient} />}
          </FormStep>
        })}
      </div>
    </div>
  )
}