@import 'src/styles/variables';

.SettingsSteps {
  max-width: 480px;
  margin: auto;

  .formWithSteps {
    .formStep {
      border-bottom: 1px solid $light-grey;
      padding-bottom: 20px;
      margin-bottom: 20px;

      .stepLabel {
        color: $blue;
        cursor: pointer;

        .stepTitle {
          display: flex;
          align-items: center;
          position: relative;
        }

        .checkIcon {
          position: relative;
          display: none;
          height: 0;
          width: 0;
          border-radius: 100%;
          padding: 10px;
          background: $pale-mint-green;
          color: $dark-mint-green;
          transition: width 1s, opacity 2s;
          opacity: 0;

          &.active {
            top: 2px;
            left: -50px;
            display: block;
            margin-right: -40px;
            margin-bottom: -20px;
            height: 40px;
            width: 40px;
            opacity: 1;
          }
        }

        .arrowIcon {
          position: absolute;
          right: 0;
          color: $light-grey;
        }
      }
    }
  }
}