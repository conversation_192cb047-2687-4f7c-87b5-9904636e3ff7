export default function BusyIndicator({ message = "Loading..." }) {
  return (
    <div className="inline-flex items-center justify-center space-x-2">
      <svg
        className="animate-spin h-5 w-5 text-blue-600"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.312 1.344 6.315 3.514 8.485l2.486-2.194z"
        ></path>
      </svg>
      <span className="text-sm text-gray-500">{message}</span>
    </div>
  );
}
