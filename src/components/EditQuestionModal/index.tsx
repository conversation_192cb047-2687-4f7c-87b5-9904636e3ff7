import React, { useState } from "react";
import Modal from "../../shared/Modal";
import Button from "../../shared/Button";
import { QuestionInterface } from "../../interfaces";
import { QuestionService } from "../../services";
import { useServiceContext } from "../../services/ServiceProvider";
import styles from "./EditQuestionModal.module.scss";

interface EditQuestionModalProps {
  isOpen: boolean;
  question: QuestionInterface;
  questionService?: QuestionService;
  onClose: () => void;
  onSave: (updatedQuestion: QuestionInterface) => void;
  onProceedWithAnswer: () => void;
}

const EditQuestionModal: React.FC<EditQuestionModalProps> = ({
  isOpen,
  question,
  questionService,
  onClose,
  onSave,
  onProceedWithAnswer,
}) => {
  const { adminStatsService } = useServiceContext();
  const [questionText, setQuestionText] = useState(question.text);
  const [isUpdating, setIsUpdating] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleSave = async () => {
    if (!questionService || !questionText.trim()) {
      setHasError(true);
      return;
    }

    setIsUpdating(true);
    setHasError(false);

    try {
      await questionService.updateQuestionText(
        { id: question.id, text: questionText.trim() },
        (updatedQuestionData: any) => {
          // The callback receives the updated question data directly
          const updatedQuestion = updatedQuestionData || {
            ...question,
            text: questionText.trim()
          };

          onSave(updatedQuestion);
          adminStatsService?.trackEvent('EditQuestionModal', 'save_question_edit');
          onProceedWithAnswer();
        }
      );
    } catch (error) {
      console.error('Failed to update question:', error);
      setHasError(true);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSkipEdit = () => {
    adminStatsService?.trackEvent('EditQuestionModal', 'skip_question_edit');
    onProceedWithAnswer();
  };

  const handleCancel = () => {
    adminStatsService?.trackEvent('EditQuestionModal', 'cancel_question_edit');
    onClose();
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setQuestionText(e.target.value);
    if (hasError) {
      setHasError(false);
    }
  };

  const handleTextFocus = () => {
    if (hasError) {
      setHasError(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Modal contentWrapperStyle={{ maxWidth: "600px", padding: "0" }}>
      <div className={styles.editQuestionModal}>
        <div className={styles.header}>
          <h2 className={styles.title}>Edit Question</h2>
          <button
            className={styles.closeButton}
            onClick={handleCancel}
            aria-label="Close modal"
          >
            ×
          </button>
        </div>

        <div className={styles.content}>
          <p className={styles.description}>
            You can edit this question before answering it, or proceed with the original question.
          </p>

          <div className={styles.inputContainer}>
            <label htmlFor="questionText" className={styles.label}>
              Question Text
            </label>
            <textarea
              id="questionText"
              className={`${styles.textarea} ${hasError ? styles.error : ''}`}
              value={questionText}
              onChange={handleTextChange}
              onFocus={handleTextFocus}
              rows={4}
              maxLength={500}
              placeholder="Enter your question..."
            />
            {hasError && (
              <div className={styles.errorText}>
                Please enter a valid question.
              </div>
            )}
            <div className={styles.characterCount}>
              {questionText.length}/500 characters
            </div>
          </div>
        </div>

        <div className={styles.buttonContainer}>
          <Button
            text="Cancel"
            customClass={styles.cancelButton}
            callback={handleCancel}
          />
          <Button
            text="Skip"
            customClass={styles.skipButton}
            callback={handleSkipEdit}
          />
          <Button
            text={isUpdating ? "Saving..." : "Save & Answer"}
            customClass={styles.saveButton}
            callback={handleSave}
            disabled={isUpdating}
          />
        </div>
      </div>
    </Modal>
  );
};

export default EditQuestionModal;
