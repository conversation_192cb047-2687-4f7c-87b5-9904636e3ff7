.editQuestionModal {
  background: white;
  border-radius: 8px;
  padding: 0;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 24px 24px;
}

.title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  transition: color 0.2s ease;

  &:hover {
    color: #374151;
  }
}

.content {
  padding: 0 24px;
}

.description {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.inputContainer {
  margin-bottom: 24px;
}

.label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &.error {
    border-color: #ef4444;

    &:focus {
      border-color: #ef4444;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }
}

.errorText {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.characterCount {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
  margin-top: 4px;
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  border-radius: 0 0 8px 8px;
}

.cancelButton {
  background-color: white;
  color: #6b7280;
  border: 1px solid #d1d5db;

  &:hover {
    background-color: #f9fafb;
    color: #374151;
  }
}

.skipButton {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;

  &:hover {
    background-color: #e5e7eb;
  }
}

.saveButton {
  background-color: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;

  &:hover {
    background-color: #2563eb;
  }

  &:disabled {
    background-color: #9ca3af;
    border-color: #9ca3af;
    cursor: not-allowed;
  }
}

// Responsive design
@media (max-width: 640px) {
  .editQuestionModal {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .header {
    padding: 16px 16px 0 16px;
  }

  .content {
    padding: 0 16px;
  }

  .buttonContainer {
    padding: 16px;
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}