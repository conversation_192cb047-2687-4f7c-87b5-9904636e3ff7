import {
    Select as SelectComponent,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "shared/ui/select"

export function Select(props: {
    items: string[];
    placeholder: string;
    selected: string | null;
    setSelected: (value: string) => void;
}) {
    return (
        <SelectComponent onValueChange={(value) => props.setSelected(value)}>
            <SelectTrigger className="w-[180px] border-none bg-white">
                <SelectValue placeholder={props.placeholder} />
            </SelectTrigger>
            <SelectContent className="z-[9999]">
                <SelectGroup>
                    {props.items.map((item) => (
                        <SelectItem
                            key={item}
                            value={item}
                        >
                            {item}
                        </SelectItem>
                    ))}
                </SelectGroup>
            </SelectContent>
        </SelectComponent>
    )
}