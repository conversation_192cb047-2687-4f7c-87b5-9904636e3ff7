// TODO: Remove - not used
import React from "react";
import { useVideoCapture } from "./videoCaptureContext";

const Teleprompter: React.FC = () => {
  const { teleprompterNotes, setTeleprompterNotes, setDoneTelepromt } =
    useVideoCapture();

  return (
    <div className="w-full h-full flex flex-col items-center justify-center bg-gray-200 p-6">
      <textarea
        value={teleprompterNotes || ""}
        onChange={(e) => setTeleprompterNotes(e.target.value)}
        placeholder="Enter teleprompter notes here"
        className="w-full p-2 rounded-md mb-4 focus:border-2 border-blue-500 focus:border-dashed focus:outline-none"
        rows={10}
      />
      <button
        onClick={() => setDoneTelepromt(true)}
        className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
      >
        Next
      </button>
    </div>
  );
};

export default Teleprompter;
