import { useVideoCapture } from "./videoCaptureContext";
import { useServiceContext } from "services/ServiceProvider";

export default function CaptureOptions() {
  const { setCaptureOption } = useVideoCapture();
  const { adminStatsService } = useServiceContext();
  return (
    <div className="flex flex-col items-center justify-center w-full h-full bg-gray-100 p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        Choose an Option
      </h2>
      <div className="flex space-x-4">
        <button
          onClick={() => {
            setCaptureOption("upload");
            adminStatsService?.trackEvent('QuestionsArchive', 'select_upload_video');
          }}
          className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
        >
          Upload Video
        </button>
        <button
          onClick={() => {
            setCaptureOption("record");
            adminStatsService?.trackEvent('QuestionsArchive', 'select_record_video');
          }}
          className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
        >
          Record Video
        </button>
      </div>
    </div>
  );
}
