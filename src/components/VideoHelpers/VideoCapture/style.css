.teleprompter-text {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.teleprompter-text.scrolling {
    -webkit-animation: scroll 40s linear infinite;
    animation: scroll 40s linear infinite;
}

@-webkit-keyframes scroll {
    0% {
        -webkit-transform: translateY(0%);
        transform: translateY(0%);
    }
    100% {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
}

@keyframes scroll {
    0% {
        -webkit-transform: translateY(0%);
        transform: translateY(0%);
    }
    100% {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
}

/* iOS-style slider with ticks */
.slider-container {
  position: relative;
  width: 120px;
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  height: 30px;
  width: 100%;
  background: #f0f0f0;
  border-radius: 15px;
  outline: none;
  padding: 0 5px;
  margin: 0;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  border: 1px solid #e0e0e0;
  z-index: 2;
  position: relative;
}

.slider::-moz-range-thumb {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  border: 1px solid #e0e0e0;
  z-index: 2;
  position: relative;
}

.slider-ticks {
  display: flex;
  justify-content: space-between;
  padding: 0 14px;
  position: absolute;
  top: calc(50% - 5px);
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
}

.tick {
  width: 2px;
  height: 12px;
  background-color: #ccc;
  display: inline-block;
}
