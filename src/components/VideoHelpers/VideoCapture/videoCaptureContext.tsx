import React, { createContext, useState, useContext, ReactNode } from "react";
import { SetState } from "../../../shared/types";

interface VideoCaptureContextType {
  captureOption: "upload" | "record" | null;
  capturedFile: File | null;
  videoNotes: string;
  captureStatus: "idle" | "capturing" | "completed" | "error";
  teleprompterNotes: string;
  setCaptureOption: (option: "upload" | "record" | null) => void;
  doneTelepromt: boolean;
  setDoneTelepromt: (option: boolean) => void;
  setCapturedFile: (file: File | null) => void;
  setVideoNotes: (notes: string) => void;
  setCaptureStatus: (
    status: "idle" | "capturing" | "completed" | "error"
  ) => void;
  setTeleprompterNotes: SetState<string>;
}

const VideoCaptureContext = createContext<VideoCaptureContextType | undefined>(
  undefined
);

const VideoCaptureProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [captureOption, setCaptureOption] = useState<
    "upload" | "record" | null
  >(null);
  const [capturedFile, setCapturedFile] = useState<File | null>(null);
  const [videoNotes, setVideoNotes] = useState<string>("");
  const [captureStatus, setCaptureStatus] = useState<
    "idle" | "capturing" | "completed" | "error"
  >("idle");
  const [doneTelepromt, setDoneTelepromt] = useState<boolean>(false);
  const [teleprompterNotes, setTeleprompterNotes] = useState<string>("");

  return (
    <VideoCaptureContext.Provider
      value={{
        captureOption,
        capturedFile,
        videoNotes,
        captureStatus,
        teleprompterNotes,
        doneTelepromt,
        setDoneTelepromt,
        setCaptureOption,
        setCapturedFile,
        setVideoNotes,
        setCaptureStatus,
        setTeleprompterNotes,
      }}
    >
      {children}
    </VideoCaptureContext.Provider>
  );
};

const useVideoCapture = (): VideoCaptureContextType => {
  const context = useContext(VideoCaptureContext);
  if (context === undefined) {
    throw new Error(
      "useVideoCapture must be used within a VideoCaptureProvider"
    );
  }
  return context;
};

export { VideoCaptureProvider, useVideoCapture };
