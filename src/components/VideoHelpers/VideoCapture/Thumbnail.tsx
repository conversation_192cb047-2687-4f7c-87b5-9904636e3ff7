import React from "react";

type Props = {
  selectedThumbnail: any;
  url: any;
  handleThumbnailClick: any;
};
export default function Thumbnail({
  selectedThumbnail,
  url,
  handleThumbnailClick,
}: Props) {
  return (
    <div
      className={`flex flex-col items-center justify-center w-60 h-32 cursor-pointer rounded-lg z-10 ${
        selectedThumbnail === url ? "border-4 border-blue-500" : ""
      }`}
      onClick={() => handleThumbnailClick(url)}
    >
      <img
        src={url}
        alt={`Thumbnail`}
        style={{ height: "120px" }}
        className={`mx-auto`}
      />
    </div>
  );
}
