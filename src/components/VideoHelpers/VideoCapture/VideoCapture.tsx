import CaptureOptions from "./CaptureOptions";
import { useVideoCapture } from "./videoCaptureContext";
import VideoRecorder from "./VideoRecorder";
import VideoInput from "../VideoInput";
import { useVideoUploadContext } from "../VideoUpload/VideoUploadProvider";
import TeleprompterNotes from "../../TeleprompterNotes";
import classesFile from "../../Answer/Answer.module.scss";
import { ClientService } from "services";
import * as interfaces from "interfaces";
import { useEffect, useState } from "react";
import { useServiceContext } from "services/ServiceProvider";
const classes: any = classesFile;

type Props = {
  clientService?: ClientService;
  notes?: string;
};

export default function VideoCapture({ clientService, notes }: Props) {
  // console.log("VIDEO CAPTURE COMPONENT");

  const {
    captureOption,
    doneTelepromt,
    teleprompterNotes,
    setTeleprompterNotes,
    setDoneTelepromt,
  } = useVideoCapture();

  const { file, setFile, question } = useVideoUploadContext();
  const { questionService } = useServiceContext();

  if (!teleprompterNotes && notes) setTeleprompterNotes(notes);

  const [transcripts, setTranscripts]: interfaces.TranscriptInterface[] | any =
    useState([]);

  const fetchTranscriptList = () => {
    questionService?.getTranscripts(setTranscripts);
  };

  useEffect(() => {
    if (questionService && transcripts.length === 0) {
      fetchTranscriptList();
    }
  }, [questionService]);

  const nextStepHandler = () => {
    navigator.mediaDevices.getUserMedia({ audio: true, video: true })
      .then(() => setDoneTelepromt(true))
      .catch((err) => console.log(err));
  }

  return (
    <>
      {!captureOption && (
        <>
          <p className={classes.subTitle}>Question: {question?.text}</p>
          <CaptureOptions />
        </>
      )}

      {captureOption === "record" && !doneTelepromt && (
        <>
          <p className={classes.subTitle}>Question: {question?.text}</p>
          <TeleprompterNotes
            clientService={clientService}
            notes={notes || teleprompterNotes}
            setNotes={setTeleprompterNotes}
            question={question}
            handleNextStep={nextStepHandler}
            handleClose={() => null}
          />
        </>
      )}

      {captureOption === "record" && doneTelepromt && <VideoRecorder />}

      {captureOption === "upload" && (
        <>
          <p className={classes.subTitle}>Question: {question?.text}</p>
          <VideoInput setFile={setFile} file={file} />
        </>
      )}
    </>
  );
}
