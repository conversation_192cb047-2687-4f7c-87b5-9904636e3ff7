import { useEffect } from "react";
import { AnswerInterface } from "../../interfaces";
import { useServiceContext } from "../../services/ServiceProvider";
import { VideoReplaceStepStatus } from "./VideoReplace/VideoReplaceProvider";

export default function useCheckUploadProcessingStatus({
  setAnswer,
  answer,
  status,
  onProcessDone,
}: any) {
  const { answersService } = useServiceContext();

  useEffect(() => {
    if (!answer || status !== VideoReplaceStepStatus.process) {
      return;
    }

    const interval = setInterval(() => {
      answersService?.getAnswer(answer.id, (_answer: AnswerInterface) => {
        setAnswer(_answer);
      });
    }, 3000);

    if (answer.mp4VideoStatus !== "") {
      clearInterval(interval);
      if (onProcessDone) {
        onProcessDone();
      }
    }

    return () => {
      clearInterval(interval);
    };
  }, [answer, status]);
}
