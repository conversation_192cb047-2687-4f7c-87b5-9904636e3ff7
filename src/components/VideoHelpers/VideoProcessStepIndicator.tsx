import clsx from "clsx";
import React from "react";

export enum StepState {
  todo = "todo",
  inProgress = "in-progress",
  done = "done",
}

type Step = {
  label: string;
  state: StepState;
};
type Props = {
  steps: Step[];
};

export default function VideoProcessStepIndicator({ steps }: Props) {
  return (
    <div className="mb-8">
      <div className="flex justify-between">
        {steps.map((step, index) => (
          <div
            key={index}
            className={clsx("mb-2 text-sm", {
              "text-blue-500 font-medium": step.state === "in-progress",
              "text-gray-500": step.state !== "in-progress",
            })}
          >
            {index + 1} - {step.label}
          </div>
        ))}
      </div>
      <div className="relative flex items-center justify-between">
        <div className="absolute w-full h-1 bg-gray-300 top-1/2 transform -translate-y-1/2 z-0"></div>
        {steps.map((step, index) => (
          <div key={index} className="relative z-10 flex items-center">
            <div
              className={clsx(
                "w-4 h-4 rounded-full flex items-center justify-center border-4",
                {
                  "border-gray-300 text-blue-500 bg-gray-500":
                    step.state === StepState.todo,
                  "border-gray-300 bg-blue-500 text-white":
                    step.state === StepState.inProgress,
                  "border-blue-500 bg-blue-500 text-white":
                    step.state === StepState.done,
                }
              )}
            ></div>
          </div>
        ))}
      </div>
    </div>
  );
}
