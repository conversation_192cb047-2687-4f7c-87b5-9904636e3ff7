import React, { useEffect, useState } from "react";
import { useServiceContext } from "../../services/ServiceProvider";
import getHumanReadableTime from "../../helpers/getHumanReadableTime";

interface Props {
  file: File;
}

export default function VideoUploadingStatusDisplay({ file }: Props) {
  const { fileService } = useServiceContext();
  const [uploadTime, setUploadTime] = useState<number | null>(null);
  const [progress, setProgress] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(0);

  const calculateUploadTime = (fileSize: number, speed: number) => {
    return fileSize / speed;
  };
  const estimateUploadTime = async () => {
    if (!file || !fileService) return;
    const fileSize = file.size;
    const testChunkSize = 500000; // 500 KB
    const testChunk = file.slice(0, testChunkSize);

    const startTime = Date.now();

    const formData = new FormData();

    formData.append("file", testChunk);

    await fileService.uploadTest(formData);

    const endTime = Date.now();
    const durationInSeconds = (endTime - startTime) / 1000;
    const speed = testChunkSize / durationInSeconds; // bytes per second

    const estimatedTime = calculateUploadTime(fileSize, speed);
    setUploadTime(estimatedTime);

    let elapsed = 0;
    const startProgress = Date.now();

    const updateProgress = () => {
      elapsed = (Date.now() - startProgress) / 1000;
      setTimeRemaining(estimatedTime - elapsed);
      const percentage = Math.min((elapsed / estimatedTime) * 100, 100);
      setProgress(percentage);

      if (elapsed < estimatedTime) {
        requestAnimationFrame(updateProgress);
      }
    };

    requestAnimationFrame(updateProgress);
  };

  useEffect(() => {
    estimateUploadTime().catch(console.log);
  }, [file]);
  if (!file || !fileService) return null;

  return (
    <div className="flex flex-col items-center justify-center p-4 bg-gray-100 rounded-b-lg shadow-md w-full">
      <h2 className="text-xl font-semibold mb-4">Uploading Video...</h2>
      <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
        <div
          className="bg-blue-600 h-4 rounded-full"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      <div className="text-sm text-gray-600 mb-2">
        Elapsed Time:{" "}
        <span className="font-medium">
          {getHumanReadableTime(uploadTime ? uploadTime - timeRemaining : 0)}
        </span>
      </div>
      <div className="text-sm text-gray-600">
        Estimated Time Remaining:{" "}
        <span className="font-medium">
          {getHumanReadableTime(timeRemaining)}
        </span>
      </div>
    </div>
  );
}
