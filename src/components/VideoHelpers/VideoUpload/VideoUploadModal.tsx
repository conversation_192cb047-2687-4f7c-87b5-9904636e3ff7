import Modal from "../../../shared/Modal";
import React, { useState } from "react";
import classesFile from "../../Answer/Answer.module.scss";
import VideoUploadProcessStepIndicator from "./VideoUploadProcessStepIndicator";
import { AnswerInterface, QuestionInterface } from "../../../interfaces";
import useVideoStepStatus from "./useVideoStepStatus";
import ThumbnailSelectionStep from "./Steps/ThumbnailSelectionStep";
import useUploadOnFileChange from "../useUploadOnFileChange";
import useCreateAnswerAfterUpload from "./useCreateAnswerAfterUpload";
import { VideoStepStatus, VideoUploadProvider } from "./VideoUploadProvider";
import UploadStep from "./Steps/UploadStep";
import ProcessingStep from "./Steps/ProcessingStep";
import UploadingStep from "./Steps/UploadingStep";
import PublishedStep from "./Steps/PublishedStep";
import { ClientService } from "services";
import { useServiceContext } from "services/ServiceProvider";

const classes: any = classesFile;
type Props = {
  onClose: () => void;
  notes?: string;
  question: QuestionInterface;
  answer?: AnswerInterface;
  clientService?: ClientService;
};
export default function VideoUploadModal({
  onClose,
  question,
  answer: answerIn,
  clientService,
  notes,
}: Props) {
  // console.log("THE MODAL");
  const { adminStatsService } = useServiceContext();

  const [file, setFile] = useState<any>();
  const [answer, setAnswer] = useState<AnswerInterface | undefined>(answerIn);
  const [videoUploadResult, setVideoUploadResult] = useState<any>();
  const [videoDuration, setVideoDuration] = useState(0);

  useUploadOnFileChange({
    file,
    videoUploadResult,
    videoDuration,
    setVideoUploadResult,
  });
  useCreateAnswerAfterUpload({
    question,
    videoUploadResult,
    setAnswer,
    answer,
    videoDuration,
  });

  const status = useVideoStepStatus({ answer });
  // console.log({ status, answer });
  function handleClose() {
    setFile(null);
    // console.log("HANDLE CLOSE CALLED");
    onClose();
    adminStatsService?.trackEvent('QuestionsArchive', 'close_video_upload_modal');
  }

  return (
    <Modal contentWrapperStyle={{ maxWidth: "860px" }}>
      <div className={classes.answerQuestion}>
        <VideoUploadProcessStepIndicator videoStatus={status} />
        <VideoUploadProvider
          setFile={setFile}
          status={status}
          file={file}
          handleClose={handleClose}
          answer={answer}
          setAnswer={setAnswer}
          setVideoDuration={setVideoDuration}
          question={question}
        >
          <UploadStep notes={notes || ""} clientService={clientService} />

          <UploadingStep />
          <ProcessingStep />

          {status === VideoStepStatus.thumbnail ? (
            <ThumbnailSelectionStep />
          ) : (
            <></>
          )}
          <PublishedStep />
        </VideoUploadProvider>
      </div>
    </Modal>
  );
}
