import { useEffect } from "react";
import { AnswerInterface } from "../../../interfaces";
import { useServiceContext } from "../../../services/ServiceProvider";

export default function useSetAnswerFromQuestion() {
  console.log("USELESS");
  // const { answersService } = useServiceContext();
  // useEffect(() => {
  //   if (!isOpen || !!answer) {
  //     return;
  //   }
  //   function setAnswerFromList(answers: AnswerInterface[]) {
  //     const a = answers.find((answer) => answer.question.id === question.id);
  //     if (!!a && !answer) {
  //       setAnswer(a);
  //     }
  //   }
  //   if (answersService?.answerList?.length) {
  //     setAnswerFromList(answersService?.answerList);
  //   } else if (answersService) {
  //     answersService
  //       .getAnswers((answers: AnswerInterface[]) => {
  //         setAnswerFromList(answers);
  //       })
  //       .catch(console.error);
  //   }
  // }, [question, isOpen, answersService, answer]);
}
