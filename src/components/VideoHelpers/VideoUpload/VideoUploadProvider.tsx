import React, { createContext, ReactChild, useContext } from "react";
import { SetState } from "../../../shared/types";
import { AnswerInterface, QuestionInterface } from "../../../interfaces";
import { useNavigate } from "react-router-dom";

export enum VideoStepStatus {
  upload = "upload",
  process = "process",
  thumbnail = "thumbnail",
  publish = "publish",
}

type Type = {
  file?: File;
  setFile: SetState<File>;
  status: VideoStepStatus;
  answer?: AnswerInterface;
  question?: QuestionInterface;
  setAnswer: SetState<AnswerInterface | undefined>;
  setVideoDuration: SetState<number>;
  handleClose: () => void;
};

export const VideoUploadContext = createContext<Type>({
  status: VideoStepStatus.upload,
  setFile: () => null,
  setAnswer: () => null,
  setVideoDuration: () => null,
  handleClose: () => null,
});

type Props = Type & { children: ReactChild | ReactChild[] };
export const VideoUploadProvider = ({
  children,
  file,
  status,
  setFile,
  answer,
  setAnswer,
  setVideoDuration,
  handleClose,
  question,
}: Props) => {
  const navigate = useNavigate();
  return (
    <VideoUploadContext.Provider
      value={{
        file,
        status,
        answer,
        setAnswer,
        setVideoDuration,
        setFile,
        question,
        handleClose: () => {
          if (
            status === VideoStepStatus.publish ||
            status === VideoStepStatus.process ||
            status === VideoStepStatus.thumbnail
          ) {
            navigate("/answers-list");
          }
          handleClose();
        },
      }}
    >
      {children}
    </VideoUploadContext.Provider>
  );
};

export function useVideoUploadContext() {
  return useContext(VideoUploadContext);
}
