import { useEffect } from "react";
import emptyThumbnailUrl from "../emptyThumbnailUrl";
import { AnswerInterface, QuestionInterface } from "../../../interfaces";
import { useServiceContext } from "../../../services/ServiceProvider";
import { useAuthContext } from "../../../services/AuthProvider";
import { SetState } from "../../../shared/types";

type Props = {
  question: QuestionInterface;
  videoUploadResult: any;
  videoDuration: number;
  setAnswer: SetState<AnswerInterface | undefined>;
  answer?: AnswerInterface;
};
export default function useCreateAnswerAfterUpload({
  question,
  videoUploadResult,
  videoDuration,
  setAnswer,
  answer,
}: Props) {
  const { fileService } = useServiceContext();
  const { client } = useAuthContext();
  useEffect(() => {
    if (!videoUploadResult || !!answer) {
      return;
    }
    const payload = {
      clientId: client?.id,
      questionId: question.id,
      imageUrl: emptyThumbnailUrl,
      videoUrl: videoUploadResult.original,
      videoUrls: {
        mp4: videoUploadResult.mp4 || videoUploadResult.original,
        webm: videoUploadResult.webm,
        ogv: videoUploadResult.ogv,
      },
      videoDuration: videoUploadResult.duration || videoDuration,
      subtitlesSpeed: 1500,
      videoKey: videoUploadResult.videoKey,
      uploadId: videoUploadResult.uploadId,
    };

    fileService
      ?.uploadAnswer(payload, (_answer: AnswerInterface) => {
        setAnswer(_answer);
      })
      .catch(console.error);
  }, [videoUploadResult]);
}
