import { useVideoUploadContext } from "../VideoUploadProvider";
import { VideoStepStatus } from "../VideoUploadProvider";
import Button from "../../../../shared/Button";
import classesFile from "../../../Answer/Answer.module.scss";
import VideoCapture from "../../VideoCapture/VideoCapture";
import { VideoCaptureProvider } from "../../VideoCapture/videoCaptureContext";
import { ClientService } from "services";

const classes: any = classesFile;

export default function UploadStep({clientService, notes}: {clientService?: ClientService, notes?: string}) {
  const { status, file, handleClose } = useVideoUploadContext();

  if (file || status !== VideoStepStatus.upload) {
    return null;
  }

  return (
    <>
      <VideoCaptureProvider>
        <VideoCapture notes={notes} clientService={clientService} />
      </VideoCaptureProvider>

      <div className={classes.buttonWrapper}>
        <Button text="Cancel" callback={handleClose} />
      </div>
    </>
  );
}
