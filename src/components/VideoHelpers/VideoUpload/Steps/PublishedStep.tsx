import { useVideoUploadContext, VideoStepStatus } from "../VideoUploadProvider";
import Button from "../../../../shared/Button";
import React from "react";
import classesFile from "../../../Answer/Answer.module.scss";

const classes: any = classesFile;

export default function PublishedStep() {
  const { handleClose, status, answer } = useVideoUploadContext();
  if (status !== VideoStepStatus.publish) {
    return null;
  }
  const thumbnailUrl = answer?.imageUrl;
  return (
    <>
      <div className="bg-white justify-between gap-8">
        <div className=" items-center mb-4">
          <h2 className="text-xl font-semibold">Video Published</h2>
          <p>
            Congratulations! Your video has been published successfully. It's
            now available for everyone to view.
          </p>
        </div>
        <div className="mb-4 bg-gray-200 flex justify-center">
          <img
            src={thumbnailUrl}
            alt="Video Thumbnail"
            className="h-64 mx-auto"
          />
        </div>
      </div>

      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </>
  );
}
