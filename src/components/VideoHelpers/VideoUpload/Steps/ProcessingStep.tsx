import VideoUploadingOrProcessingIndicator from "../../VideoUploadingOrProcessingIndicator";
import React from "react";
import { useVideoUploadContext } from "../VideoUploadProvider";
import { VideoStepStatus } from "../VideoUploadProvider";
import Button from "../../../../shared/Button";
import classesFile from "../../../Answer/Answer.module.scss";
import useCheckUploadProcessingStatus from "../../useCheckUploadProcessingStatus";
import VideoProcessingStatusDisplay from "../../VideoProcessingStatusDisplay";

const classes: any = classesFile;

export default function ProcessingStep() {
  const { status, handleClose, file, answer, setAnswer } =
    useVideoUploadContext();
  useCheckUploadProcessingStatus({ answer, setAnswer, status });
  if (status !== VideoStepStatus.process) {
    return null;
  }
  return (
    <>
      <VideoUploadingOrProcessingIndicator />
      {!!file && <VideoProcessingStatusDisplay file={file} />}
      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </>
  );
}
