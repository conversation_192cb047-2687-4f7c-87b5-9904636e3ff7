import { useMemo } from "react";
import { AnswerInterface } from "../../../interfaces";
import emptyThumbnailUrl from "../emptyThumbnailUrl";
import { VideoStepStatus } from "./VideoUploadProvider";

type Props = {
  answer?: AnswerInterface;
};

export default function useVideoStepStatus({ answer }: Props): VideoStepStatus {
  return useMemo(() => {
    if (!answer) {
      return VideoStepStatus.upload;
    }
    if (answer?.mp4VideoStatus === "") {
      return VideoStepStatus.process;
    }
    if (!answer?.imageUrl || answer.imageUrl === emptyThumbnailUrl) {
      return VideoStepStatus.thumbnail;
    }
    return VideoStepStatus.publish;
  }, [answer]);
}
