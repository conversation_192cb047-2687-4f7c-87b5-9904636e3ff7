import React, { useEffect, useState } from "react";
import VideoProcessStepIndicator, {
  StepState,
} from "../VideoProcessStepIndicator";

export default function VideoUploadProcessStepIndicator({
  videoStatus,
}: {
  videoStatus: string;
}) {
  const [steps, setSteps] = useState([
    { label: "Video Capture", state: StepState.inProgress },
    { label: "Video Processing", state: StepState.todo },
    { label: "Thumbnail Selection", state: StepState.todo },
    { label: "Published", state: StepState.todo },
  ]);

  useEffect(() => {
    if (videoStatus === "upload") {
      setSteps([
        { label: "Video Capture", state: StepState.inProgress },
        { label: "Video Processing", state: StepState.todo },
        { label: "Thumbnail Selection", state: StepState.todo },
        { label: "Published", state: StepState.todo },
      ]);
    }
    if (videoStatus === "process") {
      setSteps([
        { label: "Video Upload", state: StepState.done },
        { label: "Video Processing", state: StepState.inProgress },
        { label: "Thumbnail Selection", state: StepState.todo },
        { label: "Published", state: StepState.todo },
      ]);
    }
    if (videoStatus === "thumbnail") {
      setSteps([
        { label: "Video Upload", state: StepState.done },
        { label: "Video Processing", state: StepState.done },
        { label: "Thumbnail Selection", state: StepState.inProgress },
        { label: "Published", state: StepState.todo },
      ]);
    }
    if (videoStatus === "publish") {
      setSteps([
        { label: "Video Upload", state: StepState.done },
        { label: "Video Processing", state: StepState.done },
        { label: "Thumbnail Selection", state: StepState.done },
        { label: "Published", state: StepState.inProgress },
      ]);
    }
  }, [videoStatus]);
  return <VideoProcessStepIndicator steps={steps} />;
}
