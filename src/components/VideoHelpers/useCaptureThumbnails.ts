import { MutableRefObject, useEffect } from "react";
import { SetState } from "../../shared/types";

type Props = {
  videoRef: MutableRefObject<HTMLVideoElement | null>;
  setThumbnails: SetState<string[]>;
  videoUrl: string;
  thumbnailCount: number;
};
export default function useCaptureThumbnails({
  videoRef,
  setThumbnails,
  videoUrl,
  thumbnailCount,
}: Props) {
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const captureThumbnails = async () => {
      const duration = video.duration;
      const interval = duration / thumbnailCount;

      for (let i = 0; i < thumbnailCount; i++) {
        const time = interval * i;
        video.currentTime = time;

        await new Promise((resolve, reject) => {
          video.onseeked = () => {
            try {
              const canvas = document.createElement("canvas");
              canvas.width = video.videoWidth;
              canvas.height = video.videoHeight;
              const context = canvas.getContext("2d");
              if (context) {
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                const dataUrl = canvas.toDataURL("image/jpeg", 0.5);

                setThumbnails((old) =>
                  old.length < thumbnailCount ? [...old, dataUrl] : old
                );
              }
              resolve(true);
            } catch (error) {
              reject(error);
            }
          };
        }).catch((error) => {
          console.error("Error capturing thumbnail:", error);
          alert(
            "Cannot capture thumbnail due to CORS policy. Please use a video from the same origin or configure CORS properly."
          );
        });
      }
    };

    if (video.duration) {
      captureThumbnails().catch(console.error);
    } else {
      video.addEventListener("loadedmetadata", captureThumbnails);
    }

    return () => {
      video.removeEventListener("loadedmetadata", captureThumbnails);
    };
  }, [videoUrl, videoRef.current, videoRef]);
}
