export default function VideoUploadingOrProcessingIndicator() {
  return (
    <div className="relative w-full h-64 bg-gray-200 rounded-t-md flex items-center justify-center">
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 bg-blue-600 rounded-full animate-bounce"></div>
        <div className="w-4 h-4 bg-blue-600 rounded-full animate-bounce animation-delay-200"></div>
        <div className="w-4 h-4 bg-blue-600 rounded-full animate-bounce animation-delay-400"></div>
      </div>
      <style>
        {`
          .animation-delay-200 {
            animation-delay: 0.2s;
          }
          .animation-delay-400 {
            animation-delay: 0.4s;
          }
        `}
      </style>
    </div>
  );
}
