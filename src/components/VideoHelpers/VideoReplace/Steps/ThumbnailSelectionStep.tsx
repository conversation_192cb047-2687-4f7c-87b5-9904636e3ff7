import React, { useRef, useState } from "react";
import classesFile from "../../../Answer/Answer.module.scss";
import Button from "../../../../shared/Button";
import { useServiceContext } from "../../../../services/ServiceProvider";
import useCaptureThumbnails from "../../useCaptureThumbnails";
import { useVideoReplaceContext } from "../VideoReplaceProvider";

const classes: any = classesFile;

export const thumbnailCount = 15;

const ThumbnailSelectionStep: React.FC = () => {
  const { handleClose, setAnswer, answer } = useVideoReplaceContext();
  const { answersService, fileService } = useServiceContext();
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const [thumbnails, setThumbnails] = useState<string[]>([]);
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(
    null
  );

  const videoUrl = answer
    ? `${answersService?.base}/video-proxy?url=${encodeURIComponent(
        answer.videoUrls.mp4
      )}`
    : "";
  useCaptureThumbnails({ videoRef, setThumbnails, videoUrl, thumbnailCount });

  const handleThumbnailClick = (thumbnail: string, index: number) => {
    setSelectedThumbnail(thumbnail);
    if (videoRef.current) {
      videoRef.current.currentTime =
        (index / thumbnailCount) * videoRef.current.duration;
    }
  };

  async function handlePublish() {
    if (!selectedThumbnail) {
      return;
    }
    const blob = await fetch(selectedThumbnail).then((res) => res.blob());
    const formData = new FormData();
    formData.append("file", blob, "thumbnail.png");

    fileService
      ?.uploadImage(formData, () => {})
      .then((response: any) => {
        const publishedAnswer: any = { ...answer, imageUrl: response.url };
        answersService?.publishAnswer(publishedAnswer).then(() => {
          setAnswer(publishedAnswer);
        });
      });
    handleClose();
  }

  return (
    <div className="p-4">
      <video
        ref={videoRef}
        src={videoUrl}
        controls
        className="h-64 w-full mx-auto mb-4 rounded-lg"
        crossOrigin="anonymous"
      />
      <div className="flex gap-4 w-full overflow-x-scroll">
        {thumbnails.map((thumbnail, index) => (
          <img
            key={index}
            src={thumbnail}
            alt={`Thumbnail ${index + 1}`}
            className={`w-32 cursor-pointer rounded-lg ${
              selectedThumbnail === thumbnail ? "border-4 border-blue-500" : ""
            }`}
            onClick={() => handleThumbnailClick(thumbnail, index)}
          />
        ))}
      </div>
      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
        {selectedThumbnail && (
          <Button text="Set thumbnail" callback={handlePublish} />
        )}
      </div>
    </div>
  );
};

export default ThumbnailSelectionStep;
