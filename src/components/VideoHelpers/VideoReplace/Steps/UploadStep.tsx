import VideoInput from "../../VideoInput";
import Button from "../../../../shared/Button";
import React from "react";
import {
  useVideoReplaceContext,
  VideoReplaceStepStatus,
} from "../VideoReplaceProvider";
import classesFile from "../../../Answer/Answer.module.scss";
import { ClientService } from "services";

const classes: any = classesFile;

export default function UploadStep({clientService}: {clientService?: ClientService}) {
  const { setFile, status, file, handleClose } = useVideoReplaceContext();

  if (file || status !== VideoReplaceStepStatus.upload) {
    return null;
  }

  return (
    <>
      <VideoInput setFile={setFile} file={file} />

      <div className={classes.buttonWrapper}>
        <Button text="Cancel" callback={handleClose} />
      </div>
    </>
  );
}
