import VideoUploadingOrProcessingIndicator from "../../VideoUploadingOrProcessingIndicator";
import VideoUploadingStatusDisplay from "../../VideoUploadingStatusDisplay";
import React, { useEffect, useState } from "react";
import Button from "../../../../shared/Button";

import classesFile from "../../../Answer/Answer.module.scss";
import ConfirmDialog from "../../../ConfirmDialog";
import {
  useVideoReplaceContext,
  VideoReplaceStepStatus,
} from "../VideoReplaceProvider";

const classes: any = classesFile;

export default function UploadingStep() {
  const { status, handleClose, file } = useVideoReplaceContext();
  const [showConfirm, setShowConfirm] = useState(false);

  useEffect(() => {
    const handleBeforeUnload = (event: any) => {
      const message =
        "Are you sure you want to leave? Your video that's being uploaded will be lost.";
      event.returnValue = message;
      return message;
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);
  if (!file || status !== VideoReplaceStepStatus.upload) {
    return null;
  }
  return (
    <>
      {showConfirm && (
        <ConfirmDialog
          title="Cancel Upload"
          message="Are you sure you want to cancel the upload? The file will be lost and cannot be recovered."
          onConfirm={handleClose}
          onCancel={() => setShowConfirm(false)}
        />
      )}
      <VideoUploadingOrProcessingIndicator />
      <VideoUploadingStatusDisplay file={file} />
      <div className={classes.buttonWrapper}>
        <Button
          text="Close"
          callback={() => {
            setShowConfirm(true);
          }}
        />
      </div>
    </>
  );
}
