import VideoUploadingOrProcessingIndicator from "../../VideoUploadingOrProcessingIndicator";
import React from "react";
import Button from "../../../../shared/Button";
import classesFile from "../../../Answer/Answer.module.scss";
import useCheckUploadProcessingStatus from "../../useCheckUploadProcessingStatus";
import VideoProcessingStatusDisplay from "../../VideoProcessingStatusDisplay";
import {
  useVideoReplaceContext,
  VideoReplaceStepStatus,
} from "../VideoReplaceProvider";

const classes: any = classesFile;

export default function ProcessingStep() {
  const { status, handleClose, file, answer, setAnswer, setStatus } =
    useVideoReplaceContext();
  useCheckUploadProcessingStatus({
    status,
    answer,
    setAnswer,
    onProcessDone: () => {
      setStatus(VideoReplaceStepStatus.thumbnail);
    },
  });
  if (status !== VideoReplaceStepStatus.process) {
    return null;
  }
  return (
    <>
      <VideoUploadingOrProcessingIndicator />
      {!!file && <VideoProcessingStatusDisplay file={file} />}
      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </>
  );
}
