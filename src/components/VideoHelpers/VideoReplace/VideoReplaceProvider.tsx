import React, { createContext, ReactChild, useContext } from "react";
import { SetState } from "../../../shared/types";
import { AnswerInterface } from "../../../interfaces";

export enum VideoReplaceStepStatus {
  upload = "upload",
  process = "process",
  thumbnail = "thumbnail",
  publish = "publish",
}

type Type = {
  file?: File;
  setFile: SetState<File>;
  status: VideoReplaceStepStatus;
  answer?: AnswerInterface;
  setAnswer: SetState<AnswerInterface | undefined>;
  setStatus: SetState<VideoReplaceStepStatus>;
  handleClose: () => void;
};

export const VideoReplaceContext = createContext<Type>({
  status: VideoReplaceStepStatus.upload,
  setStatus: () => null,
  setFile: () => null,
  setAnswer: () => null,
  handleClose: () => null,
});

type Props = Type & {
  children: ReactChild | ReactChild[];
};
export const VideoReplaceProvider = ({
  children,
  file,
  setFile,
  status,
  setStatus,
  answer,
  setAnswer,
  handleClose,
}: Props) => {
  return (
    <VideoReplaceContext.Provider
      value={{
        file,
        status,
        setStatus,
        answer,
        setAnswer,
        setFile,
        handleClose,
      }}
    >
      {children}
    </VideoReplaceContext.Provider>
  );
};

export function useVideoReplaceContext() {
  return useContext(VideoReplaceContext);
}
