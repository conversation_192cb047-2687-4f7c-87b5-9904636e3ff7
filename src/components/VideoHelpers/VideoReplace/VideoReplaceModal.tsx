import Modal from "../../../shared/Modal";
import React, { useState } from "react";
import classesFile from "../../Answer/Answer.module.scss";
import VideoUploadProcessStepIndicator from "../VideoUpload/VideoUploadProcessStepIndicator";
import { AnswerInterface, QuestionInterface } from "../../../interfaces";
import ThumbnailSelectionStep from "./Steps/ThumbnailSelectionStep";
import useUploadOnFileChange from "../useUploadOnFileChange";
import useUpdateAnswerAfterUpload from "./useUpdateAnswerAfterUpload";
import {
  VideoReplaceStepStatus,
  VideoReplaceProvider,
} from "./VideoReplaceProvider";
import UploadStep from "./Steps/UploadStep";
import ProcessingStep from "./Steps/ProcessingStep";
import UploadingStep from "./Steps/UploadingStep";
import { ClientService } from "services";

const classes: any = classesFile;
type Props = {
  onClose: () => void;
  question: QuestionInterface;
  answer?: AnswerInterface;
  clientService?: ClientService,
};
export default function VideoReplaceModal({
  onClose,
  question,
  answer: answerIn,
  clientService,
}: Props) {
  const [file, setFile] = useState<any>();
  const [answer, setAnswer] = useState<AnswerInterface | undefined>(answerIn);
  const [videoUploadResult, setVideoUploadResult] = useState<any>();
  const [status, setStatus] = useState(VideoReplaceStepStatus.upload);

  useUploadOnFileChange({
    file,
    videoUploadResult,
    setVideoUploadResult,
  });
  useUpdateAnswerAfterUpload({
    videoUploadResult,
    setAnswer,
    answer,
    setStatus,
  });

  function handleClose() {
    setFile(null);
    onClose();
  }

  console.log({ status });
  return (
    <Modal contentWrapperStyle={{ maxWidth: "860px" }}>
      <div className={classes.answerQuestion}>
        <h1 className="m-auto w-96 text-center mb-6">Replace Video</h1>
        <VideoUploadProcessStepIndicator videoStatus={status} />
        <p className={classes.subTitle}>Question: {question.text}</p>
        <VideoReplaceProvider
          setFile={setFile}
          file={file}
          handleClose={handleClose}
          answer={answer}
          setAnswer={setAnswer}
          status={status}
          setStatus={setStatus}
        >
          <UploadStep clientService={clientService} />
          <UploadingStep />
          <ProcessingStep />
          {status === VideoReplaceStepStatus.thumbnail ? (
            <ThumbnailSelectionStep />
          ) : (
            <></>
          )}
        </VideoReplaceProvider>
      </div>
    </Modal>
  );
}
