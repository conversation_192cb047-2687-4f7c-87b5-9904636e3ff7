import { useEffect } from "react";
import emptyThumbnailUrl from "../emptyThumbnailUrl";
import { AnswerInterface } from "../../../interfaces";
import { useServiceContext } from "../../../services/ServiceProvider";
import { SetState } from "../../../shared/types";
import { VideoReplaceStepStatus } from "./VideoReplaceProvider";

type Props = {
  videoUploadResult: any;
  setAnswer: SetState<AnswerInterface | undefined>;
  setStatus: SetState<VideoReplaceStepStatus>;
  answer?: AnswerInterface;
};
export default function useUpdateAnswerAfterUpload({
  videoUploadResult,
  setAnswer,
  setStatus,
  answer,
}: Props) {
  const { answersService } = useServiceContext();
  useEffect(() => {
    if (!videoUploadResult || !answer) {
      return;
    }

    const payload = {
      imageUrl: emptyThumbnailUrl,
      videoUrl: videoUploadResult.mp4 || videoUploadResult.original,
      videoUrls: {
        mp4: videoUploadResult.mp4 || videoUploadResult.original,
        webm: videoUploadResult.webm,
        ogv: videoUploadResult.ogv,
      },
      videoDuration: videoUploadResult.duration,
      subtitlesSpeed: 1500,
      mp4VideoStatus: "",
      ogvVideoStatus: "",
      webmVideoStatus: "",
      videoCompletionCount: 0,
    };

    setAnswer({ ...answer, ...payload });

    answersService?.replaceVideo({ ...answer, ...payload }, (res: any) => {
      setAnswer(res);
      setStatus(VideoReplaceStepStatus.process);
    });
  }, [videoUploadResult]);
}
