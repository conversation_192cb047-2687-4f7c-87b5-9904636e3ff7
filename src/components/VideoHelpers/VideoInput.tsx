import classes from "../UploadVideo/UploadVideo.module.scss";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPhotoVideo } from "@fortawesome/free-solid-svg-icons";
import { useRef, useState } from "react";
import { SetState } from "../../shared/types";
import { useServiceContext } from "services/ServiceProvider";

type Props = {
  file?: File;
  setFile: SetState<File>;
};
export default function VideoInput({ file, setFile }: Props) {
  const fileInputField: any = useRef(null);
  const [isOver, setIsOver] = useState(false);
  const { adminStatsService } = useServiceContext();

  const handleUploadBtnClick = () => {
    if (file) fileInputField?.current?.click();
  };

  const dropFile = (event: any) => {
    event.preventDefault();
    event.persist();

    const isVideoFile: boolean =
      event.dataTransfer.files[0]?.name
        .toString()
        .match(/.mp4|.MP4|.mov|.MOV/) !== null;

    if (isVideoFile) {
      setFile(event.dataTransfer.files[0]);
      adminStatsService?.trackEvent('QuestionsArchive', 'drop_file_to_upload');
    }

    setIsOver(false);
  };

  return (
    <div
      className={`${classes.uploadVideo} ${isOver && classes.uploadVideoHovered
        }`}
      onClick={handleUploadBtnClick}
      onDrop={dropFile}
      onDragOver={(e) => {
        e.preventDefault();
        setIsOver(true);
      }}
      onDragLeave={(e) => {
        e.preventDefault();
        setIsOver(false);
      }}
    >
      <label htmlFor="video">
        <form action="">
          <input
            type="file"
            id="video"
            name="video"
            accept=".mp4,.MP4,.mov,.MOV"
            ref={fileInputField}
            onChange={(e) => {
              if (e.target.files !== null) {
                setFile(e.target.files[0]);
                adminStatsService?.trackEvent('QuestionsArchive', 'select_file_to_upload');
              }
            }}
          />

          <div className={classes.uploadIconWrapper}>
            <FontAwesomeIcon
              icon={faPhotoVideo}
              className={classes.uploadIcon}
            />
          </div>

          <div className={classes.textSection}>
            <h6>Drag and Drop or</h6>
            <p>Select an MP4 or MOV Video File</p>
          </div>
        </form>
      </label>
    </div>
  );
}
