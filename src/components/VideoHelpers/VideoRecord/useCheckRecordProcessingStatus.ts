import { useEffect } from "react";
import { AnswerInterface } from "../../../interfaces";
import { useServiceContext } from "../../../services/ServiceProvider";
import {
  useVideoRecordContext,
  VideoRecordStepStatus,
} from "./VideoRecordProvider";

export default function useCheckRecordProcessingStatus() {
  const { answersService } = useServiceContext();
  const { setAnswer, answer, setStatus } = useVideoRecordContext();

  useEffect(() => {
    if (!answer) {
      return;
    }

    const interval = setInterval(() => {
      answersService?.getAnswer(answer.id, (_answer: AnswerInterface) => {
        setAnswer(_answer);
      });
    }, 3000);

    if (answer.mp4VideoStatus !== "") {
      answersService?.publishAnswer(answer);
      setStatus(VideoRecordStepStatus.publish);
      clearInterval(interval);
    }

    return () => {
      clearInterval(interval);
    };
  }, [answer]);
}
