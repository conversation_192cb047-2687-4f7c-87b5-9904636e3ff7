import {
  useVideoRecordContext,
  VideoRecordStepStatus,
} from "../VideoRecordProvider";
import RecordVideo from "../../../RecordVideo";
import React, { useEffect, useState } from "react";
import { useServiceContext } from "../../../../services/ServiceProvider";
import { useAuthContext } from "../../../../services/AuthProvider";
import VideoUploadingOrProcessingIndicator from "../../VideoUploadingOrProcessingIndicator";
import VideoUploadingStatusDisplay from "../../VideoUploadingStatusDisplay";
import ConfirmDialog from "../../../ConfirmDialog";
import Button from "../../../../shared/Button";
import classesFile from "../../../Answer/Answer.module.scss";
import { ClientService } from "services";

const classes: any = classesFile;

export default function RecordStep({clientService}: {clientService?: ClientService}) {
  const {
    handleClose,
    status,
    setFile,
    file,
    question,
    notes,
    useAsSubtitle,
    setStatus,
    setAnswer,
  } = useVideoRecordContext();
  const { fileService } = useServiceContext();
  const { client } = useAuthContext();
  const [showConfirm, setShowConfirm] = useState(false);
  const [isVideoRecorded, setIsVideoRecorded] = useState(false);

  useEffect(() => {
    const handleBeforeUnload = (event: any) => {
      const message =
        "Are you sure you want to leave? Your video that's being uploaded will be lost.";
      event.returnValue = message;
      return message;
    };

    if (isVideoRecorded || !!file) {
      window.addEventListener("beforeunload", handleBeforeUnload);
    } else {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    }

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isVideoRecorded, file]);
  if ((status !== VideoRecordStepStatus.record && status) || !question?.id) {
    return null;
  }

  return (
    <>
      {showConfirm && (
        <ConfirmDialog
          title="Cancel Upload"
          message="Are you sure you want to cancel the upload? The file will be lost and cannot be recovered."
          onConfirm={handleClose}
          onCancel={() => setShowConfirm(false)}
        />
      )}

      <div className={`${!file ? "block" : "hidden"}`}>
        <RecordVideo
          questionId={question?.id}
          setFile={setFile}
          notes={notes}
          useAsSubtitle={useAsSubtitle}
          fileService={fileService}
          client={client}
          refetchQuestionList={() => null}
          setIsVideoRecorded={setIsVideoRecorded}
          handleDone={(answer) => {
            console.log("HANDLE DONE CALLED");
            setAnswer(answer);
            setStatus(VideoRecordStepStatus.process);
          }}
          handleClose={() => {
            if (isVideoRecorded) {
              setShowConfirm(true);
            } else {
              handleClose();
            }
          }}
          handleGoBack={() => setStatus(VideoRecordStepStatus.prompt)}
        />
      </div>

      {!!file && (
        <>
          <VideoUploadingOrProcessingIndicator />
          <VideoUploadingStatusDisplay file={file} />
          <div className={classes.buttonWrapper}>
            <Button
              text="Close"
              callback={() => {
                setShowConfirm(true);
              }}
            />
          </div>
        </>
      )}
    </>
  );
}
