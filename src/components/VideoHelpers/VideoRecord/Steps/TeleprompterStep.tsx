import {
  useVideoRecordContext,
  VideoRecordStepStatus,
} from "../VideoRecordProvider";
import TeleprompterNotes from "../../../TeleprompterNotes";
import { ClientService } from "services";

export default function TeleprompterStep({clientService, question}: {clientService?: ClientService, question: any}) {
  const {
    handleClose,
    notes,
    setNotes,
    status,
    setStatus,
  } = useVideoRecordContext();

  if (status !== VideoRecordStepStatus.prompt) {
    return null;
  }
  return (
    <TeleprompterNotes
      clientService={clientService}
      handleClose={handleClose}
      notes={notes}
      setNotes={setNotes}
      question={question}
      handleNextStep={() => setStatus(VideoRecordStepStatus.record)}
    />
  );
}
