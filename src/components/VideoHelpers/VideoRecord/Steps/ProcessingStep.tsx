import VideoUploadingOrProcessingIndicator from "../../VideoUploadingOrProcessingIndicator";
import React from "react";
import {
  useVideoRecordContext,
  VideoRecordStepStatus,
} from "../VideoRecordProvider";
import Button from "../../../../shared/Button";
import classesFile from "../../../Answer/Answer.module.scss";
import useCheckRecordProcessingStatus from "../useCheckRecordProcessingStatus";
import VideoProcessingStatusDisplay from "../../VideoProcessingStatusDisplay";

const classes: any = classesFile;

export default function ProcessingStep() {
  const { status, handleClose, file } = useVideoRecordContext();
  useCheckRecordProcessingStatus();
  if (status !== VideoRecordStepStatus.process) {
    return null;
  }
  return (
    <>
      <VideoUploadingOrProcessingIndicator />
      {!!file && <VideoProcessingStatusDisplay file={file} />}
      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </>
  );
}
