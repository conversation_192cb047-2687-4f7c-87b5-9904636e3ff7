import React, { createContext, ReactChild, useContext, useState } from "react";
import { SetState } from "../../../shared/types";
import { AnswerInterface, QuestionInterface } from "../../../interfaces";
import { useNavigate } from "react-router-dom";

export enum VideoRecordStepStatus {
  prompt = "prompt",
  record = "record",
  process = "process",
  publish = "publish",
}

type Type = {
  file?: File;
  setFile: SetState<File>;
  status: VideoRecordStepStatus;
  setStatus: SetState<VideoRecordStepStatus>;
  answer?: AnswerInterface;
  question?: QuestionInterface;
  setAnswer: SetState<AnswerInterface | undefined>;
  handleClose: () => void;
  notes: string;
  setNotes: SetState<string>;
  useAsSubtitle: boolean;
  setUseAsSubtitle: SetState<boolean>;
};

export const VideoRecordContext = createContext<Type>({
  status: VideoRecordStepStatus.prompt,
  setStatus: () => null,
  setFile: () => null,
  setAnswer: () => null,
  handleClose: () => null,
  notes: "",
  setNotes: () => null,
  useAsSubtitle: false,
  setUseAsSubtitle: () => null,
});

type Props = Omit<
  Type,
  "status" | "setStatus" | "setNotes" | "useAsSubtitle" | "setUseAsSubtitle"
> & { children: ReactChild | ReactChild[] };
export const VideoRecordProvider = ({
  children,
  file,
  notes: notesIn,
  setFile,
  answer,
  question,
  setAnswer,
  handleClose,
}: Props) => {
  const [status, setStatus] = useState(VideoRecordStepStatus.prompt);
  const [notes, setNotes] = useState(notesIn);
  const [useAsSubtitle, setUseAsSubtitle] = useState(false);
  const navigate = useNavigate();

  return (
    <VideoRecordContext.Provider
      value={{
        file,
        status,
        setStatus,
        answer,
        question: question || answer?.question,
        setAnswer,
        setFile,
        handleClose: () => {
          if (
            status === VideoRecordStepStatus.publish ||
            status === VideoRecordStepStatus.process
          ) {
            navigate("/answers-list");
          }
          handleClose();
        },
        notes,
        setNotes,
        useAsSubtitle,
        setUseAsSubtitle,
      }}
    >
      {children}
    </VideoRecordContext.Provider>
  );
};

export function useVideoRecordContext() {
  return useContext(VideoRecordContext);
}
