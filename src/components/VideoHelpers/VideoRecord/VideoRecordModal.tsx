import Modal from "../../../shared/Modal";
import React, { useState } from "react";
import classesFile from "../../Answer/Answer.module.scss";
import { AnswerInterface, QuestionInterface } from "../../../interfaces";
import TeleprompterStep from "./Steps/TeleprompterStep";
import { VideoRecordProvider } from "./VideoRecordProvider";
import VideoRecordProcessStepIndicator from "./VideoRecordProcessStepIndicator";
import RecordStep from "./Steps/RecordStep";
import ProcessingStep from "./Steps/ProcessingStep";
import PublishedStep from "./Steps/PublishedStep";
import { ClientService } from "services";

const classes: any = classesFile;
type Props = {
  onClose: () => void;
  question: QuestionInterface;
  notes?: string;
  clientService?: ClientService;
};
export default function VideoRecordModal({ onClose, question, notes, clientService }: Props) {
  const [file, setFile] = useState<any>();
  const [answer, setAnswer] = useState<AnswerInterface | undefined>();

  function handleClose() {
    setFile(null);
    onClose();
  }

  return (
    <Modal contentWrapperStyle={{ maxWidth: "860px" }}>
      <div className={classes.answerQuestion}>
        <VideoRecordProvider
          notes={notes || ""}
          setFile={setFile}
          file={file}
          handleClose={handleClose}
          answer={answer}
          question={question}
          setAnswer={setAnswer}
        >
          <VideoRecordProcessStepIndicator />

          <p className={classes.subTitle}>Question: {question.text}</p>

          <TeleprompterStep clientService={clientService} question={question} />

          <RecordStep />
          <ProcessingStep />
          <PublishedStep />
        </VideoRecordProvider>
      </div>
    </Modal>
  );
}
