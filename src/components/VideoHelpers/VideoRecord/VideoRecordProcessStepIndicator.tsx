import React, { useEffect, useState } from "react";
import {
  useVideoRecordContext,
  VideoRecordStepStatus,
} from "./VideoRecordProvider";
import VideoProcessStepIndicator, {
  StepState,
} from "../VideoProcessStepIndicator";

export default function VideoRecordProcessStepIndicator() {
  const { status: videoStatus } = useVideoRecordContext();
  const [steps, setSteps] = useState([
    { label: "Teleprompt", state: StepState.inProgress },
    { label: "Record Video", state: StepState.todo },
    { label: "Video Processing", state: StepState.todo },
    { label: "Published", state: StepState.todo },
  ]);

  useEffect(() => {
    if (videoStatus === VideoRecordStepStatus.prompt) {
      setSteps([
        { label: "Teleprompt", state: StepState.inProgress },
        { label: "Record Video", state: StepState.todo },
        { label: "Video Processing", state: StepState.todo },
        { label: "Published", state: StepState.todo },
      ]);
    }
    if (videoStatus === VideoRecordStepStatus.record) {
      setSteps([
        { label: "Teleprompt", state: StepState.done },
        { label: "Record Video", state: StepState.inProgress },
        { label: "Video Processing", state: StepState.todo },
        { label: "Published", state: StepState.todo },
      ]);
    }
    if (videoStatus === VideoRecordStepStatus.process) {
      setSteps([
        { label: "Teleprompt", state: StepState.done },
        { label: "Record Video", state: StepState.done },
        { label: "Video Processing", state: StepState.inProgress },
        { label: "Published", state: StepState.todo },
      ]);
    }
    if (videoStatus === VideoRecordStepStatus.publish) {
      setSteps([
        { label: "Teleprompt", state: StepState.done },
        { label: "Record Video", state: StepState.done },
        { label: "Video Processing", state: StepState.done },
        { label: "Published", state: StepState.inProgress },
      ]);
    }
  }, [videoStatus]);
  return <VideoProcessStepIndicator steps={steps} />;
}
