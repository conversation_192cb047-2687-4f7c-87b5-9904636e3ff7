import { useEffect } from "react";
import { useServiceContext } from "../../services/ServiceProvider";
import { SetState } from "../../shared/types";

type Props = {
  file?: File;
  setVideoUploadResult: SetState<any>;
  videoUploadResult: any;
  videoDuration?: number;
};
export default function useUploadOnFileChange({
  file,
  setVideoUploadResult,
  videoUploadResult,
  videoDuration,
}: Props) {
  const { fileService } = useServiceContext();

  useEffect(() => {
    if (file && !videoUploadResult) {
      const formData = new FormData();

      formData.append("file", file);

      fileService?.uploadVideo(formData, () => {}).then(setVideoUploadResult);
    }
  }, [file, videoUploadResult]);
}
