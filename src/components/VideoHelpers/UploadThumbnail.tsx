import React, { useState } from "react";
import CameraIcon from "./CameraIcon";
import { useServiceContext } from "../../services/ServiceProvider";
import Thumbnail from "./VideoCapture/Thumbnail";
import ThumbnailCaptureOptions from "./ThumbnailCaptureOptions";

export default function UploadThumbnail({
  setSelectedThumbnail,
  selectedThumbnail,
}: any) {
  const [file, setFile] = useState<any>(null);
  const [captureOption, setCaptureOption] = useState<"upload" | "camera" | null>(null);
  const { fileService, adminStatsService } = useServiceContext();

  const handleFileChange = async (event: any) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      const fileURL = URL.createObjectURL(selectedFile);
      const blob = await fetch(fileURL).then((res) => res.blob());
      const formData = new FormData();
      formData.append("file", blob, "thumbnail.png");
      fileService
        ?.uploadImage(formData, () => { })
        .then((response: any) => {
          setFile(response.url);
          setSelectedThumbnail(response.url);
          setCaptureOption(null);
        });
    }
  };

  const handleCameraCapture = async (imageDataUrl: string) => {
    const blob = await fetch(imageDataUrl).then((res) => res.blob());
    const formData = new FormData();
    formData.append("file", blob, "thumbnail.png");
    fileService
      ?.uploadImage(formData, () => { })
      .then((response: any) => {
        setFile(response.url);
        setSelectedThumbnail(response.url);
        setCaptureOption(null);
      });
  };

  const handleOptionClick = () => {
    setCaptureOption("upload"); // This will show the options first
    adminStatsService?.trackEvent('ThumbnailSelection', 'click_upload_thumbnail');
  };

  if (captureOption) {
    return (
      <ThumbnailCaptureOptions
        captureOption={captureOption}
        setCaptureOption={setCaptureOption}
        onFileUpload={handleFileChange}
        onCameraCapture={handleCameraCapture}
      />
    );
  }

  return (
    <div className="flex flex-col items-center">
      <div className="mb-4 flex gap-3">
        <div
          onClick={handleOptionClick}
          className="cursor-pointer w-32 h-32 flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md"
        >
          <CameraIcon />
          <div className="text-gray-400">Click to upload</div>
        </div>
        {file && (
          <Thumbnail
            selectedThumbnail={selectedThumbnail}
            url={file}
            handleThumbnailClick={() => setSelectedThumbnail(file)}
          />
        )}
      </div>
    </div>
  );
}
