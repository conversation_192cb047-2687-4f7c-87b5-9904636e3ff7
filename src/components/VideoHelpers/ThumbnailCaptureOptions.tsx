import React, { useRef, useEffect, useState } from "react";
import { useServiceContext } from "../../services/ServiceProvider";

interface ThumbnailCaptureOptionsProps {
  captureOption: "upload" | "camera" | null;
  setCaptureOption: (option: "upload" | "camera" | null) => void;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onCameraCapture: (imageDataUrl: string) => void;
}

export default function ThumbnailCaptureOptions({
  captureOption,
  setCaptureOption,
  onFileUpload,
  onCameraCapture,
}: ThumbnailCaptureOptionsProps) {
  const { adminStatsService } = useServiceContext();
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);

  useEffect(() => {
    if (captureOption === "camera") {
      startCamera();
    }
    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [captureOption]);

  useEffect(() => {
    return () => {
      // Cleanup stream when component unmounts
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [stream]);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" }, // Use back camera on mobile if available
      });
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
      // Fallback to any available camera
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: true,
        });
        setStream(mediaStream);
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
        }
      } catch (fallbackError) {
        console.error("Error accessing any camera:", fallbackError);
        alert("Unable to access camera. Please check permissions.");
        setCaptureOption(null);
      }
    }
  };

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext("2d");

      if (context) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        const imageDataUrl = canvas.toDataURL("image/jpeg", 0.8);
        setCapturedImage(imageDataUrl);
        adminStatsService?.trackEvent('ThumbnailSelection', 'capture_photo');
      }
    }
  };

  const retakePhoto = () => {
    setCapturedImage(null);
    // Restart camera stream
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    startCamera();
    adminStatsService?.trackEvent('ThumbnailSelection', 'retake_photo');
  };

  const usePhoto = () => {
    if (capturedImage) {
      onCameraCapture(capturedImage);
      adminStatsService?.trackEvent('ThumbnailSelection', 'use_captured_photo');
    }
  };

  const handleBack = () => {
    if (stream) {
      stream.getTracks().forEach((track) => track.stop());
    }
    setCaptureOption(null);
    setCapturedImage(null);
  };

  const handleUploadClick = () => {
    adminStatsService?.trackEvent('ThumbnailSelection', 'select_upload_photo');
    fileInputRef.current?.click();
  };

  // Show initial options (like CaptureOptions in video)
  if (captureOption === "upload") {
    return (
      <div className="flex flex-col items-center justify-center w-full h-full bg-gray-100 p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">
          Choose an Option
        </h2>
        <div className="flex space-x-4">
          <button
            onClick={handleUploadClick}
            className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
          >
            Upload Photo
          </button>
          <button
            onClick={() => {
              setCaptureOption("camera");
              adminStatsService?.trackEvent('ThumbnailSelection', 'select_take_photo');
            }}
            className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
          >
            Take Photo
          </button>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          onChange={onFileUpload}
        />
      </div>
    );
  }

  // Camera interface (like VideoRecorder)
  if (captureOption === "camera") {
    return (
      <div className="w-full h-full">
        <div className="flex flex-col items-center justify-center bg-gray-200 p-6">
          <div className="relative w-full bg-black flex justify-center">
            {!capturedImage ? (
              <video
                ref={videoRef}
                className="max-w-full max-h-96 object-contain"
                autoPlay
                muted
                playsInline
              />
            ) : (
              <img
                src={capturedImage}
                alt="Captured thumbnail"
                className="max-w-full max-h-96 object-contain"
              />
            )}
            <canvas ref={canvasRef} className="hidden" />
          </div>
          <div className="mt-4 flex space-x-4">
            {!capturedImage ? (
              <>
                <button
                  onClick={handleBack}
                  className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Cancel
                </button>
                <button
                  onClick={capturePhoto}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Capture Photo
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={handleBack}
                  className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Cancel
                </button>
                <button
                  onClick={retakePhoto}
                  className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Redo
                </button>
                <button
                  onClick={usePhoto}
                  className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
                >
                  Use Photo
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  return null;
}
