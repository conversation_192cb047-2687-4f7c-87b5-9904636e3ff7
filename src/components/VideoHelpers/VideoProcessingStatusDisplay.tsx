import { useEffect, useState } from "react";
import getHumanReadableTime from "../../helpers/getHumanReadableTime";

type Props = {
  file: File;
};
export default function VideoProcessingStatusDisplay({ file }: Props) {
  const [estimatedTime, setEstimatedTime] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (file) {
      const size = file.size;
      const kiloByte = 1024;
      const megaByte = kiloByte * kiloByte;
      if (size < 100 * megaByte) {
        setEstimatedTime(120);
        return;
      }
      if (size < 150 * megaByte) {
        setEstimatedTime(150);
        return;
      }
      if (size < 200 * megaByte) {
        setEstimatedTime(180);
        return;
      }
      if (size < 500 * megaByte) {
        setEstimatedTime(210);
        return;
      }
      if (size < 750 * megaByte) {
        setEstimatedTime(300);
        return;
      }
      setEstimatedTime(900);
    }
  }, [file]);
  useEffect(() => {
    if (intervalId) {
      return;
    }
    const id = setInterval(() => {
      setElapsedTime((prev) => prev + 1);
    }, 1000);
    setIntervalId(id);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [intervalId]);
  useEffect(() => {
    if (elapsedTime >= estimatedTime && intervalId) {
      clearInterval(intervalId);
    }
  }, [elapsedTime, intervalId]);

  const estimatedCompletionTime =
    estimatedTime > elapsedTime ? estimatedTime - elapsedTime : 0;
  const progress = (elapsedTime / estimatedTime) * 100;

  return (
    <div className="flex flex-col items-center justify-center p-4 bg-gray-100 rounded-b-lg shadow-md w-full">
      <h2 className="text-xl font-semibold mb-4">Processing Video...</h2>
      <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
        <div
          className="bg-blue-600 h-4 rounded-full"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      <div className="text-sm text-gray-600 mb-2">
        Elapsed Time:{" "}
        <span className="font-medium">{getHumanReadableTime(elapsedTime)}</span>
      </div>
      <div className="text-sm text-gray-600">
        Estimated Time Remaining:{" "}
        <span className="font-medium">
          {getHumanReadableTime(estimatedCompletionTime)}
        </span>
      </div>
    </div>
  );
}
