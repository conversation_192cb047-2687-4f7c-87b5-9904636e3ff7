@import 'styles/variables';

.previewVideoWrapper {
  .videoPreview {
    height: 220px;
    width: 100%;
    background-color: $transparent-blue;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;

  }

  .hiddenCaptureVideo {
    display: block;
    height: 1px;
    width: 1px;
    left: 0;
    object-fit: contain;
    position: fixed;
    top: 0;
    z-index: -1;
    opacity: 0;
  }

  .fileName {
    background-color: $pale-blue;
    padding: 10px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;

    h2 {
      color: $grey;
      font-weight: 400;
      margin: 0
    }

    p {
      color: $dark-blue;
      font-weight: 400;
    }
  }

  h2 {
    padding-bottom: 5px;
    margin: 10px 0 10px 0;
    width: 100%;
    font-size: 16px;
    border-bottom: 1px solid $grey;
    font-weight: 300;
  }

  .Thumbnails {
    display: flex;
    border: 1px solid $grey;
    border-radius: 5px;
    width: 100%;
    min-height: 120px;
    overflow-y: hidden;
    overflow-x: auto;
    position: relative;
    padding: 10px;

    .Thumbnail {
      cursor: pointer;
      margin-right: 20px;
      min-width: 150px;
      width: 150px;
      border-radius: 4px;
      border: 4px solid $pale-blue;

      &:hover {
        border-color: $blue;
      }
    }

    .selected {
      border-color: $dark-blue !important;
    }

    .Timestamp {
      margin-right: 20px;
      text-align: center;
      color: $grey;
    }

  }
}