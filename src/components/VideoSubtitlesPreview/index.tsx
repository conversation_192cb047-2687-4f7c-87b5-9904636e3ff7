import React, { useEffect, useRef, useState, useMemo } from "react";
import cn from "classnames";

import classes from "./VideoSubtitlesPreview.module.scss";
import { FileService } from "../../services";

interface VideoSubtitlesPreviewProps {
  src: string;
  setImgSrc: (file: File) => void;
  setVideoDuration: (duration: number) => void;
  fileName?: string;
  loading: boolean;
  file?: File;
  fileService?: FileService;
  mediaBlobUrl?: string;
}

export default function VideoSubtitlesPreview(
  props: VideoSubtitlesPreviewProps
) {
  const {
    src,
    setImgSrc,
    setVideoDuration,
    fileName,
    loading,
    file,
    fileService,
    mediaBlobUrl,
  } = props;
  const thumbnailsLimit = 20;

  const video: any = useRef<HTMLVideoElement>(null);

  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  const [seeked, setSeeked] = useState<boolean>(false);
  const [delay, setDelay] = useState<number>(0);
  const [timestamp, setTimestamp] = useState<number>(0);
  const [thumbnails, setThumbnails] = useState<
    { file: File; timestamp: number; preview: any }[]
  >([]);
  const [selected, setSelected] = useState<File>();

  const setNextFrame = (time: number) => {
    if (video.current) {
      video.current.currentTime = time / 1000;
      setTimestamp(time);
    }
  };

  const calculateDelay = (duration: number) => {
    video.current.onseeked = () => {
      setSeeked(true);
    };
    setDelay(Math.ceil((duration / thumbnailsLimit) * 1000));
    setTimeout(() => {
      setNextFrame(1000);
    }, 1000);
  };

  const captureThumbnail = () => {
    const canvas = document.createElement("canvas");

    canvas.width = video.current.videoWidth;
    canvas.height = video.current.videoHeight;

    const context = canvas.getContext("2d");

    if (context == null) throw new Error("Could not get context");

    context.drawImage(video.current, 0, 0, canvas.width, canvas.height);

    fetch(canvas.toDataURL())
      .then((res) => res.blob())
      .then((blob) => {
        const imageFile = new File([blob], "image.png", { type: "image/png" });
        if (timestamp > 1000) {
          setThumbnails((thumbnails) => [
            ...thumbnails,
            {
              file: imageFile,
              timestamp: timestamp,
              preview: URL.createObjectURL(imageFile),
            },
          ]);
        }
        setNextFrame(timestamp + delay);
      });
  };

  console.log({ thumbnails });
  useEffect(() => {
    if (
      !loading &&
      dataLoaded &&
      thumbnails.length < thumbnailsLimit &&
      seeked
    ) {
      captureThumbnail();
      setSeeked(false);
    }
    return () => {
      thumbnails.map((thumbnail) => URL.revokeObjectURL(thumbnail.preview));
    };
  }, [dataLoaded, seeked, thumbnails.length, loading]);

  const Preview = useMemo(
    () => (
      <>
        <video
          className={classes.videoPreview}
          src={src}
          autoPlay
          loop
          muted
          controls
          playsInline
        />

        <video
          className={classes.hiddenCaptureVideo}
          src={src}
          ref={video}
          muted
          playsInline
          onLoadedMetadata={() => {
            if (video.current) {
              const hiddenVideoCopy = video.current;

              if (!Number.isFinite(hiddenVideoCopy.duration)) {
                console.info("Infinity video duration detected, fixing");

                hiddenVideoCopy.currentTime = 1e101;

                hiddenVideoCopy.onseeked = () => {
                  setVideoDuration(hiddenVideoCopy.duration);

                  console.info(
                    "New fixed video duration:",
                    hiddenVideoCopy.duration
                  );

                  calculateDelay(hiddenVideoCopy.currentTime);
                };
              } else {
                calculateDelay(hiddenVideoCopy.duration);
                setVideoDuration(hiddenVideoCopy.duration);
              }
            }
          }}
          onLoadedData={() => setDataLoaded(true)}
        />
      </>
    ),
    [src]
  );

  return (
    <div className={classes.previewVideoWrapper}>
      <div className="mb-8">{/*<VideoRecordProcessStepIndicator />*/}</div>
      {Preview}

      {fileName && (
        <div className={classes.fileName}>
          <h2>File Name</h2>
          <p>{fileName}</p>
        </div>
      )}

      {!loading && (
        <>
          <h2>Choose Thumbnail</h2>

          <div className="flex gap-4 w-full overflow-x-scroll">
            {thumbnails.map((thumbnail, index) => (
              <img
                key={index}
                src={thumbnail.preview}
                alt={`Thumbnail ${index + 1}`}
                className={`w-32 cursor-pointer rounded-lg ${
                  selected === thumbnail.file ? "border-4 border-blue-500" : ""
                }`}
                onClick={() => {
                  setSelected(thumbnail.file);
                  setImgSrc(thumbnail.file);
                }}
              />
            ))}
          </div>
          {/*<div className={classes.Thumbnails}>*/}
          {/*  {thumbnails.length === 0 ? (*/}
          {/*    <div>Capturing thumbnails...</div>*/}
          {/*  ) : (*/}
          {/*    <>*/}
          {/*      {thumbnails.map((thumbnail) => (*/}
          {/*        <div key={thumbnail.preview}>*/}
          {/*          <div*/}
          {/*            className={cn(*/}
          {/*              classes.Thumbnail,*/}
          {/*              selected === thumbnail.file && classes.selected*/}
          {/*            )}*/}
          {/*            onClick={() => {*/}
          {/*              setSelected(thumbnail.file);*/}
          {/*              setImgSrc(thumbnail.file);*/}
          {/*            }}*/}
          {/*          >*/}
          {/*            <img src={thumbnail.preview} alt="" width="100%" />*/}
          {/*          </div>*/}
          {/*        </div>*/}
          {/*      ))}*/}
          {/*    </>*/}
          {/*  )}*/}
          {/*</div>*/}
        </>
      )}
    </div>
  );
}
