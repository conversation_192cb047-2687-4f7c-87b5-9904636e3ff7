.firstnamelastnameemailcom,
.whatIsThe {
  width: 310px;
  position: relative;
  display: flex;
  align-items: center;
}

.firstnamelastnameemailcom {
  width: 310px;
  height: 20px;
  color: var(--color-crimson);
  flex-shrink: 0;
}

.whatIsTheCurrentNumberOfParent {
  width: 310px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: var(--gap-9xs);
}

.jan2025 {
  height: 18px;
  position: relative;
  font-size: var(--font-size-sm);
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.questionItemChild {
  height: 40px;
  width: 40px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--color-slategray);

  &:hover {
    color: var(--color-crimson);
  }
}

.buttonContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rightSpacing {
  margin-right: 20px;
}

.questionItem {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  cursor: pointer;
  text-align: left;
  font-size: var(--font-size-base);
  color: var(--color-slategray);
  //font-family: var(--font-plus-jakarta-sans);
}

.questionItemIcon {
  width: 100%;
  height: 1px;
  position: relative;
}

@media (max-width: 640px) {
  .questionItem {
    align-items: flex-start;
    flex-direction: column;
  }

  .firstnamelastnameemailcom {
    padding-bottom: 10px;
  }
}