import { FunctionComponent, useMemo, useState } from "react";
import styles from "./QuestionItem.module.scss";
import { useServiceContext } from "services/ServiceProvider";
import { cn } from "utils/utils";
import { QuestionInterface } from "interfaces";
import { QuestionService, ClientService } from "services";
import ShareQuestion from "popups/ShareQuestion";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export type QuestionItemType = {
  className?: string;
  question?: string;
  email?: string;
  dateString?: string;
  onAnswer?: () => void;
  user: any;
  // New props for share functionality
  questionData?: QuestionInterface | any;
  questionService?: QuestionService;
  clientService?: ClientService;
};

const QuestionItem: FunctionComponent<QuestionItemType> = ({
  className = "",
  question,
  email,
  dateString,
  onAnswer,
  user,
  questionData,
  questionService,
  clientService
}) => {
  const { userService, adminStatsService } = useServiceContext();
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  const isSuper = useMemo(() => user?.accessLevel === "super admin", [userService?.user?.accessLevel]);

  const handleShareClick = () => {
    setIsShareModalOpen(true);
    adminStatsService?.trackEvent('QuestionItem', 'open_share_modal');
  };

  return (
    <>
      <div
        className={[styles.questionItem, className].join(" ")}
      >
        <div className={styles.whatIsTheCurrentNumberOfParent}>
          <div className={styles.whatIsThe}>{question}</div>
          <i className={styles.firstnamelastnameemailcom}>
            {email}
          </i>
        </div>
        <b className={cn(styles.jan2025, isSuper && styles.rightSpacing)}>{dateString}</b>
        {!isSuper && (
          <div className={styles.buttonContainer}>
            <img
              className={styles.questionItemChild}
              onClick={onAnswer}
              alt=""
              src="/analytics/icons/camera-recorder.svg"
            />
            {questionData && questionService && clientService && (
              <div
                className={styles.questionItemChild}
                onClick={handleShareClick}
              >
                <FontAwesomeIcon icon={['fas', 'share']} />
              </div>
            )}
          </div>
        )}
      </div>
      <img
        className={styles.questionItemIcon}
        alt=""
        src="/analytics/chart/divider-thin.svg"
      />

      {questionData && questionService && clientService && (
        <ShareQuestion
          isOpen={isShareModalOpen}
          handleClose={() => {
            setIsShareModalOpen(false);
            adminStatsService?.trackEvent('QuestionItem', 'close_share_modal');
          }}
          question={questionData}
          questionService={questionService}
          clientService={clientService}
        />
      )}
    </>
  );
};

export default QuestionItem;
