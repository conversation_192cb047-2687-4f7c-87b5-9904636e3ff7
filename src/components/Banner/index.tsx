import React from "react";
import Button from "shared/Button"
import classes from "./Banner.module.scss"

export default function Banner(props: { message: string, link: string, callback: Function }) {
  const { message, link, callback } = props

  return (<></>
    // <div className={classes.bannerWrapper}>
    //   {/* <div className={classes.banner}>
    //     {message}
    //     <div className={classes.buttons}>
    //       <Button text="View Site" callback={() => window.open(link)} />
    //       <Button text="Launch Site" callback={callback} />
    //     </div>
    //   </div> */}
    // </div>
  )
}