@import 'styles/variables';

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.content {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.logo {
    width: 200px;
    height: auto;
}

h1 {
    font-size: 24px;
    font-weight: 700;
    color: $charcoal;
    margin: 0;
}

h2 {
    font-size: 18px;
    font-weight: 500;
    color: $dark-grey;
    margin: 0;
}

.demoLink {
    color: $blue;
    text-decoration: none;
}

.footer {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
}

p {
    font-size: 14px;
    font-weight: 400;
    color: #999;
    margin: 0;
}

@media screen and (max-width: 400px) {
    .logo {
        width: 150px;
    }

    h1 {
        font-size: 20px;
    }

    h2 {
        font-size: 16px;
    }
}