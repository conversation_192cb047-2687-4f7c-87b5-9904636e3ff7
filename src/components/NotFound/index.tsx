import classes from "./NotFound.module.scss";
import logoSrc from 'assets/logo/Logo-Full.svg';

export default function NotFound(props: {}) {
    return (
        <div className={classes.container}>
            <div className={classes.content}>
                <img src={logoSrc} alt="Logo" className={classes.logo} />
                <h1>The selected client does not exist</h1>
                <h2>Try our demo? <a className={classes.demoLink} href={`${window.location.pathname.split('/')[0]}/arlington/en`}>Arlington</a></h2>
            </div>
            <div className={classes.footer}>
                <p>© {new Date().getFullYear()} Rep'd | <a href="https://www.repd.us">repd.us</a></p>
            </div>
        </div>
    );
}
