.quartileReporting {
  flex: 1;
  position: relative;
}

.chartHeader {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0px var(--padding-xs) 0px var(--padding-mini);
}

.quartileRow {
  align-self: stretch;
  height: 31px;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-5xs);
  width: 100%;
}

.cityLabel {
  position: relative;
  font-size: 35px;
  font-weight: 700;
}

.cityLabelWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-8xs-5) 0px 0px;
}

.cityBar {
  height: 24px;
  position: relative;
  border-radius: 0px var(--br-9xs) var(--br-9xs) 0px;
  background-color: var(--color-crimson-200);
  min-width: 5px;
  // max-width: 100px;
}

.cityPercentage {
  width: 32px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cityValue {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-10xs) 0px 0px;
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--color-crimson-100);
}

.cityRow {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-5xs);
}

.nationalRowIcon {
  position: absolute;
  top: 30px;
  left: 0px;
  width: 168px;
  height: 1px;
}

.firstRow {
  align-self: stretch;
  height: 31px;
  position: relative;
}

.div {
  position: relative;
  display: inline-block;
  min-width: 35px;
}

.chartContent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-7xs-8);
  text-align: left;
  font-size: var(--font-size-xs);
}

.chartContainer {
  flex: 0.9438;
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  padding: var(--padding-lgi) var(--padding-9xl) var(--padding-6xl) var(--padding-3xl);
  gap: var(--gap-xl-9);
  min-width: 165px;
}

.responseWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  padding: 0px;
  text-align: center;
  font-size: var(--font-size-base);
}

.VideoFeedbackContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.chartContainerChild {
  width: 143.1px;
  height: 1px;
  position: relative;
}

.chartContainer1 {
  flex: 1;
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 12px;
  gap: var(--gap-2xl-9);
  min-width: 185px;
  text-align: left;
  font-size: var(--font-size-15xl-7);
}

.avgCompletionRateWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0px var(--padding-11xs) 0px var(--padding-3xs);
}

.emptyRowChild {
  height: 1px;
  width: 168px;
  position: relative;
}

.emptyRow {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0px 0px var(--padding-76xl-9);
}

.b1 {
  width: 41px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.nationalLabel2 {
  align-self: stretch;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-9xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  position: absolute;
  top: -30px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-10xs) var(--padding-11xs) var(--padding-10xs) var(--padding-8xs);
  z-index: 1;
}

.nationalBar1 {
  width: 30px;
  position: relative;
  border-radius: var(--br-9xs) var (--br-9xs) 0px 0px;
  background-color: var(--color-crimson-200);
  z-index: 1;
}

.nationalBar {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 0px var(--padding-3xs);
}

.nationalLabel1 {
  flex: 1;
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-9xs);
}

.nationalPercentage {
  position: relative;
  display: inline-block;
  min-width: 41px;
}

.nationalBar3 {
  width: 30px;
  position: relative;
  border-radius: var(--br-9xs) var(--br-9xs) 0px 0px;
  background-color: var(--color-slategray-200);
  z-index: 1;
}

.nationalLabel {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-start;
  gap: var(--gap-4xs);
}

.nationalLabelWrapper {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0px var(--padding-base);
}

.frameContainer {
  align-self: stretch;
  height: 116.1px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 0px 0px var(--padding-12xs);
  box-sizing: border-box;
  gap: var(--gap-12xs-1);
  margin-top: -91px;
}

.emptyRow1 {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0px 0px var(--padding-102xl);
  margin-top: -91px;
}

.frameItem {
  align-self: stretch;
  height: 1px;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  flex-shrink: 0;
  margin-top: -91px;
}

.emptyRowParent {
  position: absolute;
  top: 7px;
  left: 50%;
  transform: translateX(-50%);
  width: 168px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0px 0px var(--padding-11xl);
  box-sizing: border-box;
  font-size: var(--font-size-sm);
  color: var(--color-crimson-100);
}

.emptyCity {
  align-self: stretch;
  position: relative;
  text-align: center;
}

.emptyCity1 {
  align-self: stretch;
  position: relative;
}

.emptyCityParent {
  position: absolute;
  top: 0px;
  left: 0px;
  background-color: var(--color-white);
  border: 2px solid var(--color-white);
  box-sizing: border-box;
  width: 44px;
  height: 139px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-mini);
  z-index: 1;
  text-align: right;
}

.city {
  position: absolute;
  top: 131.9px;
  left: 50%;
  transform: translateX(-80%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 51px;
  height: 15px;
}

.national {
  position: absolute;
  top: 131.9px;
  left: 50%;
  transform: translateX(35%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 51px;
  height: 15px;
  min-width: 51px;
}

.frameGroup {
  align-self: stretch;
  height: 146.9px;
  position: relative;
  font-size: var(--font-size-xs);
}

.chartContainer2 {
  flex: 1;
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  padding: var(--padding-lgi) var(--padding-6xl) var(--padding-3xs) var(--padding-mini);
  gap: var(--gap-2xl);
  min-width: 165px;
}

.chartContainerParent {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: 15px;
  width: 100%;
  text-align: center;
  font-size: var(--font-size-base);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

.chartContainer,
.chartContainer1,
.chartContainer2 {
  flex: 1;
  width: 100%;
  min-height: 250px;
  border-radius: var(--br-5xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  padding: var(--padding-base) var(--padding-5xl);
  gap: var(--gap-5xl);
  margin-bottom: var(--gap-xl);
}

@media screen and (max-width: 768px) {
  .chartContainerParent {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media screen and (max-width: 640px) {
  .chartContainerParent {
    flex-wrap: wrap;
    gap: 0;
    flex-direction: column;
  }
}

@media screen and (max-width: 450px) {
  .chartContainer {
    flex: 1;
  }

  .chartContainer1 {
    flex: 1;
  }
}