@import 'styles/variables';

.imageIcon {
  height: 250px;
  width: 342px;
  border-radius: var(--br-8xs-9);
  object-fit: cover;
  max-width: 100%;
}

.playIcon {
  height: 100px;
  width: 100px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  overflow: hidden;
  flex-shrink: 0;
  z-index: 1;
}

.imageParent {
  position: relative;
  flex: 0.883;
  border-radius: var(--br-8xs-9);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  min-width: 222px;
  max-width: 100%;
}

.title {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 500;
  font-family: inherit;
  display: -webkit-inline-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.viewMore {
  align-self: stretch;
  position: relative;
  font-size: var(--font-size-base);
  color: var(--color-crimson-100);
}

.question {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-7xs);
}

.userView {
  align-self: stretch;
  position: relative;
  font-size: var(--font-size-base);
  white-space: pre-wrap;
}

.questionParent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0px 0px var(--padding-5xl);
  box-sizing: border-box;
  gap: var(--gap-xl);
  min-width: 218px;
  max-width: 100%;
}

.frameParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-7xl);
  max-width: 100%;
  text-align: left;
  font-size: var(--font-size-5xl);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

.thumbnail {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  width: 100%;
  height: 250px;
  max-width: 400px;
  background: black;
  border-radius: 10px;
  color: $grey;
  font-size: 65px;
  text-align: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;

  video {
    max-width: 100%;
    max-height: 100%;
    border-radius: 10px;
    object-fit: cover;
    height: 100%;
  }

  button {
    position: absolute;
    width: 100%;
    height: 100%;
    color: transparent;
    background-color: transparent;
    border: none;
    border-radius: 10px;

    &:hover {
      color: white;
      background-color: black;
      opacity: 0.5;
    }

    svg {
      width: 30%;
      height: auto;
    }
  }
}

.meta {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: flex-start;
  flex-wrap: wrap;
  width: calc(100% - 300px - 20px);
  width: 100%;
}

.user {
  display: flex;
  align-items: center;
  margin-right: 10px;

  .name {
    margin-left: 10px;
    color: $dark-grey;
    font-size: 16px;
  }
}

.buttonBar {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: stretch;

  button {
    font-size: 18px;
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }

  .checkBoxButton {
    padding: 5px 15px;
    border-radius: 40px;
    border: 1px solid #d8d8d8;
    color: #34538d;
    background-color: white;

    input {
      margin-right: 10px;
      padding: 5px;
    }
  }

  .dropdownBar {
    padding-top: 10px;
    position: absolute;
    right: 0;
    top: 50px;
    z-index: 100;
    border-radius: 10px;
    background: white;
    box-shadow: 5px 5px 20px 5px $transparent-black-low;
    transition: opacity 5s, height 5s;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    button,
    div {
      margin-bottom: 10px;
      margin-left: 10px;
      margin-right: 10px;
      font-size: 16px;
      min-width: 200px;
      text-align: center;
    }
  }
}

.question {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .questionText {
    width: 100%;
    padding-top: 15px;
    font-size: 24px;
    font-weight: 300;
  }

  .attributes {
    display: flex;
    width: 100%;
    padding-top: 15px;
    font-size: 16px;
    color: $dark-grey;
    justify-content: space-between;
  }
}

@media screen and (max-width: 640px) {
  .frameParent {
    flex-wrap: wrap;
  }

  .thumbnail {
    width: 100%;
    margin-right: 0;
  }
}

@media screen and (max-width: 450px) {
  .imageParent {
    flex: 1;
  }

  .title {
    font-size: var(--font-size-lgi);
  }
}