import { FunctionComponent, useState } from "react";
import styles from "./VideoStatEmailComponent.module.scss";
import AnalyticsButton from "components/VoterAnalytics/Buttons";
import { BulkSendComputedInterface } from "interfaces";
import moment from "moment";
import Button from "shared/Button";
import { useServiceContext } from "services/ServiceProvider";

export type FrameComponentType = {
  className?: string;
  data: any;
  exportAnswer?: () => void;
};

const VideoStatEmailComponent: FunctionComponent<FrameComponentType> = ({
  className = "",
  data,
  exportAnswer,
}) => {
  const [videoOpened, setVideoOpened] = useState(false);
  const { adminStatsService } = useServiceContext();

  let thumbnailStyle = {
    backgroundImage: videoOpened ? '' : `url(${data.thumbnailUrl})`,
    backgroundSize: 'contain',
    backgroundPosition: 'center',
  };

  const handleDownloadAnswer = () => {
    window.location.href = data.videoUrl;
    adminStatsService?.trackEvent('VideoDetails', 'download_video');
  };

  return (
    <div className={[styles.frameParent, className].join(" ")}>
      <div className={styles.imageParent} data-answer-id={data.id}>
        <div className={styles.thumbnail} style={thumbnailStyle}>
          {videoOpened ? (
            <video
              width="100%"
              height="auto"
              controls
              src={data.videoUrl}
              autoPlay
              playsInline
              onEnded={() => setVideoOpened(false)}
            />
          ) : (
            <Button
              text=""
              iconText="play"
              callback={() => {
                setVideoOpened(true);
                adminStatsService?.trackEvent('VideoDetails', 'play_video');
              }}
            />
          )}
        </div>
      </div>
      <div className={styles.questionParent}>
        <div className={styles.question}>
          <h1 className={styles.title}>
            {data?.title}
          </h1>
        </div>
        <div className={styles.userView}>{`${data?.views} Views • ${moment(data?.createdAt || data?.question_created_at).format("Do MMM")} • ${data?.category}`}</div>
        <div className="flex flex-wrap gap-3">
          <AnalyticsButton
            type="Button Default"
            onClick={exportAnswer}
            className={"w-32 h-12 pt-3"}
          />
          <AnalyticsButton
            type="Button Default"
            title="Download Video"
            onClick={handleDownloadAnswer}
            className={"min-w-36 sm:min-w-32 h-12 pt-3"}
          />
        </div>
      </div>
    </div>
  );
};

export default VideoStatEmailComponent;
