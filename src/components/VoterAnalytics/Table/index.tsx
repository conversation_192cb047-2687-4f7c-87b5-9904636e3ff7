import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import { Button } from "shared/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "shared/ui/table"
import { BulkSendInterface } from "interfaces"
import { numberWithCommas } from "utils/utils"
import { useUIStore } from "hooks/zustand/uiStore"
import EmailDetailsStats from "../EmailDetailsStats"
import { useServiceContext } from "services/ServiceProvider"

export type EmailRowType = {
  id: string;
  date: string;
  subject: string;
  audience: string;
  delivered: number;
  openRate: string;
  clickRate: string;
}

export const columns: ColumnDef<BulkSendInterface>[] = [
  {
    accessorKey: "date",
    header: ({ column }) => {
      return (
        <Button
          // @ts-ignore
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-translate-x-4"
        >
          Date
        </Button>
      )
    },
    cell: ({ row }) => <div>{row.getValue("date")}</div>,
  },
  {
    accessorKey: "subject",
    header: ({ column }) => {
      return (
        <Button
          // @ts-ignore
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-translate-x-4"
        >
          Subject
        </Button>
      )
    },
    cell: ({ row }) => <div>{row.getValue("subject") ? row.getValue("subject") : "-"}</div>,
  },
  {
    accessorKey: "saved_list_name",
    header: ({ column }) => {
      return (
        <Button
          // @ts-ignore
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-translate-x-4"
        >
          Audience
        </Button>
      )
    },
    cell: ({ row }) => <div>{row.getValue("saved_list_name")}</div>,
  },
  {
    accessorKey: "delivered",
    header: ({ column }) => {
      return (
        <Button
          // @ts-ignore
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-translate-x-4"
        >
          Delivered
        </Button>
      )
    },
    cell: ({ row }) => <div>{numberWithCommas(Number(row.getValue("delivered")))}</div>,
  },
  {
    accessorKey: "opensString",
    header: ({ column }) => {
      return (
        <Button
          // @ts-ignore
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-translate-x-4"
        >
          Open Rate
        </Button>
      )
    },
    cell: ({ row }) => <div>{row.getValue("opensString")}</div>,
  },
  {
    accessorKey: "clicksString",
    header: ({ column }) => {
      return (
        <Button
          // @ts-ignore
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="-translate-x-4"
        >
          Click Rate
        </Button>
      )
    },
    cell: ({ row }) => <div>{row.getValue("clicksString")}</div>,
  }
]

const mobileHeaders = ["Date", "Subject", "Audience", "Delivered", "Open Rate", "Click Rate"];

export function EmailTable({
  data,
  openDetailsModal,
  isLoading = false
}: {
  data: BulkSendInterface[];
  openDetailsModal: (index: number) => void;
  isLoading?: boolean;
}) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 })
  const uiType = useUIStore(state => state.uiType)
  const [isMobile, setIsMobile] = React.useState(false)
  const { adminStatsService } = useServiceContext()

  // Check if we're on mobile
  React.useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 725)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)

    return () => {
      window.removeEventListener('resize', checkIfMobile)
    }
  }, [])

  const topBulkSend = React.useMemo(() => {
    return data.reduce((max, bulkSend) =>
      bulkSend.delivered > max.delivered ? bulkSend : max,
      data[0]
    );
  }, [data]);

  const processedData = uiType === "pdf" ? ((topBulkSend && [topBulkSend]) || []) : data;

  const table = useReactTable({
    data: processedData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
    },
  })

  const visibleRows = table.getRowModel().rows;

  return (
    <div className="w-full">
      <div className="rounded-md">
        {isLoading ? (
          <div className="h-24 flex justify-center items-center">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="ml-2">Loading...</span>
          </div>
        ) : visibleRows?.length ? (
          <div>
            {!isMobile ? (
              // Desktop view - standard table layout
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => {
                        return (
                          <TableHead key={header.id}>
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                          </TableHead>
                        )
                      })}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row, i) => (
                      <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                        className="h-12 cursor-pointer"
                        onClick={() => {
                          const rowIndex = i + pagination.pageIndex * pagination.pageSize;
                          openDetailsModal(rowIndex);
                          const rowData = data[rowIndex];
                          adminStatsService?.trackEvent('EmailsTable', 'row_click');
                        }}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id}>
                            {flexRender(
                              cell.column?.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns?.length}
                        className="h-24 text-center"
                      >
                        No results.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            ) : (
              // Mobile view - compact cards with multiple items per page
              <div className="mobile-emails-grid">
                <h2 className="mobile-header">Status</h2>
                {visibleRows.map((row, rowIndex) => {
                  const actualRowIndex = rowIndex + pagination.pageIndex * pagination.pageSize;
                  return (
                    <div
                      key={row.id}
                      className="mobile-email-card"
                      onClick={() => {
                        openDetailsModal(actualRowIndex);
                        const rowData = data[actualRowIndex];
                        adminStatsService?.trackEvent('EmailsTable', 'mobile_card_click');
                      }}
                    >
                      <table className="mobile-email-table">
                        <tbody>
                          {row.getVisibleCells().map((cell, cellIndex) => {
                            const headerContent = typeof cell.column.columnDef.header === "function"
                              ? cell.column.id.charAt(0).toUpperCase() + cell.column.id.slice(1)
                              : cell.column.columnDef.header;

                            return (
                              <tr key={cell.id} className="mobile-email-row">
                                <td className="mobile-email-header">{mobileHeaders[cellIndex]}</td>
                                <td className="mobile-email-value">
                                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  );
                })}
              </div>
            )}
            {data.length > pagination.pageSize && uiType !== "pdf" && (
              <div className="flex justify-center gap-2 items-center mt-4 text-sm">
                <Button
                  onClick={() => {
                    table.previousPage();
                    adminStatsService?.trackEvent('EmailsTable', 'pagination');
                  }}
                  disabled={!table.getCanPreviousPage()}
                >
                  &lt;
                </Button>
                <span>
                  {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                </span>
                <Button
                  onClick={() => {
                    table.nextPage();
                    adminStatsService?.trackEvent('EmailsTable', 'pagination');
                  }}
                  disabled={!table.getCanNextPage()}
                >
                  &gt;
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="h-24 flex justify-center items-center">
            No results.
          </div>
        )}
        {data.length >= 1 && uiType === "pdf" && <EmailDetailsStats data={topBulkSend} className="pt-5 max-w-3xl mx-auto" />}
      </div>
    </div>
  )
}