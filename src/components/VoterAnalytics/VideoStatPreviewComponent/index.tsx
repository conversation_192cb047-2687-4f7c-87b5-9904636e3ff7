import { FunctionComponent, useMemo } from "react";
import styles from "./VideoStatPreviewComponent.module.scss";
import { VideoStatInterface } from "interfaces";
import { useServiceContext } from "services/ServiceProvider";

export type FrameComponent1Type = {
  className?: string;
  data: any;
};

const VideoStatPreviewComponent: FunctionComponent<FrameComponent1Type> = ({
  className = "",
  data,
}) => {
  const { statService } = useServiceContext();

  const quartileData = useMemo(() => {
    return statService?.computedValues.quartileData
  }, [statService?.computedValues.quartileData]);

  const quartiles = [
    { label: '100%', value: quartileData?.find(item => item.answer_id === data?.id)?.["75-100"] || 0 },
    { label: '75%', value: quartileData?.find(item => item.answer_id === data?.id)?.["50-75"] || 0 },
    { label: '50%', value: quartileData?.find(item => item.answer_id === data?.id)?.["25-50"] || 0 },
    { label: '25%', value: quartileData?.find(item => item.answer_id === data?.id)?.["0-25"] || 0 },
  ];

  const feedbackData = useMemo(() => {
    if (!data?.videoFeedbackStats) return { likesPercentage: 0, dislikesPercentage: 0 };

    const likes = Number(data?.videoFeedbackStats.likes) || 0;
    const dislikes = Number(data?.videoFeedbackStats.dislikes) || 0;
    if (likes + dislikes === 0) return { likesPercentage: 0, dislikesPercentage: 0 };

    const likesPercentage = likes / (likes + dislikes) * 100;
    const dislikesPercentage = dislikes / (likes + dislikes) * 100;

    return { likesPercentage, dislikesPercentage };
  }, [data?.videoFeedbackStats]);

  const completionRate = useMemo(() => {
    const castedCompletionRate = Number(statService?.computedValues.completedWatchRates[0]);
    if (!castedCompletionRate || castedCompletionRate < 0) return 0;

    return castedCompletionRate;
  }, [statService?.computedValues.completedWatchRates]);

  return (
    <div className={[styles.chartContainerParent, className].join(" ")}>
      {data?.videoUrl &&
        <div className={styles.chartContainer}>
          <div className={styles.chartHeader}>
            <div className={styles.quartileReporting}>Quartile Reporting</div>
          </div>
          <div className={styles.chartContent}>
            {quartiles.map((quartile, index) => (
              <div className="w-full" key={index}>
                <div className={styles.quartileRow}>
                  <div className={styles.cityLabelWrapper}>
                    <div className={styles.div}>{quartile.label}</div>
                  </div>
                  <div className={styles.cityBar} style={{ width: `${quartile.value}%` }} />
                  <div className={styles.cityValue}>
                    <b className={styles.cityPercentage}>{quartile.value}%</b>
                  </div>
                </div>
                {index < quartiles.length - 1 && (
                  <img
                    className={styles.nationalRowIcon}
                    loading="lazy"
                    alt=""
                    src="/analytics/chart/divider"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      }
      <div className={styles.chartContainer1}>
        <div className={styles.responseWrapper}>
          <div className={styles.quartileReporting}>Response</div>
        </div>
        <div className={styles.VideoFeedbackContainer}>
          <img
            className={styles.feedbackIcon}
            loading="lazy"
            alt=""
            src="/analytics/svg-icons/thumbs-up.svg"
          />
          <b className={styles.cityLabel}>{Math.round(feedbackData?.likesPercentage)}%</b>
        </div>
        <img
          className={styles.chartContainerChild}
          loading="lazy"
          alt=""
          src="/analytics/chart/divider"
        />
        <div className={styles.VideoFeedbackContainer}>
          <img
            className={styles.feedbackIcon}
            loading="lazy"
            alt=""
            src="/analytics/svg-icons/thumbs-down.svg"
          />
          <b className={styles.cityLabel}>{Math.round(feedbackData?.dislikesPercentage)}%</b>
        </div>
      </div>
      <div className={styles.chartContainer2}>
        <div className={styles.avgCompletionRateWrapper}>
          <div className={styles.quartileReporting}>Avg. Completion Rate</div>
        </div>
        <div className={styles.frameGroup}>
          <div className={styles.emptyRowParent}>
            <div className={styles.emptyRow}>
              <img
                className={styles.emptyRowChild}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <div className={styles.frameContainer}>
              <div className={styles.nationalLabelWrapper}>
                <div className={styles.nationalLabel}>
                  <div className={styles.nationalLabel1}>
                    <div className={styles.nationalLabel2}>
                      <b className={styles.b1}>{`${Math.round(Number(statService?.computedValues.completedWatchRate))}%`}</b>
                    </div>
                    <div className={styles.nationalBar}>
                      <div className={styles.nationalBar1} style={{ height: Number(statService?.computedValues.completedWatchRate) * 1.13 }} />
                    </div>
                  </div>
                  <div className={styles.nationalLabel1}>
                    <div className={styles.nationalLabel2}>
                      <b className={styles.nationalPercentage}>{`${Math.round(data?.nationalCompletionRate)}%`}</b>
                    </div>
                    <div className={styles.nationalBar}>
                      <div className={styles.nationalBar3} style={{ height: Math.round(data?.nationalCompletionRate) * 1.13 }} />
                    </div>
                  </div>
                </div>
              </div>
              <img
                className={styles.secondNationalLabel}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <div className={styles.emptyRow1}>
              <img
                className={styles.emptyRowChild}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <div className={styles.emptyRow1}>
              <img
                className={styles.emptyRowChild}
                loading="lazy"
                alt=""
                src="/analytics/chart/divider"
              />
            </div>
            <img
              className={styles.frameItem}
              loading="lazy"
              alt=""
              src="/analytics/chart/divider"
            />
          </div>
          <div className={styles.emptyCityParent}>
            <div className={styles.emptyCity}>100%</div>
            <div className={styles.emptyCity1}>75%</div>
            <div className={styles.emptyCity1}>50%</div>
            <div className={styles.emptyCity1}>25%</div>
            <div className={styles.emptyCity1}>0%</div>
          </div>
          <div className={styles.city}>City</div>
          <div className={styles.national}>National</div>
        </div>
      </div>
    </div>
  );
};

export default VideoStatPreviewComponent;
