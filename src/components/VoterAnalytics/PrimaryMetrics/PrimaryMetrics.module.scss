.viewsIconContainer {
  height: 48px;
  width: 48px;
  position: relative;
}

.totalVideoViews {
  align-self: stretch;
  position: relative;
}

.metricSeparator {
  position: relative;
  font-size: var(--font-size-35xl);
  display: flex;
  color: var(--color-black);
}

.viewsTimeMetrics {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.primaryMetrics {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-xl);
  text-align: center;
  font-size: var(--font-size-xl);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

@media screen and (max-width: 975px) {
  .metricSeparator {
    font-size: var(--font-size-24xl);
  }
}

@media screen and (max-width: 450px) {
  .totalVideoViews {
    font-size: var(--font-size-base);
  }

  .metricSeparator {
    font-size: var(--font-size-13xl);
  }
}