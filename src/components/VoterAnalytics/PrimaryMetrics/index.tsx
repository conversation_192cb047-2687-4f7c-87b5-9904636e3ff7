import { FunctionComponent, useMemo, type CSSProperties } from "react";
import styles from "./PrimaryMetrics.module.scss";

export type PrimaryMetricsType = {
  className?: string;
  viewsIconContainer?: string;
  totalVideoViews?: string;
  metricSeparator?: string;

  /** Style props */
  viewsIconContainerOverflow?: CSSProperties["overflow"];
  metricSeparatorWidth?: CSSProperties["width"];
};

const PrimaryMetrics: FunctionComponent<PrimaryMetricsType> = ({
  className = "",
  viewsIconContainer,
  viewsIconContainerOverflow,
  totalVideoViews,
  metricSeparator,
  metricSeparatorWidth,
}) => {
  const viewsIconContainerStyle: CSSProperties = useMemo(() => {
    return {
      overflow: viewsIconContainerOverflow,
    };
  }, [viewsIconContainerOverflow]);

  const metricSeparatorStyle: CSSProperties = useMemo(() => {
    return {
      width: metricSeparatorWidth,
    };
  }, [metricSeparatorWidth]);

  return (
    <div className={[styles.primaryMetrics, className].join(" ")}>
      <img
        className={styles.viewsIconContainer}
        loading="lazy"
        alt=""
        src={viewsIconContainer}
        style={viewsIconContainerStyle}
      />
      <div className={styles.viewsTimeMetrics}>
        <div className={styles.totalVideoViews}>{totalVideoViews}</div>
        <b className={styles.metricSeparator} style={metricSeparatorStyle}>
          {metricSeparator}
        </b>
      </div>
    </div>
  );
};

export default PrimaryMetrics;
