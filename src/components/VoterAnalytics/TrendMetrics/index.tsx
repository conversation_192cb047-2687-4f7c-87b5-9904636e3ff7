import { FunctionComponent } from "react";
import styles from "./TrendMetrics.module.scss";

export type TrendMetricsType = {
  className?: string;
  placeholder1?: number;
};

const TrendMetrics: FunctionComponent<TrendMetricsType> = ({
  className = "",
  placeholder1,
}) => {
  return (
    <div className={[styles.trendMetrics, className].join(" ")}>
      <b className={styles.placeholder}>{placeholder1}</b>
      <div className={styles.trendSummary}>
        <div className={styles.totalVolume}>Total Volume</div>
      </div>
    </div>
  );
};

export default TrendMetrics;
