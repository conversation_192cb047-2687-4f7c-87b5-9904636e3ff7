.placeholderIcon {
  width: 30px;
  height: 30px;
  position: relative;
}

.trendValues {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-lgi) 0 0;
}

.placeholder,
.totalVolume {
  position: relative;
}

.totalVolume {
  align-self: stretch;
}

.trendMetrics,
.trendSummary {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.trendSummary {
  flex-direction: column;
  padding: var(--padding-lgi) 0 0;
  box-sizing: border-box;
  min-width: 94px;
  font-size: var(--font-size-5xl);
  color: var(--color-slategray);
}

.trendMetrics {
  align-self: stretch;
  flex-direction: row;
  gap: var(--gap-3xs);
  text-align: center;
  font-size: var(--font-size-35xl);
  color: var(--color-black);
  //font-family: var(--font-plus-jakarta-sans);
}

@media screen and (max-width: 975px) {
  .placeholder {
    font-size: var(--font-size-24xl);
  }
}

@media screen and (max-width: 640px) {
  .trendSummary {
    padding-top: 10px;
  }
}

@media screen and (max-width: 450px) {
  .placeholder {
    font-size: var(--font-size-13xl);
  }

  .totalVolume {
    font-size: var(--font-size-lgi);
  }

  .trendMetrics {
    flex-wrap: wrap;
  }
}