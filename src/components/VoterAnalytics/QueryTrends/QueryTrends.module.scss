.trending1 {
  position: relative;
  font-weight: 500;
  display: inline-block;
  min-width: 85px;
}

.trendingTitle {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xs-5) 0 0;
}

.trendingChild {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
}

.trending {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-xl);
}

.queries1 {
  position: relative;
  font-weight: 500;
}

.trendData {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-lgi-7);
}

.facesIcon {
  height: 24px;
  width: 24px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.placeholder {
  flex: 1;
  position: relative;
}

.iconData,
.iconTrend,
.iconValues,
.trendItem {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.iconValues {
  align-self: stretch;
  flex-direction: row;
  gap: var(--gap-base);
}

.iconData,
.iconTrend,
.trendItem {
  flex-direction: column;
  gap: var(--gap-xl);
}

.iconData {
  align-self: stretch;
}

.iconTrend,
.trendItem {
  text-align: right;
  font-size: var(--font-size-xl);
}

.trendItem {
  align-self: stretch;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  text-align: center;
  font-size: var(--font-size-35xl);
  color: var(--color-black);
}

.trend {
  flex: 1;
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-7xl) var(--padding-6xl) var(--padding-20xl);
  gap: var(--gap-5xl);
  min-width: 382px;
  max-width: 100%;
}

.questions {
  position: relative;
  font-weight: 500;
  display: inline-block;
  min-width: 101px;
}

.frameGroup,
.homelessnessParent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-xl);
}

.frameGroup {
  flex: 1;
  gap: var(--gap-5xl);
  min-width: 169px;
}

.facesGroup,
.facesParent,
.frameWrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.facesParent {
  flex: 1;
  justify-content: flex-start;
  gap: var(--gap-base);
}

.facesGroup,
.frameWrapper {
  align-self: stretch;
}

.frameWrapper {
  justify-content: flex-start;
  padding: 0 0 0 var(--padding-12xs);
}

.facesGroup {
  gap: var(--gap-mini);
}

.facesGroup,
.frameParent,
.trend1 {
  justify-content: flex-start;
}

.frameParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: var(--gap-91xl);
  text-align: right;
  color: var(--color-black);
}

.trend1 {
  flex: 1;
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  flex-direction: column;
  padding: var(--padding-7xl) var(--padding-6xl) var(--padding-15xl) var(--padding-7xl);
  gap: var(--gap-5xl);
  min-width: 382px;
}

.queries,
.queryTrends,
.trend1,
.trendList {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}

.trendList {
  align-self: stretch;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: var(--gap-9xl);
}

.queries,
.queryTrends {
  box-sizing: border-box;
  padding-bottom: 100px;
}

.queries {
  flex: 1;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  overflow: hidden;
  flex-direction: column;
  justify-content: flex-start;
  padding: var(--padding-5xl) var(--padding-5xl) var(--padding-5xl) var(--padding-6xl);
  gap: var(--gap-xl);
  flex-shrink: 0;
}

.queryTrends {
  width: 1180px;
  flex-direction: row;
  justify-content: flex-end;
  text-align: left;
  font-size: var(--font-size-xl);
  color: var(--color-slategray);
  //font-family: var(--font-plus-jakarta-sans);
}

@media screen and (max-width: 700px) {
  .trendItem {
    flex-wrap: wrap;
  }

  .trend {
    min-width: 100%;
  }

  .frameParent {
    flex-wrap: wrap;
  }

  .trend1 {
    min-width: 100%;
  }
}

@media screen and (max-width: 450px) {
  .trending1 {
    font-size: var(--font-size-base);
  }

  .trending {
    flex-wrap: wrap;
  }

  .placeholder,
  .queries1,
  .questions {
    font-size: var(--font-size-base);
  }

  .frameParent {
    gap: var(--gap-36xl);
  }

  .queries {
    padding-top: var(--padding-xl);
    padding-bottom: var(--padding-xl);
    box-sizing: border-box;
  }
}

@media (max-width: 640px) {
  .trending1 {
    font-size: 20px;
    color: #445472;
  }

  .queries {
    padding: 20px;
  }

  .trend {
    padding: 20px;
    gap: 5px;
  }

  .trend1 {
    padding: 20px;
    gap: 5px;
  }

  .trendList {
    gap: 10px;
  }
}