import { FunctionComponent } from "react";
import { motion } from "framer-motion";
import TrendMetrics from "components/VoterAnalytics/TrendMetrics";
import TrendingCategory from "components/VoterAnalytics/TrendingCategory";
import styles from "./QueryTrends.module.scss";
// import { getPercent, mockData } from "utils/mockData";
import { TrendingComputedInterface } from "interfaces";

export type QueryTrendsType = {
  className?: string;
  trendingStats: TrendingComputedInterface;
};

const QueryTrends: FunctionComponent<QueryTrendsType> = ({
  className = "",
  trendingStats
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: 40 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: 0.6 }}
      className={[styles.queryTrends, className].join(" ")}>
      <div className={styles.queries}>
        <div className={styles.trending}>
          <div className={styles.trendingTitle}>
            <div className={styles.trending1}>Trending</div>
          </div>
        </div>
        <div className={styles.trendList}>
          <div className={styles.trend}>
            <div className={styles.queries1}>Queries</div>
            <div className={styles.trendItem}>
              <TrendMetrics placeholder1={trendingStats?.queries?.reduce((acc, query) => acc + Number(query.question_count), 0)} />
              {trendingStats?.queries?.slice(0, 3).map((query) => (
                <TrendingCategory
                  key={query.category + '1'}
                  name={query.category}
                  sentiment={query.sentiment ? Math.round((3 - query.sentiment) / 2 * 100) : 50} />
              ))}
            </div>
          </div>
          <div className={styles.trend1}>
            <div className={styles.questions}>Questions</div>
            <div className={styles.frameParent}>
              <div className={styles.frameGroup}>
                <TrendMetrics placeholder1={trendingStats?.questions?.reduce((acc, question) => acc + Number(question.question_count), 0)} />
                {trendingStats?.questions?.slice(0, 3).map((questions) => (
                  <TrendingCategory
                    key={questions.category + '2'}
                    name={questions.category}
                    sentiment={questions.sentiment ? Math.round((3 - questions.sentiment) / 2 * 100) : 50} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default QueryTrends;
