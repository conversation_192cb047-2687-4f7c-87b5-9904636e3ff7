.fi3024539Icon,
.filter {
  position: relative;
  flex-shrink: 0;
}

.fi3024539Icon {
  width: 24px;
  height: 24px;
  overflow: hidden;
}

.filter {
  text-decoration: none;
  width: 40px;
  font-size: var(--font-size-base);
  font-weight: 500;
  //font-family: var(--font-plus-jakarta-sans);
  color: var(--color-black);
  text-align: left;
  display: inline-block;
}

.filter1,
.filterIcons {
  display: flex;
  justify-content: flex-start;
}

.filterIcons {
  align-self: stretch;
  flex-direction: row;
  align-items: center;
  gap: var(--gap-3xs);
}

.filter1 {
  cursor: pointer;
  border: 1px solid var(--color-silver);
  padding: var(--padding-smi) var(--padding-sm);
  background-color: var(--color-white);
  border-radius: var(--br-9xs);
  flex-direction: column;
  align-items: flex-start;
}

.filterContainer {
  position: relative;
}

.filter1:hover {
  opacity: 60%;
  transition-duration: 150ms;
}

.filterPopup {
  cursor: default;
  position: absolute;
  background-color: white;
  bottom: -200px;
  left: -576px;
  width: 680px;
  display: flex;
  flex-direction: column;
  align-items: end;
  font-size: 16px;
  z-index: 10;
  padding: 26px;
  gap: 20px;
  border-radius: 10px;
  border: 1px solid #DCE5ED;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.07);
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.clearAll {
  cursor: pointer;
}

.clearAll:hover {
  opacity: 80%;
}

.filterSelects {
  width: 100%;
  gap: 30px;
  display: flex;
  justify-content: space-between;
}

.filterSelect {
  flex: 1;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #B8BFCA;
}

.selectItem {
  display: flex;
  align-items: center;
  padding: 6px 20px 6px 20px;
  height: 35px;
  cursor: pointer;
  gap: 0px;
}

.selectItem:hover {
  background-color: rgb(248, 247, 247);
  transition: background-color 100ms linear;
}

.selectMenu {
  border-radius: 4px;
  border: 1px solid #DCE5ED;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.07) !important;
  max-height: 300px !important;
}

.selectInput {
  padding-left: 14px !important;
  border: none !important;
  box-shadow: none !important;
}

.checkbox {
  color: #B8BFCA;
  border-width: 1px;
}

.checkboxRoot:hover {
  background-color: transparent !important
}

.checkboxLabel {
  width: 100%;
  font-size: 16px;
  color: black;
  font-weight: 500;
}

.checkboxInput {
  border: 1px solid red !important;
  background-color: red !important
}

.doneButton {
  width: fit-content;
  padding: 5px 10px;
  background-color: var(--color-crimson-100);
  color: white;
  border-radius: 6px;
}

.hide {
  display: none;
}

.flex1 {
  flex: 1;
}

.narrow {
  width: 550px !important;
  bottom: -190px;
  left: -447px;
}

.mobileLocationSelectContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobileLocationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #DCE5ED;
}

.backButton {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
}

.headerTitle {
  font-weight: 500;
  font-size: 16px;
}

.mobileLocationList {
  display: flex;
  flex-direction: column;
  max-height: calc(75vh - 125px);
  overflow-y: auto;
  width: 100%;
  padding-top: 5px;
}

.mobileLocationItem {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  font-size: 15px;
}

.mobileLocationItem:active {
  background-color: #F8F9FA;
}

@media (max-width: 640px) {
  .hideOnMobile {
    display: none;
  }

  .shadow {
    position: fixed;
    top: 0;
    left: 0;
    background: #00000055;
    height: 100vh;
    width: 100vh;
    z-index: 10;
  }

  .filter1 {
    width: 40px;
    height: 40px;
    padding: 9px;
  }

  .filterPopup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100% !important;
    max-height: 75vh;
    overflow-y: auto;
    border-radius: 10px 10px 0 0;
    padding: 20px;
  }

  .filterSelects {
    flex-direction: column;
    gap: 15px;
    width: 100%;
  }

  .filterSelect {
    width: 100%;
  }

  /* Ensure date picker takes full width on mobile */
  .filterSelects>div {
    width: 100%;
    max-width: 100%;
  }

  /* Override max-width for DatePicker button on mobile */
  .filterSelects button[id="date"] {
    max-width: 100% !important;
    width: 100% !important;
  }

  .doneButton {
    margin-top: 10px;
    align-self: center;
    width: 100%;
    padding: 10px;
  }
}