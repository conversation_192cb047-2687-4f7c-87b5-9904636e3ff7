@import 'src/styles/variables';

.NgpVanStats {
    width: 100%;

    .table {
        width: 100%;
        margin: 0 0 100px 0;
    }

    .statTotal {
        width: calc(33.33% - 21px);
    }

    .statTotal:last-child {
        width: calc(33.33%);
    }

    .select {
        label {
            min-width: 150px
        }

        select {
            min-width: calc(100% - 150px - 200px);
        }

        // suffix
        div {
            min-width: 200px;
        }
    }
}

@media screen and (max-width: 640px) {
    .NgpVanStats {
        .statTotal {
            width: 100%;
            margin: 0;
            margin-top: 20px;
        }

        .statTotal:last-child {
            width: 100%;
            margin-right: 0;
            margin-top: 20px;
        }
    }
}