import React, { useState } from 'react';
import cn from 'classnames';
import { Form, FormikProvider, useFormik } from "formik";

import { NgpVanEmailStatInterface, ClientInterface } from 'interfaces';
import { StatService } from 'services';

import classes from './NgpVan.module.scss';
import statClasses from 'pages/VoterAnalytics/VoterAnalytics.module.scss';
import FormClasses from 'shared/Forms/Forms.module.scss';

export default function NgpVan(props: { client: ClientInterface, stats: NgpVanEmailStatInterface[], service: StatService }) {
  const { client, stats, service } = props;

  const [question, setQuestion] = useState('all')
  const [savedListId, setSavedListId] = useState('all')

  const formik = useFormik({
    initialValues: {
      question: 'all',
      listId: 'all'
    },
    onSubmit: (values, actions) => { }
  });

  function renderDisconnectedMessage() {
    if ((client.ngpVanApiKey || client.clientType === 'Government') && stats.length === 0)
      return <>
        <p> <strong>There are no email stats at this time.</strong> </p>

        <p>
          Please make sure there are video answers which have been sent using {client.clientType === 'Campaign' ? 'NGP VAN' : 'a CSV upload'}.
        </p>
      </>;
    else return;
  }

  function renderEmptyMessage() {
    if (!client.ngpVanApiKey && client.clientType === 'Campaign')
      return <>
        <p> <strong>You haven't connected to NGP VAN yet.</strong> </p>

        {/* <button> Watch Our Video Guide </button> */}

        <p>
          You can request an API key following NGP VAN's helpdesk article
          <a href="https://help.ngpvan.com/van/s/article/2969508-requesting-and-approving-api-keys" target="_blank" rel="noreferrer">here</a>.
        </p>
      </>;
    else return;
  }

  function renderForm() {
    function setOptions(kind: string) {
      var options: any;

      if (kind === 'question')
        options = stats.map(stat => {
          return <option key={stat.question} value={stat.question}> {stat.question} </option>
        })

      if (kind === 'listId') {
        var ids: string[] = [];
        options = []

        for (var i = 0; i < stats.length; i++) {
          if (question !== 'all' && stats[i].question !== question) continue;

          for (var i2 = 0; i2 < stats[i].emails.length; i2++) {
            const list = stats[i].emails[i2].savedList;
            var isDuplicated: boolean = false;

            for (var i3 = 0; i3 < ids.length; i3++)
              if (ids[i3] === list) isDuplicated = true

            if (!isDuplicated) ids.push(list)
          }
        }

        for (var i4 = 0; i4 < ids.length; i4++)
          options.push(<option key={ids[i4]} value={ids[i4]}> {ids[i4]} </option>)
      }

      return [
        <option key={`${kind}-all`} value='all'>All</option>,
        ...options
      ];
    }

    return (
      <FormikProvider value={formik}>
        <Form className={FormClasses.Form}>
          <div className={cn(FormClasses.InputWithLabel, FormClasses.suffixed, classes.select)}>
            <label>Select an Answer</label>
            <select name="question" onChange={(event) => setQuestion(event.target.value)} defaultValue={question}>
              {setOptions('question')}
            </select>
            <div className={FormClasses.suffix}>{service.getNgpVanDate(question)}</div>
          </div>

          <div className={cn(FormClasses.InputWithLabel, FormClasses.suffixed, classes.select)}>
            <label>Select a List</label>
            <select name="listId" onChange={(event) => setSavedListId(event.target.value)} defaultValue={savedListId}>
              {setOptions('listId')}
            </select>
            <div className={FormClasses.suffix}>{service.getNgpVanTimesSent(question)}</div>
          </div>
        </Form>
      </FormikProvider>
    )
  }

  function statTotalsTempate() {
    if (!client.ngpVanApiKey || stats.length === 0) return <></>;

    const result: any = service.ngpVanStats(question, savedListId)

    return <>
      <div className={statClasses.title}>Mail Stats</div>

      {renderForm()}

      <div className={statClasses.Row}>
        <div className={cn(statClasses.StatTotal, classes.statTotal)}>
          <div className={statClasses.title}>Total Opens</div>
          <div className={statClasses.centerNumber}>{result.opensTotal || 0}</div>
        </div>
        <div className={cn(statClasses.StatTotal, classes.statTotal)}>
          <div className={statClasses.title}>Total CTA Clicks</div>
          <div className={statClasses.centerNumber}>{result.ctaTotal || 0}</div>
        </div>
        <div className={cn(statClasses.StatTotal, classes.statTotal, statClasses.edge)}>
          <div className={statClasses.title}>Unique Opens</div>
          <div className={statClasses.centerNumber}>{result.opensUnique || 0}</div>
        </div>
      </div>
      <div className={cn(statClasses.Row, statClasses.bottom)}>
        <div className={cn(statClasses.StatTotal, classes.statTotal)}>
          <div className={statClasses.title}>Unique Clicks</div>
          <div className={statClasses.centerNumber}>{result.clicksUnique || 0}</div>
        </div>
        <div className={cn(statClasses.StatTotal, classes.statTotal)}>
          <div className={statClasses.title}>Unsubscribes</div>
          <div className={statClasses.centerNumber}>{result.unsubscribes || 0}</div>
        </div>
        <div className={cn(statClasses.StatTotal, classes.statTotal, statClasses.edge)}>
          <div className={statClasses.title}>Donation Clicks</div>
          <div className={statClasses.centerNumber}>{result.donations || 0}</div>
        </div>
      </div>
    </>
  }

  return (
    <div className={classes.NgpVanStats}>
      <div className={cn(statClasses.Table, classes.table)}>
        {renderDisconnectedMessage()}
        {renderEmptyMessage()}
        {statTotalsTempate()}
      </div>
    </div>
  );
}
