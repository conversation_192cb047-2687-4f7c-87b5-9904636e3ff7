import { format, addYears, subYears } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"
import { useState } from "react"

import { cn } from "utils/utils"
import { But<PERSON> } from "shared/ui/button"
import { Calendar } from "shared/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "shared/ui/popover"

export function DatePickerWithRange({
  className,
  date,
  setDate
}: { date: DateRange | undefined; className?: string; setDate: (date: DateRange | undefined) => void; }) {
  const [today] = useState(new Date());
  const [calendarOpen, setCalendarOpen] = useState(false);

  const selectToday = () => {
    setDate({ from: today, to: today });
  };

  const closeCalendar = () => {
    setCalendarOpen(false);
  };

  return (
    <div className={cn("grid gap-2 w-full", className && className)}>
      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full max-w-[300px] justify-start text-left font-normal border-[#b8bfca] rounded-sm shadow-none",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            selected={date}
            defaultMonth={date?.from || today}
            onSelect={setDate}
            numberOfMonths={2}
          />
          <div className="flex justify-center gap-2 m-2">
            <Button onClick={selectToday}>
              Today
            </Button>
            <Button onClick={closeCalendar}>
              Done
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

export function DatePicker({
  className,
  date,
  setDate
}: { date: Date | undefined; className?: string; setDate: (date: Date | undefined) => void; }) {
  const [today] = useState(new Date());
  const [calendarOpen, setCalendarOpen] = useState(false);

  const closeCalendar = () => {
    setCalendarOpen(false);
  };

  return (
    <div className={cn("grid gap-2 w-full", className && className)}>
      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full max-w-[300px] justify-start text-left font-normal border-[#f0f0f0] rounded-sm shadow-none",
              !date && "text-[#aaa]"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? (
              format(date, "LLL dd, y")
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 z-[9999]" align="start">
          <Calendar
            initialFocus
            mode="single"
            selected={date}
            defaultMonth={date || today}
            onSelect={setDate}
            numberOfMonths={1}
          />
          <div className="flex justify-center gap-2 m-2">
            <Button onClick={closeCalendar}>
              Done
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}