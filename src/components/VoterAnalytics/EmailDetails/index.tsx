import { FunctionComponent, useRef } from "react";
import styles from "./EmailDetails.module.scss";
import VideoStatEmailComponent from "components/VoterAnalytics/VideoStatEmailComponent";
import EmailDetailsStats from "components/VoterAnalytics/EmailDetailsStats";
import { BulkSendComputedInterface } from "interfaces";
import html2canvas from "html2canvas";
import { useServiceContext } from "services/ServiceProvider";

export type EmailDetailsType = {
  className?: string;
  onClose?: () => void;
  data: BulkSendComputedInterface;
};

const EmailDetails: FunctionComponent<EmailDetailsType> = ({
  className = "",
  onClose,
  data
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { adminStatsService } = useServiceContext();

  const exportAsImage = async () => {
    if (!containerRef.current) return;

    try {
      const canvas = await html2canvas(containerRef.current, {
        allowTaint: true,
        useCORS: true,
      });
      const image = canvas.toDataURL("image/png");

      // Create a download link
      const downloadLink = document.createElement("a");
      downloadLink.href = image;
      downloadLink.download = `email-details-${data.id || new Date().getTime()}.png`;
      downloadLink.click();

      adminStatsService?.trackEvent('EmailDetails', 'export_as_image');
    } catch (error) {
      console.error("Error exporting image:", error);
    }
  };

  return (
    <div className={[styles.emailDetailsContainer, className].join(" ")} ref={containerRef}>
      <section className={styles.contentWrapper}>
        <div className={styles.headerContainer}>
          <div className={styles.titleWrapper}>
            <a className={styles.linkText}>Email Details</a>
          </div>
          <img
            className={styles.closeIcon}
            loading="lazy"
            alt="Close"
            src="/analytics/icons/close-icon.png"
            onClick={() => {
              if (onClose) {
                onClose();
                adminStatsService?.trackEvent('EmailDetails', 'close_modal');
              }
            }}
          />
        </div>

        {data.videoUrl && <VideoStatEmailComponent data={data} exportAnswer={exportAsImage} />}

        <EmailDetailsStats data={data} />
      </section>
    </div>
  );
};

export default EmailDetails;
