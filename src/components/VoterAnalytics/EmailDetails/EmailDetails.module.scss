.linkText {
  text-decoration: none;
  position: relative;
  font-weight: 500;
  color: inherit;
  display: inline-block;
  min-width: 128px;
}

.titleWrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xs-5) 0px 0px;
}

.closeIcon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
  cursor: pointer;
}

.headerContainer {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-xl);
  margin-bottom: var(--gap-xl);
}

.contentWrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-7xl);
  width: 100%;
  padding: 20px;
  text-align: left;
  font-size: var(--font-size-xl);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

.emailDetailsContainer {
  width: 756px;
  position: relative;
  border-radius: var(--br-3xs);
  background-color: var(--color-whitesmoke);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  padding: 10px;
  line-height: normal;
  letter-spacing: normal;
  max-width: 100%;
  max-height: 100%;
  overflow-y: auto;
}

@media screen and (max-width: 768px) {
  .emailDetailsContainer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    border-radius: 10px 10px 0 0;
    z-index: 100;
    max-height: 80vh;
    padding: 20px;
  }

  .contentWrapper {
    width: 100%;
  }

  .closeIcon {
    height: 30px;
    width: 30px;
  }
}

@media screen and (max-width: 640px) {
  .linkText {
    font-size: 20px;
  }

  .headerContainer {
    flex-wrap: wrap;
    margin: 0;
  }

  .emailDetailsContainer {
    padding: var(--padding-lg) var(--padding-md);
  }
}

@media screen and (max-width: 640px) {
  .linkText {
    font-size: 20px;
  }

  .headerContainer {
    flex-wrap: wrap;
  }
}