.export,
.icons {
  position: relative;
}

.icons {
  height: 24px;
  width: 24px;
  overflow: hidden;
  flex-shrink: 0;
}

.export {
  text-decoration: none;
  font-size: var(--font-size-lg);
  font-weight: 500;
  //font-family: var(--font-plus-jakarta-sans);
  color: var(--color-white);
  text-align: center;
}

.buttons,
.iconsParent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.iconsParent {
  align-self: stretch;
  height: 24px;
  flex-direction: row;
  gap: var(--gap-3xs);
}

.buttons {
  cursor: pointer;
  border: 0;
  padding: var(--padding-smi) var(--padding-xl);
  background-color: var(--color-crimson-100);
  border-radius: var(--br-7xs);
  overflow: hidden;
  gap: 10px;
}

.buttons:hover {
  opacity: 80%;
  transition-duration: 150ms;
}

@media (max-width: 640px) {
  .buttons {
    padding: 9px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .hideOnMobile {
    display: none;
  }
}