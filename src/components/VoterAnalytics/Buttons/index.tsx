import { FunctionComponent, useState } from "react";
import { CSVLink } from "react-csv";
import classes from "./Buttons.module.scss";

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "shared/ui/dropdown-menu"
import { DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";
import { cn } from "utils/utils";
import { useServiceContext } from "services/ServiceProvider";

type Checked = DropdownMenuCheckboxItemProps["checked"]

export type AnalyticsButtonType = {
  className?: string;

  /** Variant props */
  type?: string;
  csvData?: any;
  csvFilename?: string;
  title?: string;
  onClickExcel?: () => void;
  onClick?: () => void;
  onClickPDF?: (exportEngagements: boolean, exportVideos: boolean, exportEmails: boolean) => void;
};

const AnalyticsButton: FunctionComponent<AnalyticsButtonType> = ({
  className = "",
  type = "Button Default",
  title = "Export",
  csvData,
  csvFilename,
  onClickExcel,
  onClick,
  onClickPDF,
}) => {
  const { adminStatsService } = useServiceContext();
  const [exportEngagements, setExportEngagements] = useState<Checked>(true);
  const [exportVideos, setExportVideos] = useState<Checked>(true);
  const [exportEmails, setExportEmails] = useState<Checked>(true);

  const isOneChecked = exportEngagements || exportVideos || exportEmails;

  return (
    onClick ?
      <button
        className={[classes.buttons, className].join(" ")}
        data-property-type={type}
        onClick={() => {
          onClick();
          adminStatsService?.trackEvent('VoterAnalytics', 'export_button_click');
        }}
      >
        <div className={classes.iconsParent}>
          <img className={classes.icons} alt="" src="/analytics/icons/export.svg" />
          <a className={classes.export}>{title}</a>
        </div>
      </button> :
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className={[classes.buttons, className].join(" ")}
            data-property-type={type}
            onClick={() => {
              adminStatsService?.trackEvent('VoterAnalytics', 'open_export_dropdown');
            }}
          >
            <div className={classes.iconsParent}>
              <img className={classes.icons} alt="" src="/analytics/icons/export.svg" />
              <a className={cn(classes.export, classes.hideOnMobile)}>Export</a>
            </div>
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-56">
          {/* <DropdownMenuItem>
            <CSVLink className="w-full h-full" data={csvData} filename={csvFilename}>
              <div>Export as CSV</div>
            </CSVLink>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem>
            <div className="w-full h-full cursor-pointer" onClick={onClickExcel}>Export as Excel</div>
          </DropdownMenuItem>

          <DropdownMenuSeparator /> */}

          <DropdownMenuSub>
            <DropdownMenuSubTrigger>Export as PDF</DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuCheckboxItem
                  className="w-full h-full cursor-pointer"
                  onSelect={(e) => e.preventDefault()}
                  checked={exportEngagements}
                  onCheckedChange={() => setExportEngagements(!exportEngagements)}>
                  Engagements
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  className="w-full h-full cursor-pointer"
                  onSelect={(e) => e.preventDefault()}
                  checked={exportVideos}
                  onCheckedChange={() => setExportVideos(!exportVideos)}>
                  Videos
                </DropdownMenuCheckboxItem>
                {/* <DropdownMenuCheckboxItem
                  className="w-full h-full cursor-pointer"
                  onSelect={(e) => e.preventDefault()}
                  checked={exportEmails}
                  onCheckedChange={() => setExportEmails(!exportEmails)}>
                  Emails
                </DropdownMenuCheckboxItem> */}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className={cn("w-full h-full", isOneChecked && "cursor-pointer")}
                  onClick={() => {
                    if (onClickPDF && isOneChecked) {
                      onClickPDF(!!exportEngagements, !!exportVideos, !!exportEmails);
                      adminStatsService?.trackEvent('VoterAnalytics', 'download_pdf_from_dropdown');
                    }
                  }}
                >
                  Download PDF
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
        </DropdownMenuContent>
      </DropdownMenu>
  );
};

export default AnalyticsButton;