import classes from "./FilterBar.module.scss";
import { useFilterStore } from "hooks/zustand/filterStore";
import CloseIcon from "assets/icons/close.svg";

type FilterItemProps = {
  name: string;
  onClick: () => void;
}

const FilterItem = ({ name, onClick }: FilterItemProps) => {
  return (
    <div className={classes.filterItem} onClick={onClick}>
      <div>{name}</div>
      <img className={classes.closeIcon} alt="Close" src={CloseIcon}/>
    </div>
  )
}

const FilterBar = () => {
  const filterState = useFilterStore((state) => state);
  const { locationFilters, timeFilter, removeLocationFilter, setLocationFilters, setTimeFilter } = filterState;

  const clearAll = () => {
    setLocationFilters([]);
    setTimeFilter(null);
  }

  return (
    locationFilters.length > 0 || timeFilter ? <div className={classes.filterBar}>
      {locationFilters.map((item) => (
        <FilterItem name={item.name} onClick={() => removeLocationFilter(item)}/>
      ))}
      {timeFilter && <FilterItem name={timeFilter.name} onClick={() => setTimeFilter(null)}/>}
      <div className={classes.clearAll} onClick={() => clearAll()}>Clear all</div>
    </div> : null
  );
};

export default FilterBar;
