.filterBar {
  display: flex;
  background-color: white;
  width: 100%;
  padding: 10px 12px;
  border-radius: 10px;
  gap: 10px;
  flex-wrap: wrap;
  font-size: 14px;
}

.filterItem {
  display: flex;
  border: 1px solid black;
  border-radius: 4px;
  padding: 10px;
  gap: 10px;
  cursor: pointer;
}

.filterItem:hover {
  opacity: 70%;
  border: 1px solid gray;
  transition: all 100ms;
}

.clearAll {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.clearAll:hover {
  opacity: 70%;
  transition: all 100ms;
}