import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import styles from './ExportElementBtn.module.scss';
import { useServiceContext } from 'services/ServiceProvider';

function ExportButton(props: { mainContent: any }) {
  const [isExporting, setIsExporting] = useState(false);
  const { adminStatsService } = useServiceContext();

  const handleExport = async () => {
    setIsExporting(true);
    try {
      // const mainContent = document.getElementById('#analytics-chart');
      if (!props.mainContent.current) return;

      const canvas = await html2canvas(props.mainContent.current as HTMLElement, {
        scale: 2,
        useCORS: true,
        logging: false,
      });

      const imgData = canvas.toDataURL('image/jpeg', 1.0);
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'px',
        format: [canvas.width, canvas.height]
      });

      pdf.addImage(imgData, 'JPEG', 0, 0, canvas.width, canvas.height);
      pdf.save('dashboard-export.pdf');

      // Track chart export
      adminStatsService?.trackEvent('VoterAnalytics', 'export_chart');
    } catch (error) {
      console.error('Error exporting PDF:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <button
      onClick={handleExport}
      disabled={isExporting}
      className={styles.buttons}
    >
      {isExporting ? (
        <Loader2 className={`${styles.icon} ${styles.spinner}`} />
      ) : (
        <img className={styles.icons} alt="" src="/analytics/icons/export.svg" />
      )}
      <span className={styles.export}>
        {isExporting ? 'Exporting...' : 'Export'}
      </span>
    </button>
  );
}

export default ExportButton