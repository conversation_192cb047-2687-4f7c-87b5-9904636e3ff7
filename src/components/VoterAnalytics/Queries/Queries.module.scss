.queries {
  width: 77px;
  height: 25px;
  position: relative;
  font-weight: 500;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.queriesTitleContainer {
  height: 32.5px;
  width: 76px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-5xs-5) 0 0;
  box-sizing: border-box;
}

.queriesTitleContainerParent {
  width: 1048px;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-913xl);
}

.totalVolumePlaceholder {
  width: 30px;
  height: 30px;
  position: relative;
}

.totalVolumeContent {
  height: 49px;
  width: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-lgi) 0 0;
  box-sizing: border-box;
}

.totalVolume,
.totalVolumeValue {
  height: 68px;
  min-width: 100px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.totalVolume {
  width: 146px;
  height: 30px;
}

.totalVolumeContentParent,
.totalVolumeWrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.totalVolumeWrapper {
  height: 49px;
  width: 145px;
  flex-direction: column;
  padding: var(--padding-lgi) 0 0;
  box-sizing: border-box;
  font-size: var(--font-size-5xl);
  color: var(--color-slategray);
}

.totalVolumeContentParent {
  height: 68px;
  flex-direction: row;
  gap: var(--gap-3xs);
  text-align: center;
  font-size: var(--font-size-35xl);
  color: var(--color-black);
}

.sentiment,
.topCategories {
  height: 15px;
  width: 152px;
  position: relative;
  font-weight: 600;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.sentiment {
  width: 62px;
}

.sortIcon {
  width: 6px;
  height: 3px;
  position: relative;
}

.metricIcons {
  height: 9px;
  width: 6px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-7xs) 0 0;
  box-sizing: border-box;
}

.metricTypes,
.volume {
  height: 15px;
  display: flex;
}

.metricTypes {
  cursor: pointer;
  width: 82px;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-3xs);
}

.volume {
  width: 45px;
  position: relative;
  font-weight: 600;
  align-items: center;
  flex-shrink: 0;
}

.sortIconContainer {
  height: 9px;
  width: 6px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-7xs) 0 0;
  box-sizing: border-box;
}

.metricTypes1 {
  cursor: pointer;
  height: 15px;
  width: 61px;
  flex-direction: row;
  gap: var(--gap-3xs);
}

.categoriesContent,
.categoriesHeader,
.categoryList,
.metricTypes1,
.metrics {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.metrics {
  width: 100%;
  height: 15px;
  flex-direction: row;
  padding: 0 30px 0 var(--padding-xl);
  box-sizing: border-box;
  gap: var(--gap-31xl);
}

.categoriesContent,
.categoriesHeader,
.categoryList {
  flex-direction: column;
}

.categoryList {
  width: 439px;
  height: fit-content;
  gap: var(--gap-7xs);
  text-align: right;
  font-size: var(--font-size-xl);
  color: var(--color-black);
}

.categoriesContent,
.categoriesHeader {
  width: 452px;
}

.categoriesHeader {
  // height: 395px;
  gap: var(--gap-xl);
}

.categoriesContent {
  min-height: 421px;
  overflow-y: auto;
  padding: var(--padding-7xl) 0 0;
  box-sizing: border-box;
  z-index: 2;
}

.topQuestionsAboutContainer {
  width: 100%;
}

.topQuestionsAboutContainer1 {
  width: 100%;
  height: 25px;
  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.question {
  height: 354px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-smi);
}

.questionList {
  height: 100%;
  display: flex;
  width: 100%;
  flex: 1;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-base);
  font-size: var(--font-size-base);
}

.questions {
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow-y: scroll;
  overflow-x: hidden;
  flex-shrink: 0;
}

.questions {
  height: 100%;
  min-height: 505px;
  flex: 1;
  border-radius: 0 var(--br-3xs) var(--br-3xs) 0;
  background-color: var(--color-whitesmoke);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-10xl) var(--padding-7xs) var(--padding-mid) var(--padding-7xl);
  gap: var(--gap-3xl);
  z-index: 1;
  font-size: var(--font-size-xl);
}

.categoriesContentParent {
  max-width: 100%;
  height: -3px;
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  flex-direction: row;
  padding: 0 0 0 var(--padding-7xl);
  gap: var(--gap-lg);
  font-size: var(--font-size-xs);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  flex-shrink: 0;
  flex: 1;
}

.categoriesContentParent,
.frameParent,
.queries1 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.frameParent {
  //height: 575px;
  max-width: 100%;
  flex-direction: column;
  gap: var(--gap-3xs);
}

.queries1 {
  max-width: 100%;
  width: 1100px;
  //height: 627px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
  flex-direction: row;
  padding: var(--padding-7xl);
  text-align: left;
  font-size: var(--font-size-xl);
  color: var(--color-slategray);
  font-family: var(--font-plus-jakarta-sans);
}

.noQuestions {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100px;
}

.noQuestionsText {
  font-size: var(--font-size-base);
  color: var(--color-slategray);
}

.trend {
  flex: 1;
  background-color: var(--color-white);
  border-left: 1px solid var(--color-gainsboro-100);
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-7xl) var(--padding-6xl) var(--padding-20xl);
  gap: var(--gap-5xl);
  height: 100%;
}

.queriesSection {
  position: relative;
  font-weight: 500;
}

.trendItem {
  text-align: right;
  font-size: var(--font-size-xl);
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  align-self: stretch;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  text-align: center;
  font-size: var(--font-size-35xl);
  color: var(--color-black);
}

.frameParentQueries {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: var(--gap-91xl);
  text-align: right;
  color: var(--color-black);
}

.frameGroup {
  flex: 1;
  gap: var(--gap-5xl);
  min-width: 169px;
}

.showOnMobile {
  display: none !important;
}

@media (max-width: 640px) {
  .hideOnMobile {
    display: none !important;
  }

  .showOnMobile {
    display: flex !important;
  }

  .metrics {
    display: none;
  }

  .queries1 {
    height: auto;
    padding: 20px;
  }

  .categoriesContentParent {
    padding: 10px;
    flex-direction: column;
    height: auto;
  }

  .categoriesContent {
    width: auto;
    min-height: auto;
  }

  .frameParent {
    height: auto;
  }

  .categoryList {
    width: auto;
  }

  .topCategories {
    width: auto;
  }

  .categoriesHeader {
    width: auto;
  }

  .questions {
    width: 100%;
    padding: 10px;
  }
}