.questionCardComponentsChild {
  align-self: stretch;
  height: 180px;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  flex-shrink: 0;
  object-fit: cover;
}
.title {
  position: relative;
  font-weight: 500;
}
.views {
  margin: 0;
  white-space: pre-wrap;
}
.housing {
  margin: 0;
}
.userViewContainer {
  align-self: stretch;
  position: relative;
  font-size: var(--font-size-sm);
}
.questionCardComponents {
  flex: 1;
  display: flex;
  cursor: pointer;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-3xs);
  min-width: 230px;
  max-width: 250px;
  text-align: left;
  font-size: var(--font-size-base);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}
