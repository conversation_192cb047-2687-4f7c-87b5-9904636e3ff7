import { FunctionComponent, useMemo, type CSSProperties } from "react";
import styles from "./QuestionCardComponents.module.scss";

export type QuestionCardComponentsType = {
  className?: string;
  group60?: string;
  housing?: string;
  onClick?: () => void;

  /** Style props */
  userViewContainer?: CSSProperties["alignSelf"];
};

const QuestionCardComponents: FunctionComponent<QuestionCardComponentsType> = ({
  className = "",
  group60,
  userViewContainer,
  housing,
  onClick
}) => {
  const userViewContainerStyle: CSSProperties = useMemo(() => {
    return {
      alignSelf: userViewContainer,
    };
  }, [userViewContainer]);

  return (
    <div className={[styles.questionCardComponents, className].join(" ")} onClick={onClick}>
      <img
        className={styles.questionCardComponentsChild}
        loading="lazy"
        alt=""
        src={group60}
      />
      <div className={styles.title}>
        This is the question that the video is trying to answer?
      </div>
      <div className={styles.userViewContainer} style={userViewContainerStyle}>
        <p className={styles.views}>250 Views • 10 Jan</p>
        <p className={styles.housing}>{housing}</p>
      </div>
    </div>
  );
};

export default QuestionCardComponents;
