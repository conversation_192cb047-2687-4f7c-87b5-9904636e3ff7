import { FunctionComponent, useRef } from "react";
import styles from "./VideoDetails.module.scss";
import VideoStatEmailComponent from "components/VoterAnalytics/VideoStatEmailComponent";
import VideoStatPreviewComponent from "components/VoterAnalytics/VideoStatPreviewComponent";
import { BulkSendComputedInterface, VideoStatInterface } from "interfaces";
import html2canvas from "html2canvas";
import { useServiceContext } from "services/ServiceProvider";

export type VideoDetailsType = {
  className?: string;
  onClose?: () => void;
  video: VideoStatInterface;
};

const VideoDetails: FunctionComponent<VideoDetailsType> = ({
  className = "",
  onClose,
  video,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { adminStatsService } = useServiceContext();

  const exportAsImage = async () => {
    if (!containerRef.current) return;

    try {
      const canvas = await html2canvas(containerRef.current, {
        allowTaint: true,
        useCORS: true,
      });
      const image = canvas.toDataURL("image/png");

      // Create a download link
      const downloadLink = document.createElement("a");
      downloadLink.href = image;
      downloadLink.download = `video-details-${video.id || new Date().getTime()}.png`;
      downloadLink.click();

      adminStatsService?.trackEvent('VideoDetails', 'export_as_image');
    } catch (error) {
      console.error("Error exporting image:", error);
    }
  };

  return (
    <div className={[styles.videoDetailsContainer, className].join(" ")} ref={containerRef}>
      <section className={styles.contentWrapper}>
        <div className={styles.headerContainer}>
          <div className={styles.titleWrapper}>
            <a className={styles.linkText}>Video Details</a>
          </div>

          <img
            className={styles.closeIcon}
            loading="lazy"
            alt="Close"
            src="/analytics/icons/close-icon.png"
            onClick={() => {
              if (onClose) {
                onClose();
                adminStatsService?.trackEvent('VideoDetails', 'close_modal');
              }
            }}
          />
        </div>

        <VideoStatEmailComponent data={video} exportAnswer={exportAsImage} />

        <VideoStatPreviewComponent data={video} />
      </section>
    </div>
  );
};

export default VideoDetails;
