import { <PERSON>, <PERSON><PERSON>hart, CartesianG<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import styles from "./Chart.module.scss"

import {
  Card,
  CardContent,
} from "shared/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from "shared/ui/chart"
import moment from "moment"
import { numberWithCommas } from "utils/utils"
import { useMemo } from "react"

function ChartTooltipContent({ payload, type }: { type: string, payload?: any }) {

  const data = payload[0]?.payload;

  let openedPercentage = 0;
  if (type === "emails") {
    openedPercentage = data?.secondary ? Math.round(data?.data / data?.secondary * 100) : 0;
    if (data?.data > data?.secondary) {
      openedPercentage = 100;
    }
  }

  return (
    <div className={styles.sentimentChartHeader}>
      <b className={styles.jan2025}>{moment(data?.date).format("ll")}</b>
      {type !== "emails" &&
        <>
          <b className={styles.sentiment}>{
            type === "queries" ? "Queries" :
              type === "videos" ? "Views" : "Visits"
          }</b>
          <div className={styles.sentimentChartIcons}>
            <div className={styles.sentimentChartLegend}>
              <img
                className={styles.fi6637188Icon}
                loading="lazy"
                alt=""
                src={type === "engagements" ? "/analytics/cursor.svg" : "/analytics/view-icon.svg"}
              />
              <div className={styles.sentimentChartPointIcons}>
                <b className={styles.sentimentChartPoint}>{numberWithCommas(Number(data?.data))}</b>
              </div>
            </div>
          </div>
        </>}
      {
        type === "emails" && <>
          <b className={styles.sentiment}>Delivered</b>
          <div className={styles.sentimentChartIcons}>
            <div className={styles.sentimentChartLegend}>
              <img
                className={styles.fi6637188Icon}
                loading="lazy"
                alt=""
                src="/analytics/svg-icons/mail.svg"
              />
              <div className={styles.sentimentChartPointIcons}>
                <b className={styles.sentimentChartPoint}>{data?.secondary}</b>
              </div>
            </div>
          </div>
          <b className={styles.sentiment}>Opened</b>
          <div className={styles.sentimentChartIcons}>
            <div className={styles.sentimentChartLegend}>
              <img
                className={styles.fi6637188Icon}
                loading="lazy"
                alt=""
                src="/analytics/opened-email.svg"
              />
              <div className={styles.sentimentChartPointIcons}>
                <b className={styles.sentimentChartPoint}>{`${data?.data} (${openedPercentage}%)`}</b>
              </div>
            </div>
          </div>
        </>
      }
      {
        type === "engagements" || type === "queries" && <>
          <b className={styles.sentiment}>Sentiment</b>
          <div className={styles.sentimentChartIcons}>
            <div className={styles.sentimentChartLegend}>
              <img
                className={styles.fi6637188Icon}
                loading="lazy"
                alt=""
                src="/analytics/faces/happy-face.svg"
              />
              <div className={styles.sentimentChartPointIcons}>
                <b className={styles.sentimentChartPoint}>{data?.happyPercentage}%</b>
              </div>
            </div>
            <div className={styles.sentimentChartLegend}>
              <img
                className={styles.fi6637188Icon}
                loading="lazy"
                alt=""
                src="/analytics/faces/neutral-face.svg"
              />
              <div className={styles.sentimentChartPointIcons}>
                <b className={styles.sentimentChartPoint}>{data?.neutralPercentage}%</b>
              </div>
            </div>
          </div>
          <div className={styles.sentimentChartLegend}>
            <img
              className={styles.fi6637188Icon}
              loading="lazy"
              alt=""
              src="/analytics/faces/unhappy-face.svg"
            />
            <div className={styles.sentimentChartPointIcons}>
              <b className={styles.sentimentChartPoint}>
                {data?.unhappyPercentage}%
              </b>
            </div>
          </div>
        </>
      }
    </div>
  )
}

export function Chart({ chartData, color, type, chartRef }: { chartData: { date: string; data: number }[], color?: string; type: string; chartRef: any }) {
  const chartConfig: ChartConfig = {
    visitors: {
      label: "Visitors",
    },
    data: {
      label: "Data",
      color: color ? color : "#136ac6",
    },
  };
  
  const maxValue = useMemo(() => {
    if (!chartData || chartData.length === 0) return 10;
    
    // Get the max value from the data
    const max = Math.max(...chartData.map(item => Number(item.data || 0)));
    
    // Round up to make sure we have a nice upper bound
    // For small numbers (under 10), return the exact max value
    if (max <= 10) return max;
    
    // For larger numbers, round up to the nearest 10, 100, 1000, etc.
    const magnitude = Math.pow(10, Math.floor(Math.log10(max)));
    return Math.ceil(max / magnitude) * magnitude;
  }, [chartData]);

  return (
    <Card className="w-full shadow-none" id="analytics-chart" ref={chartRef}>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart data={chartData.length === 1 ? [...chartData, { ...chartData[0], date: new Date(chartData[0].date).setDate(new Date(chartData[0].date).getDate() + 1) }] : chartData}>
            <defs>
              <linearGradient id="fillDesktop" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-data)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-data)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillMobile" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-mobile)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-mobile)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value)
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                })
              }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              width={35}
              tick={{ fontSize: 10 }}
              tickCount={5}
              domain={[0, maxValue]}
              allowDecimals={false}
              tickFormatter={(value) => {
                if (value === 0) return '';
                return value > 1000 ? `${(value / 1000).toFixed(0)}k` : value;
              }}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent type={type} />}
            />
            <Area
              dataKey="data"
              type="monotone"
              fill="url(#fillDesktop)"
              stroke="var(--color-data)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
