.sentimentChartIcons {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-7xs);
  text-align: right;
}
.sentimentChartLegend1 {
  width: 37px;
  position: relative;
  display: flex;
  align-items: center;
}
.sentimentChartLegend {
  flex-direction: row;
  gap: var(--gap-3xs);
  text-align: right;
  align-items: center;
  justify-content: center;
}
.sentimentChartElements,
.sentimentChartHeader,
.sentimentChartLegend {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.sentimentChartHeader {
  align-self: stretch;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.07);
  border-radius: var(--br-3xs);
  background-color: var(--color-white);
  border: 1px solid var(--color-gainsboro-100);
  flex-direction: column;
  padding: var(--padding-5xs) 7px var(--padding-5xs) 10px;
  gap: var(--gap-7xs);
  z-index: 4;
}
.sentimentChartElements {
  height: 154px;
  flex-direction: row;
  padding: 0 180px;
  box-sizing: border-box;
  max-width: 100%;
}
.sentimentChartDataPoints {
  align-self: stretch;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.sentimentChartDataPoints8 {
  width: 14px;
  height: 14px;
  position: relative;
  border-radius: 50%;
  background-color: var(--color-white);
  border: 0 solid var(--color-dodgerblue);
  box-sizing: border-box;
  transform: rotate(180deg);
  flex-shrink: 0;
  z-index: 3;
}
.sentimentChartPointIcons {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.sentimentChartPoint {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-top: 5px;
}
.sentimentChartGraphElementsChild {
  width: 14px;
  height: 196px;
  position: relative;
  z-index: 3;
}
.sentimentChartGraphElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 791px;
  height: 196px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-9xl);
  max-width: 100%;
}
.frameItem,
.sentimentChartCursorCircle1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.sentimentChartCursorCircle1 {
  border-radius: var(--br-3xs);
  z-index: 2;
}