import { FunctionComponent } from "react";
import styles from "./TrendingCategory.module.scss";

export type TrendingCategoryType = {
  className?: string;
  name: string;
  sentiment: number;
};

const TrendingCategory: FunctionComponent<TrendingCategoryType> = ({
  className = "",
  name,
  sentiment
}) => {
  return (
    <div
      className={[styles.value, className].join(" ")}
    >
      <b className={styles.label}>
        {name}
      </b>
      <div className={"flex gap-2"}>
        <img
          className={styles.facesIcon}
          alt=""
          src={`/analytics/faces/${sentiment > 66 ? "happy" : sentiment > 33 ? "neutral" : "unhappy"}-face.svg`}
        />
        <b className={styles.sentiment}>{sentiment + "%"}</b>
      </div>
    </div>
  );
};

export default TrendingCategory;
