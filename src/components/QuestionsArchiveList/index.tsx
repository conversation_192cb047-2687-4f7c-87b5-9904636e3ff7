import { useState } from "react";

import * as interfaces from "interfaces";

import QuestionsItem from "../QuestionsItem";
import CreateAnswer from "popups/CreateAnswer";

import {
  QuestionService,
  AnswersService,
  FileService,
  UserService,
  ClientService
} from "services";
import { useServiceContext } from "services/ServiceProvider";

import pageClasses from "styles/PageWrapper.module.scss";
import classes from "./QuestionsList.module.scss";
import Button from "shared/Button";
import { cn } from "utils/utils";

const questionTabs = {
  approved: "Approved Questions",
  review: "Review",
  archive: "Archive"
};

export default function QuestionsArchiveList(props: {
  service?: QuestionService;
  answersService?: AnswersService;
  fileService?: FileService;
  userService?: UserService;
  clientService?: ClientService,
  client: interfaces.ClientInterface;
  user?: interfaces.UserInterface;
}) {
  const { service, fileService, answersService, userService, clientService, client, user } =
    props;
  const { adminStatsService } = useServiceContext();
  const [isCreateQuestionModalOpen, setIsCreateQuestionModalOpen] =
    useState(false);
  const [questions, setQuestions]: interfaces.QuestionInterface[] | any =
    useState([]);
  const activeTab = questionTabs.archive;

  const fetchQuestionList = () => {
    service?.getQuestions(setQuestions);
  };

  if (service && questions.length === 0) fetchQuestionList();

  const QuestionsTemplate = () => {
    const questionsSortComparator = (
      a: interfaces.QuestionInterface,
      b: interfaces.QuestionInterface
    ) => {
      if (activeTab === questionTabs.approved) return b.votes - a.votes;
      return Number(a.blockedForClient) - Number(b.blockedForClient);
    };

    const filterCriteria = (question: interfaces.QuestionInterface): boolean => {
      let canShowQuestion = false;

      const isArchived = question.isDenied || question.blockedForClient || (!question.isAnswered && !question.enabled);

      switch (activeTab) {
        case questionTabs.approved:
          canShowQuestion = question.isApproved && !question.isDenied && !question.blockedForClient && question.enabled && !question.isAnswered;
          break;
        case questionTabs.review:
          canShowQuestion = !question.isApproved && !isArchived;
          break;
        case questionTabs.archive:
          canShowQuestion = isArchived;
          break;
      }

      return canShowQuestion;
    }

    const filteredQuestions = questions
      .filter(filterCriteria)
      .sort(questionsSortComparator);

    if (filteredQuestions.length === 0) {
      return <div className={pageClasses.NoQuestions}>🎉 All done, no questions to show! Please check back in later.</div>;
    } else {
      return (
        <div className={pageClasses.limitHeight}>
          {filteredQuestions
            .map((question: interfaces.QuestionInterface) => (
              <QuestionsItem
                key={question.id}
                question={question}
                fileService={fileService}
                answersService={answersService}
                userService={userService}
                clientService={clientService}
                client={client}
                service={service}
                refetchQuestionList={fetchQuestionList}
              />
            ))}
        </div>
      );
    }
  };

  return (
    <div className={classes.QuestionsList}>
      <div className={pageClasses.TitleWithTabs}>
        <div className={pageClasses.Tabs}>
          <div
            className={cn(
              pageClasses.Tab, pageClasses.active
            )}
          >
            Archive
          </div>
        </div>
        <div className={pageClasses.Buttons}>
          <Button
            iconText="plus"
            text="Create Question"
            callback={() => {
              setIsCreateQuestionModalOpen(true);
              adminStatsService?.trackEvent('QuestionsArchive', 'create_question');
            }}
          />
        </div>
      </div>

      {questions.length > 0 && <QuestionsTemplate />}

      <CreateAnswer
        isOpen={isCreateQuestionModalOpen}
        handleClose={() => { setIsCreateQuestionModalOpen(false) }}
        fileService={fileService}
        service={service}
        client={client}
        user={user}
        refetchQuestionList={fetchQuestionList}
      />
    </div>
  );
}
