import BusyIndicator from "../BusyIndicator";
import { AnswerVideoStatusEnum } from "./useAnswerVideoStatus";

type Props = {
  status: AnswerVideoStatusEnum;
};

export default function AnswerVideoStatus({ status }: Props) {
  let statusClass = "bg-gray-300 text-gray-900";
  if (status === AnswerVideoStatusEnum.processing) {
    statusClass = "bg-yellow-300 text-yellow-900";
  }
  if (status === AnswerVideoStatusEnum.unpublished) {
    statusClass = "bg-blue-300 text-blue-900";
  }
  if (status === AnswerVideoStatusEnum.published) {
    statusClass = "bg-green-300 text-green-900";
  }

  return (
    <div
      className={`inline-flex gap-3 py-1 px-2 mt-2 rounded-lg text-center ${statusClass}`}
    >
      <h1 className="text-md font-semibold capitalize">{status}</h1>
      {status === AnswerVideoStatusEnum.processing && (
        <div className="flex gap-3">
          <BusyIndicator message={""} />
        </div>
      )}
    </div>
  );
}
