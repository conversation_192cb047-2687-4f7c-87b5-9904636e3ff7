import React, { useCallback, useEffect, useState } from "react";
import { AnswerInterface } from "../../interfaces";
import { toast } from "react-toastify";
import { AnswersService } from "../../services";
import emptyThumbnailUrl from "../VideoHelpers/emptyThumbnailUrl";

export enum AnswerVideoStatusEnum {
  processing = "Processing",
  unpublished = "Unpublished",
  published = "Published",
}

function isAfterMay2024(answer: AnswerInterface) {
  const inputDate = new Date(answer.createdAt);
  const comparisonDate = new Date("2024-05-20T09:00:00Z");

  return inputDate > comparisonDate;
}
export default function useAnswerVideoStatus(
  answer: AnswerInterface,
  answersService: AnswersService
) {
  const getStatusFromAnswer = useCallback(() => {
    const isProcessing =
      isAfterMay2024(answer) && answer.mp4VideoStatus === "";
    const hasSelectedThumbnail = answer.imageUrl !== emptyThumbnailUrl;
    return isProcessing
      ? AnswerVideoStatusEnum.processing
      : hasSelectedThumbnail
        ? AnswerVideoStatusEnum.published
        : AnswerVideoStatusEnum.unpublished;
  }, [answer]);

  const [answerVideoStatus, setAnswerVideoStatus] = useState(
    getStatusFromAnswer()
  );

  useEffect(() => {
    setAnswerVideoStatus(getStatusFromAnswer());
  }, [getStatusFromAnswer]);

  useEffect(() => {
    const interval = setInterval(() => {
      checkStatus().then();
    }, 3000);

    async function checkStatus() {
      if (answerVideoStatus !== AnswerVideoStatusEnum.processing) {
        clearInterval(interval);
        return;
      }
      await answersService.getAnswer(answer.id, (data: AnswerInterface) => {
        if (data.mp4VideoStatus !== "") {
          if (answer.imageUrl === emptyThumbnailUrl) {
            setAnswerVideoStatus(AnswerVideoStatusEnum.unpublished);
          } else {
            setAnswerVideoStatus(AnswerVideoStatusEnum.published);
          }
          toast(
            <div className="text-green-600 px-4 py-3" role="alert">
              <div className="flex">
                <div>
                  <p className="font-bold text-2xl mb-4">Uploading Complete!</p>
                  <p className="text-lg">
                    Your video has been uploaded and posted successfully!
                  </p>
                </div>
              </div>
            </div>,
            {
              autoClose: 60000,
            }
          );
          clearInterval(interval);
          return;
        }
      });
    }

    return () => {
      clearInterval(interval);
    };
  }, [answerVideoStatus, answer, answersService]);

  return answerVideoStatus;
}
