import { FunctionComponent, useMemo, type CSSProperties } from "react";
import styles from "./StatusOptions.module.scss";

export type StatusOptionsType = {
  className?: string;
  fi10200022?: string;
  opened?: string;
  prop?: string;
};

const StatusOptions: FunctionComponent<StatusOptionsType> = ({
  className = "",
  fi10200022,
  opened,
  prop,
}) => {
  return (
    <div className={[styles.statusOptions, className].join(" ")}>
      <img
        className={styles.fi10200022Icon}
        loading="lazy"
        alt=""
        src={fi10200022}
      />
      <div className={styles.openedParent}>
        <h3 className={styles.opened}>
          {opened}
        </h3>
        <b className={styles.b}>
          <span>{prop}</span>
          <span className={styles.span}>%</span>
        </b>
      </div>
    </div>
  );
};

export default StatusOptions;
