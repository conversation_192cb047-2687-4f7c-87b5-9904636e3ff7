.fi10200022Icon {
  height: 48px;
  width: 48px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.opened {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
  display: flex;
  align-items: center;
}

.span {
  font-size: var(--font-size-15xl);
}

.b {
  position: relative;
  color: var(--color-black);
  font-size: var(--font-size-35xl);
}

.openedParent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.statusOptions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--gap-xl);
  text-align: center;
  font-size: var(--font-size-xl);
  color: var(--color-slategray-100);
  font-family: var(--font-plus-jakarta-sans);
}

@media screen and (max-width: 975px) {
  .b {
    font-size: var(--font-size-8xl);
  }
}

@media screen and (max-width: 450px) {
  .opened {
    font-size: var(--font-size-base);
  }

  .b {
    font-size: var(--font-size-xl);
  }
}