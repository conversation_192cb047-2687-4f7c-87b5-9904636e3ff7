import React from "react";
import Button from "shared/Button"
import classes from "./Notification.module.scss"

export default function Notification(props: { message: string, handleClose: () => void }) {
  const { message, handleClose } = props

  return (
    <div className={classes.alertWrapper}>
      <div className={classes.alert}>
        {message}
        <Button text="Close" callback={handleClose} />
      </div>
    </div>
  )
}