@import 'styles/variables';

.alertWrapper {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1002;
  width: 100%;
  background-color: $dark-blue;

  &.warn {
    position: relative;
    z-index: auto;
    background-color: $dark-yellow
  }
}

.alertWarning,
.alert {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: auto;
  padding: 20px;
  max-width: 800px;
  color: $pale-blue;
  transition: opacity .5s;

  .buttons {
    min-width: 240px;

    button {
      margin-left: 10px;
    }
  }

  button {
    padding: 5px 15px;
    color: $pale-blue;
    border: 1px solid $pale-blue;
    background: none;
  }
}