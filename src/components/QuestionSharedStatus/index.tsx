export enum QuestionSharedStatusEnum {
    unopened = "unopened",
    opened = "opened",
    clicked = "clicked",
    completed = "completed",
}

type Props = {
    status: QuestionSharedStatusEnum;
};

export default function QuestionSharedStatus({ status }: Props) {
    let statusClass = "bg-gray-300 text-gray-900";
    if (status === QuestionSharedStatusEnum.opened) {
        statusClass = "bg-yellow-300 text-yellow-900";
    }
    if (status === QuestionSharedStatusEnum.clicked) {
        statusClass = "bg-yellow-300 text-yellow-900";
    }
    if (status === QuestionSharedStatusEnum.completed) {
        statusClass = "bg-green-300 text-green-900";
    }

    return (
        <>
            {
                status !== QuestionSharedStatusEnum.unopened && <div
                    className={`inline-flex gap-3 py-1 px-2 mt-2 mr-3 rounded-lg text-center ${statusClass}`}
                >
                    <h1 className="text-md font-semibold capitalize">{status}</h1>
                </div>
            }
        </>
    );
}
