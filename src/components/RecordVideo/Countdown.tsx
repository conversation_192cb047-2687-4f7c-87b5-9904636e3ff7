import {useEffect, useState} from "react";
import logger from 'utils/logger';

type Props = {
    record: () => void;
};
export default function Countdown({ record }: Props) {
    const [count, setCount] = useState(4);
    const totalDuration = 4;
    const circumference = 283;

    useEffect(() => {
        if (count > 0) {
            const intervalId = setInterval(() => { 
                setCount((currentCount) => currentCount - 1); 
                
                logger.info(`Recording Countdown: ${count}`);
            }, 1000);

            return () => clearInterval(intervalId);
        }

        const intervalId = setInterval(() => { record(); }, 1000);

        logger.info('Recording started');

        return () => clearInterval(intervalId);
    }, [count]);

    const strokeDashoffset = ((count / totalDuration) * circumference).toFixed(0);

    return (
        <div style={{width:800, height: 450}} className="absolute top-0 left-0 z-10 flex items-center justify-center w-full h-full">
            <div className="flex flex-col items-center justify-center">
                <div className={`text-6xl font-bold ${count === 4 ? 'text-transparent' : 'text-white'}`}>{count}</div>

                <svg className="w-40 h-40 text-green-500" viewBox="0 0 100 100">
                    <circle cx="50" cy="50" r="45" fill="none" stroke-width="5" className="opacity-25" />
                    <circle
                        cx="50"
                        cy="50"
                        r="45"
                        fill="none"
                        stroke-width="5"
                        stroke-dasharray={circumference}
                        stroke-dashoffset={strokeDashoffset}
                        className="stroke-current transform -rotate-90 origin-center"
                        style={{ transition: 'stroke-dashoffset 1s linear' }}
                    />
                </svg>

                {count !== 4 &&
                  <div className="mt-4 text-lg font-medium text-center text-white">Recording starts in...</div>}
            </div>
        </div>
    );
}