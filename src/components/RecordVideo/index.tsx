import logger from 'utils/logger';
import { useCallback, useEffect, useRef, useState } from "react";
import { StatusMessages, useReactMediaRecorder } from "react-media-recorder";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";

import VideoSubtitlesPreview from "../VideoSubtitlesPreview";
import ProgressBar from "components/ProgressBar";
import Button from "shared/Button";

import { FileService } from "services";
import { ClientInterface } from "interfaces";
import { useNotification } from "hooks";

import classes from "./RecordVideo.module.scss";
import Countdown from "./Countdown";
import { SetState } from "../../shared/types";
import emptyThumbnailUrl from "../VideoHelpers/emptyThumbnailUrl";

interface ReactMediaRecorderProps {
  status: StatusMessages;
  startRecording: () => void;
  stopRecording: () => void;
  mediaBlobUrl: any;
  previewStream: any;
}

interface RecordVideoProps {
  handleClose: () => void;
  handleGoBack: () => void;
  handleDone?: (answer: any) => void;
  notes: string;
  setFile?: SetState<File>;
  questionId: string;
  refetchQuestionList?: () => void;
  useAsSubtitle: boolean;
  fileService?: FileService;
  client?: ClientInterface;
  setIsVideoRecorded?: SetState<boolean>;
}

var scrollingInterval: number | null | any;

const CONVERSION_ETA_MS_PER_MB: number = parseInt(
  process.env.CONVERSION_ETA_PER_MB || "1800"
);

const VideoPreview = ({
  stream,
  videoRef,
  notes,
  preRecordStatus,
  testing,
  videoSpeedValue,
}: {
  stream: MediaStream | null;
  videoRef: any;
  notes: string;
  preRecordStatus: boolean;
  testing: boolean;
  videoSpeedValue: string;
}) => {
  const scrollContainer: any = useRef<HTMLElement>(null);
  var scrollCriteria: boolean =
    !preRecordStatus || (testing && scrollContainer?.current);

  const clearScroll = () => {
    setScroll(0);
    clearInterval(scrollingInterval);
    scrollingInterval = null;
  };

  const setScroll = (amount: number) => {
    if (amount === 0 || (scrollCriteria && amount > 0))
      scrollContainer.current.scrollTop = amount;
  };

  const handleScroll = () => {
    if (scrollCriteria) setScroll(scrollContainer.current.scrollTop + 1);
    else clearScroll();
  };

  useEffect(() => {
    if (videoRef.current && stream?.active) videoRef.current.srcObject = stream;
  }, [stream]);

  useEffect(() => {
    let scrollTimer: ReturnType<typeof setTimeout>;
    let scrollingInterval: ReturnType<typeof setInterval>;

    if (scrollContainer) {
      if (scrollCriteria) {
        const speedMultiplier = parseFloat(videoSpeedValue),
          speed =
            speedMultiplier <= 0.15
              ? 30 / speedMultiplier
              : 80 / speedMultiplier;

        scrollTimer = setTimeout(() => {
          if (scrollingInterval) clearScroll();
          if (scrollCriteria)
            scrollingInterval = setInterval(() => handleScroll(), speed);
        }, 1000);
      } else clearScroll();
    } else clearScroll();
    return () => {
      clearTimeout(scrollTimer);
      clearInterval(scrollingInterval);
    };
  }, [scrollContainer, scrollCriteria, testing, videoSpeedValue]);

  return (
    <div className={classes.previewVideo}>
      <div className={notes !== "" ? classes.previewSubtitle : ""}>
        <div
          className={notes !== "" ? classes.subtitleWrapper : ""}
          ref={scrollContainer}
        >
          <span style={{ whiteSpace: "pre-line" }}>{notes}</span>
        </div>
      </div>

      <video
        className={!stream?.active ? classes.fade : ""}
        ref={videoRef}
        autoPlay
        controls
        playsInline
      />
    </div>
  );
};

export default function RecordVideo({
  handleClose,
  client,
  notes,
  setFile,
  useAsSubtitle,
  fileService,
  handleGoBack,
  handleDone,
  refetchQuestionList,
  questionId,
  setIsVideoRecorded: setIsVideoRecordedIn,
}: RecordVideoProps) {
  const { showAlert } = useNotification();
  const {
    status,
    startRecording,
    stopRecording,
    mediaBlobUrl,
    previewStream,
  }: ReactMediaRecorderProps = useReactMediaRecorder({ video: true });
  const video: any = useRef<HTMLVideoElement>(null);

  const [isPreRecord, setIsPreRecord] = useState(true);
  const [isVideoRecorded, setIsVideoRecorded] = useState(false);
  const [videoSpeedValue, setVideoSpeedValue] = useState("1.00");
  const [testing, setTesting] = useState(false);
  const [imgSrc, setImgSrc] = useState<any>();
  const [imageUrl, setImageUrl] = useState("");
  const [videoUrl, setVideoUrl] = useState("");
  const [videoUrls, setVideoUrls] = useState<any>(undefined);
  const [videoDuration, setVideoDuration] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [isDraft, setIsDraft] = useState(false);
  const [uploadingPercent, setUploadingPercent] = useState<number>(0);
  const [fileSize, setFileSize] = useState<number>(0);

  const onProgress = useCallback((updatedPercent: number) => {
    setUploadingPercent(updatedPercent);
  }, []);

  const handleDoneClick = () => {
    // setIsVideoRecorded(true);
    if (setIsVideoRecordedIn) setIsVideoRecordedIn(true);
    // handleVideoUpload();
  };

  const handleSaveDraftClick = () => {
    setIsDraft(true);
    // handleVideoUpload();
    // handleImgUpload();
    setLoading(true);
  };

  const handleImgUpload = () => {
    const formData = new FormData();

    formData.append("file", imgSrc);

    fileService
      ?.uploadImage(formData, () => {})
      .then((thumbnailUrl: any) => {
        setImageUrl(thumbnailUrl.url);
        showAlert(
          "Thumbnail Image uploaded successfully, Video upload is in progress!"
        );
      });
  };

  const handleVideoUpload = async () => {
    const videoBlob = await fetch(mediaBlobUrl).then((response) =>
      response.blob()
    );
    const videoFile = new File([videoBlob], `{"mp4"}`, { type: "video/mp4" });
    setFileSize(videoFile.size);
    if (setFile) setFile(videoFile);

    const formData = new FormData();
    formData.append("file", videoFile);

    fileService
      ?.uploadVideo(formData, () => {}, onProgress)
      .then((videoData: any) => {
        if (videoDuration < videoData.duration)
          setVideoDuration(videoData.duration);

        setVideoUrls({
          mp4: videoData.mp4 || videoData.original,
          webm: videoData.webm,
          ogv: videoData.ogv,
        });

        setVideoUrl(videoData.mp4 || videoData.original);
      });
  };

  const answerUploadSuccess = (video: any) => {
    if (handleDone) handleDone(video);
    else {
      handleClose();
      refetchQuestionList && refetchQuestionList();
      showAlert(
        isDraft ? "Draft uploaded successfully" : "Answer uploaded successfully"
      );
    }
  };

  useEffect(() => {
    if (videoUrl !== "" && !!videoUrls) {
      console.log("ABOUT TO UPLOAD THE ANSWER");
      logger.info('About to upload the answer');

      const payload = {
        clientId: client?.id,
        questionId,
        imageUrl: emptyThumbnailUrl,
        videoUrl,
        videoUrls,
        subtitles: useAsSubtitle ? notes : "",
        videoDuration,
        subtitlesSpeed: 1500,
        isDraft: isDraft,
      };

      fileService
        ?.uploadAnswer(payload, () => {
          setLoading(false);
        })
        .then((video) => video?.id && answerUploadSuccess(video));
    }
  }, [videoUrl, videoUrls]);

  useEffect(() => {
    if (status === "idle" && !isVideoRecorded) startRecording();
    logger.info(`Recording status: ${status} - isVideoRecorded: ${isVideoRecorded}`);
  }, [status, isVideoRecorded, isPreRecord]);

  const [showCountdown, setShowCountdown] = useState(false);
  const record = () => {
    setShowCountdown(false);
    setTesting(false);
    setIsPreRecord(false);
    stopRecording();
    startRecording();
  };
  const stop = () => {
    stopRecording();
    // setIsVideoRecorded(true);
    if (setIsVideoRecordedIn) setIsVideoRecordedIn(true);
    // handleVideoUpload();
  };

  const handleSubmitClick = () => {
    handleVideoUpload();
    handleImgUpload();
    setLoading(true);
  };

  const videoSpeedOptions = ["0.05", "0.15", "0.25", "0.50", "0.75", "1.00"];

  return (
    <div className={classes.recordVideo}>
      {isVideoRecorded ? (
        <div className={classes.previewVideo}>
          <p>Preview</p>
          <div className={classes.draftButtonWrapper}>
            <Button
              text="Save Draft"
              customClass={imgSrc && !loading ? "" : "disabled"}
              callback={handleSaveDraftClick}
            />
          </div>

          <VideoSubtitlesPreview
            src={mediaBlobUrl}
            setImgSrc={setImgSrc}
            loading={loading}
            setVideoDuration={setVideoDuration}
            fileService={fileService}
            mediaBlobUrl={mediaBlobUrl}
          />
          {loading && fileSize > 0 && (
            <ProgressBar
              uploadingProgress={uploadingPercent}
              calculatedConversionETA={
                (fileSize / 1000000) * CONVERSION_ETA_MS_PER_MB
              }
            />
          )}

          <div className={classes.actionButtonsWrapper}>
            <Button
              text="Cancel"
              customClass={classes.cancelButton}
              callback={handleClose}
            />
            <Button
              text={!loading ? "Submit" : "Loading"}
              customClass={imgSrc && !loading ? "" : "disabled"}
              callback={handleSubmitClick}
            />
          </div>
        </div>
      ) : (
        <div>
          <div className={classes.titleSection}>
            <button onClick={handleGoBack} className={classes.goBack}>
              <FontAwesomeIcon
                icon={faArrowLeft}
                className={classes.leftArrow}
              />
              Terminate Notes
            </button>

            <p>{isPreRecord ? "Preview" : "Recording"}</p>

            <div>
              <select
                name="videoSpeed"
                id="videoSpeed"
                value={videoSpeedValue}
                onChange={(e) => setVideoSpeedValue(e.target.value)}
              >
                {videoSpeedOptions.map((item, index) => (
                  <option value={item} key={index}>
                    {" "}
                    {item}x{" "}
                  </option>
                ))}
              </select>

              <button onClick={() => setTesting(!testing)}>
                {" "}
                {testing ? "Stop" : "Test"}{" "}
              </button>
            </div>
          </div>
          <div className="relative">
            {showCountdown && <Countdown record={record} />}
            <>
              <VideoPreview
                stream={previewStream}
                videoRef={video}
                preRecordStatus={isPreRecord}
                testing={testing}
                videoSpeedValue={videoSpeedValue}
                notes={notes}
              />
              <div>
                <div className={classes.recordingButton}>
                  {status === "recording" && !isPreRecord ? (
                    <Button text="Stop" callback={stop} />
                  ) : (
                    <Button
                      text="Start"
                      callback={() => setShowCountdown(true)}
                    />
                  )}
                </div>

                <div className={classes.actionButtonsWrapper}>
                  <Button text="Cancel" callback={handleClose} />
                  <Button
                    text="Done"
                    callback={() => {
                      handleDoneClick();
                      stopRecording();
                    }}
                  />
                </div>
              </div>
            </>
          </div>
        </div>
      )}
    </div>
  );
}
