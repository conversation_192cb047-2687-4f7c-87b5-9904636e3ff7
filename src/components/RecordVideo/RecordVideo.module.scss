@import 'styles/variables';

.recordVideo {
  width: 100%;
  font-size: 16px;
  font-weight: 400;

  .previewVideo {
    p {
      font-size: 16px;
      font-weight: 400;
      margin-bottom: 18px;
    }

    video {
      width: 800px;
      height: 450px;
      display: block;
      max-height: calc(min(100vh - 300px, 640px));
      transition: opacity 1s;
    }

    .draftButtonWrapper {
      position: absolute;
      right: 0;
      top: -10px;
    }

    .actionButtonsWrapper {
      margin-top: 48px;
    }

    .fade {
      opacity: 0;
    }
  }

  button {
    padding: 7px 35px;
    transition: none;
    color: $blue;

    span {
      transition: none;
    }

    &:hover {
      color: $blue;
      background-color: $pale-blue;
    }
  }

  video {
    background: rgba(0, 0, 0, 0.6) !important;
  }

  .titleSection {
    margin-bottom: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .goBack {
      font-size: 16px;
      padding: 0;

      .leftArrow {
        margin-right: 5px;
      }
    }

    select {
      padding: 5px 10px;
      background-color: $pale-blue;
      border-radius: 5px;
      appearance: none;

      &:focus {
        outline: $blue 2px dashed !important;
      }
    }
  }

  .recordingButton {
    margin-top: 25px;
    display: flex;
    justify-content: center;

    button {
      border-color: red;
      color: red;
    }
  }

  .actionButtonsWrapper {
    display: flex;
    justify-content: center;
    margin-top: 25px;

    button {
      &:nth-child(2) {
        margin-left: 10px;
      }
    }
  }
}

.previewVideo {
  position: relative;

  video {
    height: auto;
  }

  .previewSubtitle {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    top: 0px;
    left: 0px;
    right: 0px;
    height: 170px;
    padding: 0px;
    color: white;
    z-index: 1;

    .subtitleWrapper {
      height: 125px;
      max-width: 800px;
      min-width: 100%;
      padding: 10px;
      background: rgba(0, 0, 0, 0.3);
      margin-bottom: 20px;
      overflow-y: scroll;
      line-height: 1.5;
      text-align: center;

      span {
        position: relative top 0 transition all 1000ms linear
      }
    }
  }

}