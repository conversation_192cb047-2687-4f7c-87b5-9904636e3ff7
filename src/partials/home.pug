main.home-page
  section.page-segment.main
    .jumbotron
      .text-container
        h1
          b
            span.pink-text Humanizing
            br
            |  Local Government
        .text A video engagement platform for municipalities powered by AI
        form.contact-us.mobile-hidden
          .input-group
            .input-with-label
              input( type="email" name="contactEmail" data-regex="/^ *[^@]+@[^.]+.[a-z]+ *$/i" placeholder="Enter an email" )
              .error.contactEmail.hidden Please enter a valid email
            button( data-function="submitContactForm( event )" ) Request Demo
      .image-container
        img.background-image(src=assetUrl + "/required/svgs/main-background.svg" alt="Background Image" )
        .cover-image
            .cover-video-placeholder
                img.play-button(src="https://files.repd.us/others/PlayButton.png" data-function="playCoverVideo( event )" id="play-button")
                img.cover(src="https://files.repd.us/others/ImageCover.jpg" data-function="playCoverVideo( event )" id="cover-image")
                img.cover2(src="https://files.repd.us/others/ImageCover.jpg" data-function="playCoverVideo( event )" id="cover-image2")
            video(src="https://files.repd.us/others/Cover-v2.mp4", id="cover-video" preload volume="1.0", muted="false" loop, playsinline, controls, speed="1" )

    section.flex-container.text-center
      .column
        img( src=assetUrl + "/required/icons/sound-the-alarm.svg" alt="Sound the Alarm Icon" )
        .subtitle Own the Narrative
        .text Share any short video update with your
          br
          | residents in seconds.
      .column
        img( src=assetUrl + "/required/icons/prevent-misformation.svg" alt="Prevent Misinformation Icon" )
        .subtitle Stop Misinformation
        .text Establish a source-of-truth for key
          br
          | community updates.
      .column
        img( src=assetUrl + "/required/icons/timer.svg" alt="Timer Icon" )
        .subtitle Save Time
        .text Reclaim staff time with automated
          br
          | responses to common questions.

  aside.page-segment.city-logo-scroller( style="background-image: url( " + assetUrl + "/required/city-logos.jpg )" )

  section.page-segment.features
    .wrapper
      .jumbotron
        h1.text-center
          b Product Features

        //- See variables section for features.
        each feature in features
          .flex-container
            .column.video
              img( src=assetUrl + "/required/circle-decoration.png", alt="Decoration" )
              video( src=assetUrl + "/required/videos/" + feature.video, autoplay, preload, volume="0", muted, loop, playsinline, speed="0.75" )
            .column.text
              img( src=assetUrl + "/required/icons/features/" + feature.img, alt=feature.alt )
              .subtitle= feature.subtitle
              .text= feature.text
              a( data-function="scrollToElement()" ) Schedule a Demo
                != '&rarr;'

  section.page-segment.case-studies
    h1.title.text-center
      b Case Studies

    .arrow-container
      img.arrow.left-arrow(  src=assetUrl + "/required/icons/arrow.svg" data-function="switchCaseStudy(-1)" alt="Left Arrow Icon" )
      img.arrow.right-arrow( src=assetUrl + "/required/icons/arrow.svg" data-function="switchCaseStudy(+1)" alt="Right Arrow Icon" )

    //- See variables section for caseStudies.
    section.carousel
      each caseStudy in caseStudies
        .case-study(class=caseStudy.id)
          //- img.study( src=assetUrl + "/required/sections/case-studies/" + caseStudy.img, alt=caseStudy.alt )
          video.study( src=assetUrl + "/required/sections/case-studies/" + caseStudy.video, preload, volume="0", autoplay, muted, loop, playsinline, speed="1", controls )
          .quote-text
            !='&ldquo;'
            =caseStudy.quote
            !='&rdquo;'
          .attribution= caseStudy.attribution
          .stats
            each stat in caseStudy.stats
              .stat
                .number= stat.number
                .text
                  | #{stat.text}
                  //- .subtext= stat.subtext

    aside.decoration.left( style="background-image: url( " + assetUrl + "/required/svgs/city-left.svg )" )
    aside.decoration.right( style="background-image: url( " + assetUrl + "/required/svgs/city-right.svg )" )

  section.page-segment.press
    h1.title.text-center
      b Press

    //- .arrow-container
    //-   img.arrow.left-arrow(  src=assetUrl + "/required/icons/arrow.svg" data-function="switchCaseStudy(-1)" alt="Left Arrow Icon" )
    //-   img.arrow.right-arrow( src=assetUrl + "/required/icons/arrow.svg" data-function="switchCaseStudy(+1)" alt="Right Arrow Icon" )

    //- See variables section for caseStudies.
    section.press-carousel
      .inner-press-wrapper
        - var pressGroups = []
        - for (var i = 0; i < press.length; i += 3) {
        -   pressGroups.push(press.slice(i, i + 3))
        - }
        each group, groupIndex in pressGroups
          .press-article-carousel-item
            each article, articleIndex in group
              .press-article( data-id=(groupIndex * 3 + articleIndex) )
                img.press-article( src=assetUrl + "/required/sections/press/" + article.img, alt=article.alt )
                img.press-article-logo( class=article.logoStyle src=assetUrl + "/required/sections/press/" + article.logo, alt=article.alt )
                p.press-article-text
                  =article.text
                a.press-article-link( href=article.link, target="_blank" ) Read More
      .press-dots
        //- Add a dot for every 3 items in the press array
        each group, groupIndex in pressGroups
          .press-dot( data-function="switchPressArticle( event )" data-id=groupIndex )
      button.carousel-control.prev(onclick="prevPressSlide()") &#10094;
      button.carousel-control.next(onclick="nextPressSlide()") &#10095;

  section.page-segment.about-us
    .wrapper
      .jumbotron
        h1.text-center
          b About Us

        section.flex-container.team
          img.decoration( src=assetUrl + "/required/svgs/decoration.svg" alt="Decoration" )
          .column
            img.team-photo( src=assetUrl + "/required/sections/about/Whiteboarding with Mike and Mark.jpg", alt="Team Photo" )
            .text
              | During the pandemic, brothers Mike and Dave Baumwoll were frustrated by social media's inability to host informative, civil conversations.
              | In response, they built Rep'd: a troll-proof, ultra-efficient, fundamentally human way for government leaders and constituents to connect.
              | Twitter veteran Mark Friese joined Rep'd in 2022 to lead the team's efforts to humanize local government and improve the way communities engage.
              br
              br
              | Rep'd is built on the belief that government/resident trust is built not through blocks of text,
              | but by connecting as people through names and faces.

      h2.text-center Leadership

      section.flex-container.founders
        .column
          .image-wrapper
            img.team-photo( src=assetUrl + "/required/sections/about/Mike.png", alt="Mike" )
            .social-media-wrapper
              .link-group
                a( href="https://www.linkedin.com/in/mikebaums" target="_blank" rel="canonical external dofollow" )
                  img( src=assetUrl + "/required/icons/about-us/linkedin.svg", alt="LinkedIn Icon" )
                a( href="mailto:<EMAIL>" target="_blank" rel="external nofollow" )
                  img( src=assetUrl + "/required/icons/about-us/mail.svg", alt="Mail Icon" )
                a( href="https://twitter.com/baumwoll" target="_blank" rel="canonical external dofollow" class="hidden" )
                  img( src=assetUrl + "/required/icons/about-us/twitter.svg", alt="Twitter Icon" )
          h3 Mike Baumwoll
          h4 CEO
          .text
            | Mike is a seasoned entrepreneur with 15 years of experience in digital marketing,
            | business development, and government technology. After 6.5 years at Twitter,
            | Mike channeled his passion and energy into improving transparency between communities and their government leaders.
            | With two little ones at home, Mike is dedicated to building technology that will make his kids proud.
            | Mike holds a BA from Lafayette College.
        .column
          .image-wrapper
            img.team-photo( src=assetUrl + "/required/sections/about/Mark.png", alt="Mark" )
            .social-media-wrapper
              .link-group
                a( href="https://www.linkedin.com/in/mfriese" target="_blank" rel="canonical external dofollow" )
                  img( src=assetUrl + "/required/icons/about-us/linkedin.svg", alt="LinkedIn Icon" )
                a( href="mailto:<EMAIL>" target="_blank" rel="external nofollow" )
                  img( src=assetUrl + "/required/icons/about-us/mail.svg", alt="Mail Icon" )
                a( href="https://twitter.com/TweetGuyMark" target="_blank" rel="canonical external dofollow" class="hidden"  )
                  img( src=assetUrl + "/required/icons/about-us/twitter.svg", alt="Twitter Icon" )
          h3 Mark Friese
          h4 COO
          .text
            | Mark is an experienced, enthusiastic business leader with a passion for solving meaningful societal problems.
            | A 10-year veteran of Twitter's global advertising team, Mark left a comfortable career in sales to help Rep'd
            | fulfill its mission of improving trust and transparency in communities nationwide.
            | Mark holds a BA from Stanford University and an MBA from UC Berkeley's Haas School of Business.

  section.page-segment.footer-contact-form
    .wrapper
      .jumbotron.text-center
        .title Interested in being a part of the future?
        .text Schedule a quick demo with us.

      section.contact-us-form-group
        form.contact-us
          //- .input-group
          //-   .input-with-label
          //-     label Name
          //-     input( type="text" name="contactName" data-regex="/^ *[a-z]+ *([a-z]+|) *$/i" )
          //-     .error.contactName.hidden Please enter a valid name
          //-   .input-with-label
          //-     label Organization
          //-     input( type="text" name="organization" data-regex="/^ *([a-z0-9\-\_\+\.,]+|) *$/i" )
          //-     .error.contactName.hidden Please enter a org name
          .input-group
            .input-with-label
              //- label Email
              input( type="email" name="contactEmail" data-regex="/^ *[^@]+@[^.]+.[a-z]+ *$/i" )
              .error.contactEmail.hidden Please enter a valid email
            button( data-function="submitContactForm( event )" ) Request Demo
