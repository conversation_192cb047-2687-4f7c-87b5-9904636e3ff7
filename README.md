### Install Global Dependencies

```bash
  npm install pug-cli stylus typescript -g
```

### Create a Build

```bash
  npm run build # Uses build.sh
```

### Deploy to the Server

```bash
  cp ~/.aws/projects/repd ~/.aws/credentials

  # Web
  aws s3 sync ./dist s3://repd.us --region us-east-1 --acl public-read --exclude .DS_Store # --recursive
  aws cloudfront create-invalidation --distribution-id E1OY3SL50TBY4V --paths "/*" --region us-east-1
```

### Deploy Assets to the Server

```bash
  cp ~/.aws/projects/minque ~/.aws/credentials

  aws s3 sync ./dist s3://repd-us-files --region us-east-1 --acl public-read --exclude .DS_Store # --recursive
```
