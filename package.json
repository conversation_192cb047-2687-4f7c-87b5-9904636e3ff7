{"name": "minque-repd", "version": "0.0.4", "description": "Software Dashboard & Platform for <PERSON>'s Political Base", "main": "src/script.ts", "scripts": {"build": "/bin/bash build.sh", "start": "/bin/bash build.sh; serve -s dist", "watch": "/bin/bash build.sh --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["Politics", "<PERSON><PERSON>"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@fortawesome/fontawesome-free": "^5.13.0", "@openfonts/nunito-sans_all": "^1.44.1", "@types/node": "^14.0.5", "animate.css": "^4.1.0", "autoprefixer": "^9.8.0", "gulp": "^4.0.2", "gulp-pug": "^5.0.0", "gulp-rename": "^2.0.0", "jquery": "^3.5.1", "lodash": "^4.17.15", "postcss-cli": "^7.1.1", "pug": "^2.0.4", "stylus": "^0.54.7"}, "dependencies": {"caniuse-lite": "^1.0.30001285"}}