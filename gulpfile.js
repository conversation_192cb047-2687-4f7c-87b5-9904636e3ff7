const gulp = require('gulp');
const pug = require('gulp-pug');
const rename = require('gulp-rename');

const paths = [
  { path: '/', filename: 'index.html' },
  { path: '/emporia', filename: 'emporia.html' }
];

gulp.task('pug', function() {
  return Promise.all(paths.map(({ path, filename }) => {
    return new Promise((resolve, reject) => {
      gulp.src('src/index.pug')
        .pipe(pug({ locals: { path } }))
        .pipe(rename(filename))
        .pipe(gulp.dest('dist'))
        .on('end', resolve)
        .on('error', reject);
    });
  }));
});

gulp.task('default', gulp.series('pug'));

// aws s3 cp ./dist/emporia.html s3://repd.us/emporia --metadata-directive REPLACE --content-type text/html --acl public-read;
